<template>
  <uni-popup
    ref="recordPopup"
    type="bottom"
    background-color="#fff"
    border-radius="10px 10px 0 0"
  >
    <view class="record-popup">
      <view class="popup-header">
        <text class="title">录制音频</text>
        <text class="iconfont icon-close" @click="handleClose"></text>
      </view>

      <view class="record-content">
        <!-- 录音状态 -->
        <template v-if="!previewUrl">
          <view class="timer">{{ recordTime }}</view>
          <view
            class="record-btn"
            :class="{ recording: isRecording }"
            @click="toggleRecord"
          >
            <text
              class="iconfont"
              :class="isRecording ? 'icon-mic-pause' : 'icon-mic'"
            ></text>
          </view>
          <text class="tip">{{ isRecording ? "点击结束" : "点击开始" }}</text>
        </template>

        <!-- 预览状态 -->
        <template v-else>
          <view class="preview-title">录音预览</view>
          <audio-player
            :src="previewUrl"
            :show-close-btn="false"
            :duration="recordDuration"
            :show-delete-btn="false"
            class="preview-player"
          />
          <view class="bottom-buttons">
            <view class="btn btn-secondary" @click="handleReRecord">
              <text class="iconfont icon-refresh"></text>
              <text>重新录制</text>
            </view>
            <view class="btn btn-primary" @click="handleSubmit">
              <text class="iconfont icon-check"></text>
              <text>确认提交</text>
            </view>
          </view>
        </template>
      </view>
    </view>
  </uni-popup>
</template>

<script lang="ts" setup>
import { uploadFile } from "@/api/file";
import AudioPlayer from "@/components/audio-player/index.vue";
import { FileInfo, FileUploadOptions } from "@/types/file";
import { uploadObjectLog } from "@/utils/log/uploader";
import { nextTick, onMounted, onUnmounted, ref } from "vue";

const emit = defineEmits<{
  (e: "success", fileInfo: FileInfo): void;
  (e: "close"): void;
}>();

const recordPopup = ref<any>(null);
const isRecording = ref(false);
const recordTime = ref("00:00");
const recorderManager = uni.getRecorderManager();
const recordTimer = ref<NodeJS.Timeout | null>(null);
const recordStartTime = ref<number>(0);
const previewUrl = ref<string>("");
const isUploading = ref(false);
const recordDuration = ref(0);

// 打开弹窗
const open = async () => {
  await nextTick();
  setTimeout(() => {
    if (recordPopup.value) {
      recordPopup.value.open();
    }
  }, 100);
};

// 关闭弹窗
const handleClose = async () => {
  if (previewUrl.value && !isUploading.value) {
    uni.showModal({
      title: "提示",
      content: "录音尚未提交，确定要关闭吗？",
      success: (res) => {
        if (res.confirm) {
          closePanel();
        }
      },
    });
  } else {
    closePanel();
  }
};

// 格式化时间
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${String(minutes).padStart(2, "0")}:${String(
    remainingSeconds
  ).padStart(2, "0")}`;
};

// 切换录音状态
const toggleRecord = () => {
  if (isRecording.value) {
    stopRecord();
  } else {
    startRecord();
  }
};

// 开始录音
const startRecord = () => {
  isRecording.value = true;
  recordStartTime.value = Date.now();

  recordTimer.value = setInterval(() => {
    const elapsed = Math.floor((Date.now() - recordStartTime.value) / 1000);
    recordTime.value = formatTime(elapsed);
  }, 1000);

  recorderManager.start({
    duration: 600000,
    sampleRate: 16000,
    numberOfChannels: 1,
    encodeBitRate: 64000,
    format: "mp3",
  });
};

// 停止录音
const stopRecord = () => {
  isRecording.value = false;

  if (recordTimer.value) {
    clearInterval(recordTimer.value);
    recordTimer.value = null;
  }
  recordTime.value = "00:00";

  recorderManager.stop();
};

// 处理重新录制
const handleReRecord = () => {
  previewUrl.value = "";
  recordTime.value = "00:00";
};

// 处理提交
const handleSubmit = async () => {
  if (!previewUrl.value || isUploading.value) return;

  try {
    isUploading.value = true;
    uni.showLoading({ title: "上传中..." });

    const fileOptions: FileUploadOptions = {
      tempFilePath: previewUrl.value,
      name: `audio_${Date.now()}.mp3`,
    };

    const result = await uploadFile(fileOptions);

    if (result.isSuccess()) {
      const fileInfo = result.getData();
      emit("success", fileInfo);
      handleClose();
    } else {
      throw new Error(result.msg);
    }
  } catch (error: any) {
    uploadObjectLog("录音面板上传失败", {
      ...error,
    });
    uni.showToast({
      title: error.message || "上传失败",
      icon: "none",
    });
  } finally {
    isUploading.value = false;
    uni.hideLoading();
  }
};

// 实际关闭面板的函数
const closePanel = () => {
  if (recordTimer.value) {
    clearInterval(recordTimer.value);
    recordTimer.value = null;
  }
  recordTime.value = "00:00";
  previewUrl.value = "";
  if (recordPopup.value) {
    recordPopup.value.close();
  }
  emit("close");
};

// 初始化录音管理器
const initRecorder = () => {
  recorderManager.onStop(async (res) => {
    const { tempFilePath, duration } = res;
    previewUrl.value = tempFilePath;
    recordDuration.value = duration / 1000; // 将毫秒转换为秒
  });

  recorderManager.onError((error) => {
    console.error(
      "录音错误:",
      error,
      "错误代码:",
      error.errCode,
      "错误信息:",
      error.errMsg
    );
    // 使用新方法上报错误对象
    uploadObjectLog("录音面板录音错误", {
      ...error,
      recordTime: recordTime.value,
      isRecording: isRecording.value,
      recordDuration: recordDuration.value,
    });
    uni.showToast({
      title: "录音失败",
      icon: "none",
    });
  });
};

onMounted(() => {
  initRecorder();
});

onUnmounted(() => {
  if (recordTimer.value) {
    clearInterval(recordTimer.value);
    recordTimer.value = null;
  }
});

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.record-popup {
  width: 100%;
  min-height: 40vh;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  box-sizing: border-box;

  .popup-header {
    padding: 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2rpx solid #f5f5f5;

    .title {
      font-size: 32rpx;
      color: #2b2b2b;
      font-weight: 600;
    }

    .icon-close {
      font-size: 40rpx;
      color: #666;
    }
  }

  .record-content {
    padding: 48rpx 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 32rpx;

    .timer {
      font-size: 48rpx;
      color: #2b2b2b;
      font-weight: 600;
    }

    .record-btn {
      width: 120rpx;
      height: 120rpx;
      background: #ffd600;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &.recording {
        animation: pulse 1.5s infinite;
      }

      .iconfont {
        font-size: 48rpx;
        color: #2b2b2b;
      }
    }

    .tip {
      font-size: 28rpx;
      color: #666;
    }

    .preview-title {
      font-size: 32rpx;
      color: #2b2b2b;
      font-weight: 600;
      margin-bottom: 16rpx;
    }

    .preview-player {
      width: 100%;
      margin: 16rpx 0 32rpx;
    }

    .bottom-buttons {
      width: 100%;
      display: flex;
      gap: 24rpx;

      .btn {
        flex: 1;
        height: 80rpx;
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;

        .iconfont {
          font-size: 32rpx;
        }

        text:not(.iconfont) {
          font-size: 28rpx;
        }

        &.btn-primary {
          background: #ffd600;
          color: #2b2b2b;
          font-weight: 600;
        }

        &.btn-secondary {
          background: #f5f5f5;
          color: #666;
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
