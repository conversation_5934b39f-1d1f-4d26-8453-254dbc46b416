<template>
  <view v-if="visible" class="custom-image-preview">
    <view class="preview-mask" @click="handleClose"></view>

    <!-- 单张图片预览容器 -->
    <view class="preview-container">
      <movable-area class="zoom-area">
        <movable-view
          class="zoom-view"
          :scale="true"
          :scale-min="1"
          :scale-max="3"
          :scale-value="scaleValue"
          :animation="false"
          :damping="20"
          @scale="handleScale"
          @change="handleMoveChange"
          direction="all"
          :x="movableX"
          :y="movableY"
          @touchstart="handleMovableTouchStart"
          @touchend="handleMovableTouchEnd"
          :friction="2"
          inertia
          out-of-bounds
        >
          <image
            :src="currentImage"
            mode="aspectFit"
            class="preview-image"
            @error="handleImageError"
            @load="handleImageLoad"
            @click.stop="handleImageClick"
            :style="{ transform: `scale(${scaleValue})` }"
            :lazy-load="false"
          />
        </movable-view>
      </movable-area>
    </view>

    <!-- 切换控制按钮 -->
    <view class="nav-buttons" v-if="images.length > 1">
      <view class="nav-button-container left">
        <view
          class="nav-button prev"
          @click="handlePrev"
          v-if="currentIndex > 0"
        >
          <view class="nav-icon">←</view>
        </view>
      </view>
      <view class="nav-button-container right">
        <view
          class="nav-button next"
          @click="handleNext"
          v-if="currentIndex < images.length - 1"
        >
          <view class="nav-icon">→</view>
        </view>
      </view>
    </view>

    <view class="preview-counter"
      >{{ currentIndex + 1 }}/{{ images.length }}</view
    >
    <view class="close-btn" @click="handleClose">×</view>

    <!-- 缩放提示 -->
    <view class="zoom-tip" v-if="showZoomTip">
      <view class="zoom-tip-text">双指缩放</view>
    </view>
  </view>
</template>

<script lang="ts">
import { computed, defineComponent, nextTick, ref, watch } from "vue";

export default defineComponent({
  name: "ImagePreview",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    urls: {
      type: Array as () => string[],
      default: () => [],
    },
    current: {
      type: Number,
      default: 0,
    },
  },
  emits: ["close", "change"],
  setup(props, { emit }) {
    console.log("ImagePreview setup 初始化", props.visible, props.urls);

    const currentIndex = ref(props.current);
    const scaleValue = ref(1);
    const isZooming = ref(false);
    const showZoomTip = ref(false);
    const imageLoaded = ref(false);
    const movableX = ref(0);
    const movableY = ref(0);
    const imageSize = ref({ width: 0, height: 0 });
    const viewportSize = ref({ width: 0, height: 0 });

    let scaleAnimationTimer: any = null;
    let touchAnimationFrame: number | null = null;
    let isDragging = ref(false);
    let movableTouchStartX = ref(0);
    let movableTouchStartTime = ref(0);
    let movableTouchStartY = ref(0);

    // 当前显示的图片URL
    const currentImage = computed(() => {
      if (images.value.length === 0) return "";
      return images.value[currentIndex.value];
    });

    const initViewportSize = () => {
      try {
        const systemInfo = uni.getSystemInfoSync();
        viewportSize.value = {
          width: systemInfo.windowWidth,
          height: systemInfo.windowHeight,
        };
        console.log("视口大小：", viewportSize.value);
      } catch (e) {
        console.error("获取视口大小失败", e);
      }
    };

    initViewportSize();

    const images = computed(() => {
      console.log("计算图片列表", props.urls);
      return props.urls;
    });

    watch(
      () => props.current,
      (newVal) => {
        console.log("current 变化", newVal);
        if (currentIndex.value !== newVal) {
          currentIndex.value = newVal;
          resetViewState(); // 确保切换时重置状态
        }
      }
    );

    watch(
      () => props.visible,
      (newVal) => {
        console.log("visible 变化", newVal);
        if (newVal) {
          // 确保在显示时基于传入的current初始化
          currentIndex.value = props.current;
          resetViewState();
          showZoomTip.value = true;
          setTimeout(() => {
            showZoomTip.value = false;
          }, 2000);
        } else {
          // 关闭时也重置状态
          resetViewState();
        }
      }
    );

    const resetViewState = () => {
      scaleValue.value = 1;
      isZooming.value = false;
      imageLoaded.value = false;
      isDragging.value = false;

      // 取消任何可能正在进行的动画
      if (touchAnimationFrame) {
        cancelAnimationFrame(touchAnimationFrame);
        touchAnimationFrame = null;
      }

      // 重置 movable-view 位置需要延迟确保渲染完成
      nextTick(() => {
        movableX.value = 0;
        movableY.value = 0;
      });
      console.log("视图状态已重置");
    };

    const handleScale = (e: any) => {
      // 使用 requestAnimationFrame 优化性能
      if (touchAnimationFrame) {
        cancelAnimationFrame(touchAnimationFrame);
      }

      touchAnimationFrame = requestAnimationFrame(() => {
        const newScale = e.detail.scale;
        isZooming.value = true;

        if (scaleAnimationTimer) clearTimeout(scaleAnimationTimer);
        scaleAnimationTimer = setTimeout(() => {
          isZooming.value = false;
          if (newScale <= 1) {
            resetMovablePosition();
          }
        }, 300); // 增加延迟时间，确保缩放状态能够正确维持

        scaleValue.value = newScale;
      });
    };

    const handleMoveChange = (e: any) => {
      // 使用 requestAnimationFrame 优化性能
      if (touchAnimationFrame) {
        cancelAnimationFrame(touchAnimationFrame);
      }

      touchAnimationFrame = requestAnimationFrame(() => {
        if (e.detail.source === "touch" && imageLoaded.value) {
          movableX.value = e.detail.x;
          movableY.value = e.detail.y;
          isDragging.value = true;
        } else if (e.detail.source === "" && !isZooming.value) {
          // 惯性滚动结束
          if (scaleValue.value <= 1) {
            resetMovablePosition();
          }
        }
      });
    };

    const resetMovablePosition = () => {
      nextTick(() => {
        movableX.value = 0;
        movableY.value = 0;
      });
    };

    const handleImageLoad = (e: any) => {
      imageLoaded.value = true;
      imageSize.value = {
        width: e.detail.width || viewportSize.value.width,
        height: e.detail.height || viewportSize.value.height,
      };
      resetMovablePosition();
    };

    const handleMovableTouchStart = (e: any) => {
      // 记录触摸点数量，用于区分单指点击和多指操作
      const touchCount = e.touches.length;
      if (touchCount > 1) {
        // 多指触摸，标记为缩放操作
        isZooming.value = true;
        return;
      }

      movableTouchStartTime.value = Date.now();
      movableTouchStartX.value = e.touches[0].clientX;
      // 记录Y轴位置，用于更准确判断是否有移动
      movableTouchStartY.value = e.touches[0].clientY;
    };

    const handleMovableTouchEnd = (e: any) => {
      // 如果是缩放操作，阻止关闭预览
      if (isZooming.value) {
        // 延迟重置缩放状态，防止误触
        setTimeout(() => {
          isZooming.value = false;
        }, 100);
        return;
      }

      const tapDuration = Date.now() - movableTouchStartTime.value;
      const touchEndX = e.changedTouches[0].clientX;
      const touchEndY = e.changedTouches[0].clientY;
      const deltaX = touchEndX - movableTouchStartX.value;
      const deltaY = touchEndY - movableTouchStartY.value;

      // 计算总移动距离，更精确地判断是否为点击
      const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // 判断为单击（短时间内触摸且几乎没有移动）
      if (
        tapDuration < 300 &&
        moveDistance < 10 &&
        !isDragging.value &&
        !isZooming.value &&
        e.changedTouches.length === 1
      ) {
        console.log("图片单击，关闭预览");
        handleImageClick();
      }

      // 用requestAnimationFrame优化释放拖动状态
      if (isDragging.value) {
        if (touchAnimationFrame) {
          cancelAnimationFrame(touchAnimationFrame);
        }

        touchAnimationFrame = requestAnimationFrame(() => {
          isDragging.value = false;
          touchAnimationFrame = null;
        });
      }
    };

    const handleImageClick = () => {
      if (!isDragging.value && !isZooming.value) {
        console.log("图片单击，关闭预览");
        handleClose();
      }
    };

    const handleImageError = () => {
      console.log(`图片加载失败: ${currentIndex.value}`);
      imageLoaded.value = true; // 即使加载失败也标记为加载完成，避免UI卡住
    };

    const handleClose = () => {
      console.log("关闭预览触发");
      emit("close");
    };

    // 切换到上一张图片
    const handlePrev = () => {
      if (currentIndex.value > 0) {
        currentIndex.value--;
        resetViewState();
        emit("change", currentIndex.value);
      }
    };

    // 切换到下一张图片
    const handleNext = () => {
      if (currentIndex.value < images.value.length - 1) {
        currentIndex.value++;
        resetViewState();
        emit("change", currentIndex.value);
      }
    };

    return {
      currentIndex,
      currentImage,
      scaleValue,
      isZooming,
      images,
      showZoomTip,
      movableX,
      movableY,
      handleScale,
      handleMoveChange,
      handleImageLoad,
      handleImageError,
      handleClose,
      handleMovableTouchStart,
      handleMovableTouchEnd,
      handleImageClick,
      handlePrev,
      handleNext,
    };
  },
});
</script>

<style lang="scss" scoped>
.custom-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  display: flex;
  flex-direction: column;
  background-color: #000;

  .preview-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .preview-container {
    width: 100%;
    height: 100%;
    z-index: 2;

    .zoom-area {
      width: 100%;
      height: 100%;
      overflow: hidden;

      .zoom-view {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        will-change: transform; /* 优化渲染性能 */

        .preview-image {
          width: 100%;
          height: 100%;
          will-change: transform; /* 优化渲染性能 */
        }
      }
    }
  }

  .nav-buttons {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    z-index: 3;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
    pointer-events: none; /* 避免干扰图片触摸 */

    .nav-button-container {
      &.left {
        margin-left: 30rpx;
      }

      &.right {
        margin-right: 30rpx;
      }
    }

    .nav-button {
      width: 80rpx;
      height: 80rpx;
      background-color: rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      pointer-events: auto; /* 恢复按钮的点击能力 */

      .nav-icon {
        color: #fff;
        font-size: 36rpx;
      }
    }
  }

  .preview-counter {
    position: absolute;
    bottom: 80rpx;
    left: 0;
    right: 0;
    text-align: center;
    color: #fff;
    font-size: 28rpx;
    z-index: 3;
  }

  .close-btn {
    position: absolute;
    top: var(--status-bar-height, 40rpx);
    right: 30rpx;
    width: 70rpx;
    height: 70rpx;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
  }

  .zoom-tip {
    position: absolute;
    bottom: 150rpx;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 20rpx;
    padding: 10rpx 20rpx;
    z-index: 3;
    animation: fadeOut 2s forwards;

    .zoom-tip-text {
      color: #fff;
      font-size: 24rpx;
    }
  }

  @keyframes fadeOut {
    0% {
      opacity: 1;
    }
    70% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
}
</style>
