<template>
  <view class="audio-player">
    <!-- 播放控制按钮 -->
    <view class="play-btn" @click="togglePlay">
      <view class="play-circle" :class="{ 'is-playing': isPlaying }">
        <text
          class="iconfont"
          :class="isPlaying ? 'icon-pause' : 'icon-play'"
        ></text>
      </view>
    </view>

    <!-- 时间和进度条容器 -->
    <view class="progress-container">
      <text class="time">{{ currentTime }} / {{ duration }}</text>
      <slider
        class="progress-bar"
        :value="progress"
        @change="onProgressChange"
        @changing="onProgressChanging"
        activeColor="#FFD600"
        backgroundColor="#EEEEEE"
        block-color="#FFD600"
        block-size="12"
      />
    </view>

    <!-- 删除按钮 -->
    <view v-if="showDeleteBtn" class="delete-btn" @click="handleDelete">
      <text class="iconfont icon-B-11"></text>
    </view>
  </view>
</template>

<script lang="ts">
import { uploadObjectLog } from "@/utils/log/uploader";
import {
  defineComponent,
  onBeforeUnmount,
  onMounted,
  ref,
  shallowRef,
  watchEffect,
} from "vue";

export default defineComponent({
  name: "AudioPlayer",
  props: {
    src: {
      type: String,
      required: true,
    },
    showDeleteBtn: {
      type: Boolean,
      default: true,
    },
    duration: {
      type: [Number, String],
      default: 0,
    },
  },
  emits: ["delete"],
  setup(props, { emit }) {
    const isPlaying = ref(false);
    const progress = ref(0);
    const currentTime = ref("00:00");
    const durationDisplay = ref("00:00");
    // 使用shallowRef存储音频实例
    const audioContext = shallowRef<any>(null);

    const formatTime = (seconds: number) => {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${String(minutes).padStart(2, "0")}:${String(
        remainingSeconds
      ).padStart(2, "0")}`;
    };

    // 初始化时长显示
    watchEffect(() => {
      if (props.duration) {
        const durationInSeconds =
          typeof props.duration === "string"
            ? parseInt(props.duration)
            : props.duration;
        durationDisplay.value = formatTime(durationInSeconds);
      }
    });

    const initAudioContext = () => {
      // 如果已经存在实例，先销毁
      if (audioContext.value) {
        audioContext.value.destroy();
      }

      // 检查src是否有效
      if (!props.src) {
        console.error("音频地址无效");
        return;
      }

      console.log("初始化音频上下文，音频地址:", props.src);
      try {
        const innerAudioContext = uni.createInnerAudioContext();
        audioContext.value = innerAudioContext;

        console.log("创建音频实例:", innerAudioContext ? "成功" : "失败");

        // 预检查音频URL是否有效
        if (!props.src.startsWith("http") && !props.src.startsWith("wxfile")) {
          console.warn("音频地址可能无效，当前地址:", props.src);
        }

        innerAudioContext.src = props.src;
        innerAudioContext.autoplay = false;
        innerAudioContext.obeyMuteSwitch = false;

        console.log("音频实例详细信息:", {
          src: innerAudioContext.src,
          duration: innerAudioContext.duration,
          autoplay: innerAudioContext.autoplay,
          paused: innerAudioContext.paused,
          volume: innerAudioContext.volume,
          startTime: innerAudioContext.startTime,
          buffered: innerAudioContext.buffered,
        });

        innerAudioContext.onWaiting(() => {
          console.log("音频加载中...", new Date().toISOString());
        });

        innerAudioContext.onCanplay(() => {
          console.log("音频可以播放，详细信息:", {
            时间戳: new Date().toISOString(),
            duration: innerAudioContext.duration,
            currentTime: innerAudioContext.currentTime,
            paused: innerAudioContext.paused,
            buffered: innerAudioContext.buffered,
            volume: innerAudioContext.volume,
          });
          // 如果没有外部传入duration，才使用音频文件的实际时长
          if (!props.duration) {
            durationDisplay.value = formatTime(innerAudioContext.duration);
          }
        });

        innerAudioContext.onPlay(() => {
          isPlaying.value = true;
          console.log("音频开始播放回调，当前状态:", {
            currentTime: innerAudioContext.currentTime,
            duration: innerAudioContext.duration,
            paused: innerAudioContext.paused,
            buffered: innerAudioContext.buffered,
            isPlaying: isPlaying.value,
          });
        });

        innerAudioContext.onPause(() => {
          isPlaying.value = false;
          console.log("音频暂停播放回调，当前状态:", {
            currentTime: innerAudioContext.currentTime,
            paused: innerAudioContext.paused,
            isPlaying: isPlaying.value,
          });
        });

        innerAudioContext.onTimeUpdate(() => {
          const curTime = innerAudioContext.currentTime;
          const dur =
            Number(props.duration) || innerAudioContext.duration || 0.001;

          progress.value = dur > 0 ? (curTime / dur) * 100 : 0;
          currentTime.value = formatTime(curTime);
        });

        innerAudioContext.onEnded(() => {
          console.log("音频播放结束");
          isPlaying.value = false;
          progress.value = 0;
          currentTime.value = "00:00";
        });

        // 增强错误处理
        innerAudioContext.onError((err) => {
          console.error(
            "音频播放错误, 详细信息:",
            err,
            "错误代码:",
            err.errCode,
            "错误信息:",
            err.errMsg,
            "音频地址:",
            props.src
          );
          // 使用新方法上报错误对象
          uploadObjectLog("002组件音频播放器错误", {
            ...err,
            audioSrc: props.src,
            duration: props.duration,
            playerState: {
              isPlaying: isPlaying.value,
              progress: progress.value,
              currentTime: currentTime.value,
            },
          });
          isPlaying.value = false;

          let errorMsg = err.errMsg || "未知错误";

          // 特定错误码处理
          if (err.errCode === 62) {
            errorMsg = "加载音频失败，请检查网络连接或音频地址是否正确";
            // 尝试重新加载音频
            setTimeout(() => {
              console.log("尝试重新加载音频...");
              if (innerAudioContext) {
                innerAudioContext.stop();
                innerAudioContext.src = props.src;
              }
            }, 1000);
          }

          uni.showToast({
            title: `音频播放失败: ${errorMsg}`,
            icon: "none",
            duration: 2000,
          });
        });
      } catch (error: any) {
        uploadObjectLog("Error initializing audio context:", {
          ...error,
        });
        console.error("创建音频上下文失败:", error);
        uni.showToast({
          title: "音频初始化失败，请重试",
          icon: "none",
        });
      }
    };

    const togglePlay = () => {
      const innerAudioContext = audioContext.value;
      if (!innerAudioContext) {
        console.error("音频实例未初始化");
        return;
      }

      console.log("点击播放/暂停按钮，详细信息:", {
        isPlaying: isPlaying.value,
        currentTime: innerAudioContext.currentTime,
        duration: innerAudioContext.duration,
        paused: innerAudioContext.paused,
        src: innerAudioContext.src,
      });

      try {
        if (innerAudioContext.paused) {
          console.log("执行播放操作");
          if (!innerAudioContext.duration) {
            console.warn("警告：音频可能未正确加载，duration为0");
          }

          // 如果是第一次播放或者播放完毕，重新设置播放位置
          if (
            innerAudioContext.currentTime === 0 ||
            innerAudioContext.currentTime >= innerAudioContext.duration
          ) {
            innerAudioContext.seek(0);
          }

          innerAudioContext.play();
        } else {
          console.log("执行暂停操作");
          innerAudioContext.pause();
        }
      } catch (err: any) {
        uploadObjectLog("Error in togglePlay:", {
          ...err,
        });
        console.error("播放控制出错:", err);
        isPlaying.value = false;
      }
    };

    const onProgressChange = (e: any) => {
      const innerAudioContext = audioContext.value;
      if (!innerAudioContext) return;

      const value = e.detail.value;
      const targetTime = (innerAudioContext.duration * value) / 100;
      console.log("进度条改变，跳转到:", formatTime(targetTime));
      innerAudioContext.seek(targetTime);
    };

    const onProgressChanging = (e: any) => {
      const innerAudioContext = audioContext.value;
      if (!innerAudioContext) return;

      const value = e.detail.value;
      currentTime.value = formatTime(
        (innerAudioContext.duration * value) / 100
      );
    };

    const handleDelete = () => {
      uni.showModal({
        title: "提示",
        content: "确定要删除这条录音吗？",
        success: (res) => {
          if (res.confirm) {
            if (audioContext.value) {
              audioContext.value.stop();
            }
            emit("delete");
          }
        },
      });
    };

    onMounted(() => {
      // 延长初始化时间，确保组件完全挂载
      setTimeout(() => {
        initAudioContext();
      }, 300); // 增加延迟时间
    });

    onBeforeUnmount(() => {
      if (audioContext.value) {
        audioContext.value.destroy();
        audioContext.value = null;
      }
    });

    return {
      isPlaying,
      progress,
      currentTime,
      duration: durationDisplay,
      togglePlay,
      onProgressChange,
      onProgressChanging,
      handleDelete,
    };
  },
});
</script>

<style lang="scss" scoped>
.audio-player {
  position: relative;
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  gap: 24rpx;

  .play-btn {
    width: 48rpx;
    height: 48rpx;
    flex-shrink: 0;

    .play-circle {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: #ffd600;
      display: flex;
      align-items: center;
      justify-content: center;

      .iconfont {
        font-size: 28rpx;
      }
    }
  }

  .progress-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 32rpx;

    .progress-bar {
      flex: 1;
      margin: 0;
    }

    .time {
      font-size: 24rpx;
      color: #666666;
      white-space: nowrap;
      min-width: 120rpx;
      text-align: right;
    }
  }

  .delete-btn {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .iconfont {
      font-size: 28rpx;
      color: #999999;
    }
  }
}
</style>
