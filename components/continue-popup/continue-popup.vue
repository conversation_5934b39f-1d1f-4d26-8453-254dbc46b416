<template>
  <uni-popup ref="popup" type="center">
    <view class="continue-popup">
      <view class="continue-title">{{ title }}</view>
      <view class="continue-content">{{ content }}</view>
      <view class="continue-actions">
        <button class="restart-btn" @click="handleRestart">重新开始</button>
        <button class="continue-btn" @click="handleContinue">
          {{ continueText }}
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const props = withDefaults(
  defineProps<{
    title?: string;
    content: string;
    continueText?: string;
  }>(),
  {
    title: "继续学习",
    continueText: "继续学习",
  }
);

const emit = defineEmits<{
  (e: "restart"): void;
  (e: "continue"): void;
}>();

const popup = ref();

const open = () => {
  popup.value.open();
};

const close = () => {
  popup.value.close();
};

const handleRestart = () => {
  emit("restart");
  close();
};

const handleContinue = () => {
  emit("continue");
  close();
};

// 暴露方法给父组件
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.continue-popup {
  width: 560rpx;
  background-color: #fff;
  border-radius: 32rpx;
  padding: 32rpx;
  overflow: hidden;

  .continue-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 24rpx;
  }

  .continue-content {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 48rpx;
  }

  .continue-actions {
    display: flex;
    justify-content: space-between;
    gap: 24rpx;
    margin: 0 12rpx;

    .restart-btn,
    .continue-btn {
      flex: 1;
      height: 72rpx;
      border-radius: 36rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      margin: 0;
      padding: 0;
      border: 0 none;
      background: none;
      outline: none;
      position: relative;
      overflow: visible;

      &:active {
        transform: scale(0.95);
        opacity: 0.9;
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: none;
        transform: none;
        border-radius: inherit;
        pointer-events: none;
      }
    }

    .restart-btn {
      background-color: #f5f5f5;
      color: #666;
    }

    .continue-btn {
      background-color: #ffd600;
      color: #333;
      font-weight: 500;
    }
  }
}
</style>
