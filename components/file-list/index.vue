<template>
  <view class="file-list" v-if="files && files.length">
    <view v-if="title" class="title">{{ title }}</view>
    <view class="list">
      <view
        class="file-item"
        v-for="(item, index) in files"
        :key="index"
        @click="handleFileClick(item)"
      >
        <text class="iconfont icon" :class="getFileIcon(item)"></text>
        <text class="text">{{ item.name }}</text>
        <view class="meta">
          <text v-if="item.size" class="size">{{
            formatFileSize(item.size)
          }}</text>
        </view>
        <view class="actions">
          <text
            v-if="deletable"
            class="iconfont icon-B-11 delete"
            @click.stop="handleDelete(item, index)"
          ></text>
          <text class="iconfont icon-icon_arrow_right arrow"></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { VERSION } from "@/config";
import { UploadFileInfo } from "@/types/file";
import { defineComponent, PropType } from "vue";

export default defineComponent({
  name: "FileList",
  props: {
    title: {
      type: String,
      default: "",
    },
    files: {
      type: Array as PropType<Array<UploadFileInfo>>,
      required: true,
    },
    deletable: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["delete"],
  setup(props, { emit }) {
    // 获取文件图标
    const getFileIcon = (file: UploadFileInfo) => {
      const ext = file.name.substring(file.name.lastIndexOf(".")).toLowerCase();

      // 根据文件类型返回对应的图标类名
      if ([".jpg", ".jpeg", ".png", ".gif", ".webp"].includes(ext)) {
        return "icon-image";
      }
      if ([".mp4", ".avi", ".mov", ".wmv", ".flv"].includes(ext)) {
        return "icon-video";
      }
      if ([".mp3", ".wav", ".aac", ".ogg"].includes(ext)) {
        return "icon-audio";
      }
      if ([".doc", ".docx"].includes(ext)) {
        return "icon-word";
      }
      return "icon-pdf";
    };

    // 获取文件类型
    const getFileType = (url: string) => {
      const ext = url.substring(url.lastIndexOf(".")).toLowerCase();

      if ([".jpg", ".jpeg", ".png", ".gif", ".webp"].includes(ext)) {
        return "image";
      }
      if ([".mp4", ".avi", ".mov", ".wmv", ".flv"].includes(ext)) {
        return "video";
      }
      if ([".mp3", ".wav", ".aac", ".ogg"].includes(ext)) {
        return "audio";
      }
      return "document";
    };

    // 格式化文件大小
    const formatFileSize = (size: number) => {
      if (size < 1024) {
        return size + "B";
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + "KB";
      } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(2) + "MB";
      } else {
        return (size / (1024 * 1024 * 1024)).toFixed(2) + "GB";
      }
    };

    // 处理文件点击
    const handleFileClick = (item: UploadFileInfo) => {
      if (!item?.url) {
        uni.showToast({
          title: "文件地址无效",
          icon: "none",
        });
        return;
      }

      const fileType = getFileType(item.url);

      // 检查视频播放限制
      if (fileType === "video") {
        // 从storage获取配置
        const confData = uni.getStorageSync("homework_conf");
        if (
          confData &&
          confData.vcount &&
          confData.vcount.toString() === VERSION
        ) {
          return; // 如果vcount和VERSION相等，不执行任何操作
        }
      }

      switch (fileType) {
        case "image":
          uni.previewImage({
            urls: [item.url],
            fail: (err) => {
              console.error("图片预览失败：", err);
              uni.showToast({
                title: "图片预览失败",
                icon: "none",
              });
            },
          });
          break;
        case "video":
          uni.previewMedia({
            sources: [
              {
                url: item.url,
                type: "video",
              },
            ],
          });
          break;
        case "audio":
          uni.navigateTo({
            url: `/common-package/audio-player/index?url=${encodeURIComponent(
              item.url
            )}&title=${encodeURIComponent(item.name)}${
              item.duration ? `&duration=${item.duration}` : ""
            }`,
            fail: (err) => {
              console.error("音频播放器跳转失败：", err);
              uni.showToast({
                title: "音频播放器跳转失败",
                icon: "none",
              });
            },
          });
          break;
        default:
          uni.downloadFile({
            url: item.url,
            success: (res) => {
              if (res.statusCode === 200) {
                uni.openDocument({
                  filePath: res.tempFilePath,
                  showMenu: true,
                  success: () => {
                    console.log("文档打开成功");
                  },
                  fail: (error) => {
                    uni.showToast({
                      title: "无法打开此类型文件",
                      icon: "none",
                    });
                    console.error("打开文档失败", error);
                  },
                });
              }
            },
            fail: () => {
              uni.showToast({
                title: "文件下载失败",
                icon: "none",
              });
            },
          });
      }
    };

    // 处理删除
    const handleDelete = (
      item: { name: string; url: string },
      index: number
    ) => {
      uni.showModal({
        title: "确认删除",
        content: "确定要删除这个文件吗？",
        success: (res) => {
          if (res.confirm) {
            emit("delete", { item, index });
          }
        },
      });
    };

    return {
      getFileIcon,
      formatFileSize,
      handleFileClick,
      handleDelete,
      getFileType,
    };
  },
});
</script>

<style lang="scss" scoped>
.file-list {
  margin-top: 28rpx;

  .title {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
    font-weight: 500;
    display: flex;
    align-items: center;

    &::before {
      content: "";
      width: 4rpx;
      height: 24rpx;
      background: #f7e96f;
      margin-right: 12rpx;
      border-radius: 2rpx;
    }
  }

  .list {
    .file-item {
      display: flex;
      align-items: center;
      background: #f8f8f8;
      border-radius: 8rpx;
      padding: 26rpx 32rpx;
      margin-bottom: 16rpx;
      cursor: pointer;

      .icon {
        font-size: 40rpx;
        margin-right: 12rpx;
        flex-shrink: 0;

        // 不同文件类型的图标颜色
        &.icon-image {
          color: #36d1ab; // 图片文件使用青色
        }

        &.icon-video {
          color: #5c8eff; // 视频文件使用蓝色
        }

        &.icon-audio {
          color: #ffd736; // 音频文件使用橙色
        }

        &.icon-word {
          color: #9d7be1; // Word文档使用紫色
        }

        &.icon-pdf {
          color: #ff7676; // PDF文件使用红色
        }
      }

      .text {
        flex: 1;
        font-size: 28rpx;
        color: #000;
        line-height: 40rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .meta {
        margin-right: 12rpx;
        flex-shrink: 0;

        .size {
          font-size: 24rpx;
          color: #999;
        }
      }

      .actions {
        display: flex;
        align-items: center;
        gap: 16rpx;
        flex-shrink: 0;

        .delete {
          font-size: 32rpx;
          padding: 8rpx;
          color: #999;
          background: rgba(0, 0, 0, 0.05);
          border-radius: 50%;
        }

        .arrow {
          font-size: 32rpx;
          color: #999;
        }
      }
    }
  }
}
</style>
