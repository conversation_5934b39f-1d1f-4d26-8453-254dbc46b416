<template>
  <view class="homework-submit">
    <!-- 主弹窗 -->
    <uni-popup
      ref="popup"
      type="bottom"
      background-color="#fff"
      border-radius="10px 10px 0 0"
      @change="handlePopupChange"
      :mask-click="true"
    >
      <view class="submit-popup">
        <!-- 标题栏 -->
        <view class="popup-header">
          <view class="header-placeholder"></view>
          <text class="title">提交作业</text>
          <text class="iconfont icon-close" @click="handleClose"></text>
        </view>

        <view class="content">
          <!-- 输入框 -->
          <textarea
            class="remark"
            v-model="remark"
            placeholder="写备注和说明，如没有可不填"
            placeholder-style="font-size:28rpx;color:#999;line-height:40rpx;"
            :maxlength="200"
            show-confirm-bar="false"
            v-if="showTextarea"
          />

          <!-- 媒体文件列表 -->
          <view class="media-list">
            <!-- 媒体预览组件 -->
            <media-preview
              v-if="mediaPreviewList.length > 0"
              :mediaList="mediaPreviewList"
              :itemWidth="160"
              :itemHeight="160"
              :gap="16"
              :showDelete="true"
              @delete="handleMediaDelete"
            />

            <!-- 音频列表 -->
            <view class="audio-list" v-if="recordList.length > 0">
              <view
                class="audio-item"
                v-for="(item, index) in recordList"
                :key="index"
              >
                <audio-player
                  :src="item.url"
                  :duration="item.duration || 0"
                  @delete="() => handleDeleteAudio(index)"
                />
              </view>
            </view>
          </view>

          <!-- 上传来源按钮 -->
          <view class="upload-methods">
            <template v-if="!mediaTypes.length || mediaTypes.includes('image')">
              <view class="method-btn" @click="handleTakePhoto">
                <text class="iconfont icon-evol_camare"></text>
                <text>拍照</text>
              </view>
              <view class="method-btn" @click="handleChooseFromAlbum">
                <text class="iconfont icon-photo"></text>
                <text>从相册中选取</text>
              </view>
            </template>

            <!-- 从聊天中选取按钮，当有图片或视频类型时显示 -->
            <template
              v-if="
                !mediaTypes.length ||
                mediaTypes.includes('image') ||
                mediaTypes.includes('video')
              "
            >
              <view class="method-btn" @click="handleChooseFromChat">
                <text class="iconfont icon-chat"></text>
                <text>从聊天中选取</text>
              </view>
            </template>

            <template v-if="!mediaTypes.length || mediaTypes.includes('video')">
              <view class="method-btn" @click="handleChooseVideo">
                <text class="iconfont icon-a-AddVideo"></text>
                <text>添加视频</text>
              </view>
            </template>

            <template v-if="!mediaTypes.length || mediaTypes.includes('audio')">
              <view class="method-btn" @click="showRecordPopup">
                <text class="iconfont icon-audio1"></text>
                <text>添加音频</text>
              </view>
            </template>
          </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-btn" @click="handleSubmit">
          <text>确认提交</text>
        </view>
      </view>
    </uni-popup>

    <!-- 录音组件 -->
    <record-panel
      ref="recordPanel"
      @success="handleRecordSuccess"
      @close="handleRecordClose"
    />
  </view>
</template>

<script lang="ts" setup>
import { uploadFile } from "@/api/file";
import { getHomeworkExerciseDetail } from "@/api/homework-exercise";
import { createOrUpdateHomeworkSubmissionDetail } from "@/api/homework-submission";
import AudioPlayer from "@/components/audio-player/index.vue";
import MediaPreview from "@/components/media-preview/index.vue";
import RecordPanel from "@/components/record-panel/index.vue";
import { FileInfo, UploadFileInfo } from "@/types/file";
import { HomeworkExerciseSubmissionRequest } from "@/types/homework-submission";
import { computed, nextTick, onMounted, PropType, ref } from "vue";

interface MediaItem extends FileInfo {
  // 移除 type 字段
}

const props = defineProps({
  exerciseId: {
    type: Number,
    required: true,
  },
  homeworkId: {
    type: Number,
    required: true,
  },
  mediaTypes: {
    type: Array as PropType<Array<"image" | "video" | "audio">>,
    default: () => ["image", "video", "audio"],
  },
});

const emit = defineEmits(["success", "close"]);

const popup = ref<any>(null);
const recordPanel = ref<any>(null);
const showTextarea = ref(true);
const mediaList = ref<MediaItem[]>([]);
const recordList = ref<MediaItem[]>([]);
const remark = ref("");
const submissionId = ref<number>();
const exerciseDetail = ref<any>(null);

// 计算合并的媒体列表
const mediaPreviewList = computed(() => {
  const submission = exerciseDetail.value?.extSubmissionDetail;
  if (!submission) return mediaList.value;

  // 获取已批改的图片映射
  const gradingImagesMap = new Map(
    (submission.gradingImages || []).map((item: any) => [
      item.originalUrl,
      item.gradedUrl,
    ])
  );

  // 处理媒体列表，为已批改的图片添加 gradedUrl
  return mediaList.value.map((item) => {
    const gradedUrl = gradingImagesMap.get(item.url);
    return {
      ...item,
      gradedUrl,
      isGraded: !!gradedUrl,
    };
  });
});

// 处理媒体预览组件的删除事件
const handleMediaDelete = (index: number) => {
  // 如果是已批改的图片，不允许删除
  const item: any = mediaPreviewList.value[index];
  if (item.isGraded) return;

  // 直接删除对应索引的图片
  mediaList.value.splice(index, 1);
};

// 打开弹窗
const open = async () => {
  try {
    // 重置所有数据
    remark.value = "";
    mediaList.value = [];
    recordList.value = [];
    showTextarea.value = true;
    submissionId.value = undefined;
    exerciseDetail.value = null;

    // 获取作业练习详情
    const result = await getHomeworkExerciseDetail(props.exerciseId);
    if (result.isSuccess()) {
      // 保存作业详情数据
      exerciseDetail.value = result.getData();

      if (exerciseDetail.value?.extSubmissionDetail) {
        // 如果有提交记录，填充表单
        const submission = exerciseDetail.value.extSubmissionDetail;
        submissionId.value = submission?.id;
        remark.value = submission?.contentText || "";

        // 填充媒体列表（包含图片和视频）
        mediaList.value =
          submission?.contentImages?.map((item: UploadFileInfo) => item) || [];

        // 填充录音列表
        recordList.value =
          submission?.recordFileUrls?.map((item: UploadFileInfo) => ({
            ...item,
            type: "audio",
          })) || [];
      }
    }

    await nextTick();
    // 等待一个短暂的时间确保组件已经挂载
    setTimeout(() => {
      if (popup.value) {
        popup.value.open();
      } else {
        console.error("popup组件未初始化");
      }
    }, 100);
  } catch (error: any) {
    console.error("获取作业详情失败:", error);
    uni.showToast({
      title: error.message || "获取作业详情失败",
      icon: "none",
    });
  }
};

// 处理弹窗状态变化
const handlePopupChange = (e: { show: boolean }) => {
  console.log("popup change:", e.show);
  if (!e.show) {
    cleanupAndEmit();
  }
};

// 清理数据并触发事件
const cleanupAndEmit = () => {
  // 先隐藏 textarea
  showTextarea.value = false;

  // 清空内容
  remark.value = "";
  mediaList.value = [];
  recordList.value = [];

  // 触发关闭事件
  emit("close");

  // 延迟重新显示 textarea
  setTimeout(() => {
    showTextarea.value = true;
  }, 100);
};

// 关闭弹窗
const handleClose = () => {
  console.log("handleClose");
  if (popup.value) {
    popup.value.close();
  }
};

// 显示录音弹窗
const showRecordPopup = () => {
  if (recordPanel.value) {
    recordPanel.value.open();
  }
};

// 处理录音成功
const handleRecordSuccess = (fileInfo: FileInfo) => {
  recordList.value.push({
    ...fileInfo,
    duration: fileInfo.duration || 0,
  });
};

// 处理录音关闭
const handleRecordClose = () => {
  // 可以在这里添加额外的处理逻辑
};

// 拍照
const handleTakePhoto = async () => {
  try {
    const res: UniApp.ChooseMediaSuccessCallbackResult = await new Promise(
      (resolve, reject) => {
        uni.chooseMedia({
          count: 1,
          mediaType: ["image"],
          sourceType: ["camera"],
          camera: "back",
          sizeType: ["compressed"],
          success: resolve,
          fail: reject,
        });
      }
    );

    uni.showLoading({ title: "上传中...", mask: true });
    const tempFile = res.tempFiles[0];
    const result = await uploadFile({
      tempFilePath: tempFile.tempFilePath,
      name: `image_${Date.now()}.jpg`,
    });

    if (result.isSuccess()) {
      const fileInfo: FileInfo = result.getData();
      mediaList.value.push(fileInfo);
    } else {
      throw new Error(result.msg);
    }
  } catch (error: any) {
    // 用户取消操作时不显示提示
    if (error.errMsg?.includes("cancel")) return;

    console.error("拍照失败:", error);
    uni.showToast({
      title: error.message || "拍照失败",
      icon: "none",
    });
  } finally {
    uni.hideLoading();
  }
};

// 从相册选择图片
const handleChooseFromAlbum = async () => {
  try {
    const res: UniApp.ChooseMediaSuccessCallbackResult = await new Promise(
      (resolve, reject) => {
        uni.chooseMedia({
          count: 9,
          mediaType: ["image"],
          sourceType: ["album"],
          sizeType: ["compressed"],
          success: resolve,
          fail: reject,
        });
      }
    );

    uni.showLoading({ title: "上传中...", mask: true });
    for (const tempFile of res.tempFiles) {
      const result = await uploadFile({
        tempFilePath: tempFile.tempFilePath,
        name: `image_${Date.now()}.jpg`,
      });

      if (result.isSuccess()) {
        const fileInfo: FileInfo = result.getData();
        mediaList.value.push(fileInfo);
      } else {
        throw new Error(result.msg);
      }
    }
  } catch (error: any) {
    // 用户取消操作时不显示提示
    if (error.errMsg?.includes("cancel")) return;

    console.error("选择图片失败:", error);
    uni.showToast({
      title: error.message || "选择失败",
      icon: "none",
    });
  } finally {
    uni.hideLoading();
  }
};

// 从聊天选择文件
const handleChooseFromChat = () => {
  try {
    // 根据mediaTypes确定可选择的文件类型
    let type: "all" | "video" | "image" | "file" = "all";
    if (props.mediaTypes.length > 0) {
      if (
        props.mediaTypes.includes("image") &&
        !props.mediaTypes.includes("video")
      ) {
        type = "all";
      } else if (
        !props.mediaTypes.includes("image") &&
        props.mediaTypes.includes("video")
      ) {
        type = "all";
      }
    }

    // 设置文件扩展名
    const extensions = {
      image: ["jpg", "jpeg", "png", "gif"],
      video: ["mp4", "mov", "avi"],
      audio: ["mp3", "wav", "m4a", "aac", "ogg", "flac", "wma"],
      all: [
        "jpg",
        "jpeg",
        "png",
        "gif",
        "mp4",
        "mov",
        "avi",
        "mp3",
        "wav",
        "m4a",
        "aac",
        "ogg",
        "flac",
        "wma",
      ],
    };

    uni.chooseMessageFile({
      count: 9,
      type: type,
      extension: extensions[type],
      success: async (res) => {
        uni.showLoading({ title: "上传中...", mask: true });
        try {
          for (const file of res.tempFiles) {
            const fileType = file.name?.toLowerCase().split(".").pop() || "";

            // 判断文件类型
            const isImage = extensions.image.includes(fileType);
            const isVideo = extensions.video.includes(fileType);
            const isAudio = extensions.audio.includes(fileType);

            if (!isImage && !isVideo && !isAudio) continue;

            console.log("上传文件:", file.name);
            const result = await uploadFile({
              tempFilePath: file.path,
              name: file.name || `file_${Date.now()}.${fileType}`,
              ...(file.size && { size: file.size }),
            });
            console.log("上传文件结果:", result);
            if (result.isSuccess()) {
              const fileInfo = result.getData();
              if (isImage || isVideo) {
                mediaList.value.push(fileInfo);
              } else if (isAudio) {
                recordList.value.push({
                  ...fileInfo,
                });
              }
            } else {
              throw new Error(result.msg);
            }
          }
        } catch (error: any) {
          console.error("上传文件失败:", error);
          uni.showToast({
            title: error.message || "上传失败",
            icon: "none",
          });
        } finally {
          uni.hideLoading();
        }
      },
      fail: (error) => {
        if (!error.errMsg?.includes("cancel")) {
          console.error("选择文件失败:", error);
          uni.showToast({
            title: "选择文件失败",
            icon: "none",
          });
        }
      },
    });
  } catch (error: any) {
    console.error("选择文件失败:", error);
    uni.showToast({
      title: error.message || "选择失败",
      icon: "none",
    });
  }
};

// 选择视频
const handleChooseVideo = async () => {
  // 先显示提示弹窗
  uni.showModal({
    title: "视频时长提示",
    content: "视频超过60秒，上传会变慢，建议从聊天文件中选择",
    confirmText: "继续选择",
    cancelText: "取消",
    success: async (res) => {
      if (res.confirm) {
        try {
          const res = await uni.chooseVideo({
            sourceType: ["album"],
            maxDuration: 60,
          });

          uni.showLoading({ title: "上传中...", mask: true });
          const result = await uploadFile(
            {
              tempFilePath: res.tempFilePath,
              name: `video_${Date.now()}.mp4`,
            },
            60000 // 5分钟超时
          );

          if (result.isSuccess()) {
            const fileInfo = result.getData();
            mediaList.value.push(fileInfo);
          } else {
            throw new Error(result.msg);
          }
        } catch (error: any) {
          // 用户取消操作时不显示提示
          if (error.errMsg?.includes("cancel")) return;

          console.error("选择视频失败:", error);
          uni.showToast({
            title: error.message || "选择失败",
            icon: "none",
          });
        } finally {
          uni.hideLoading();
        }
      }
      // 如果点击取消，不做任何操作
    },
  });
};

// 删除音频
const handleDeleteAudio = (index: number) => {
  recordList.value.splice(index, 1);
};

// 提交作业
const handleSubmit = async () => {
  if (
    mediaList.value.length === 0 &&
    recordList.value.length === 0 &&
    remark.value === ""
  ) {
    return uni.showToast({
      title: "请至少添加一个媒体文件或填写备注",
      icon: "none",
    });
  }

  try {
    uni.showLoading({
      title: "提交中...",
      mask: true,
    });

    const submitData: HomeworkExerciseSubmissionRequest = {
      exerciseId: props.exerciseId,
      homeworkId: props.homeworkId,
      contentText: remark.value,
      contentImages: mediaList.value,
      recordFileUrls: recordList.value.map(({ ...item }) => item),
      fileUrls: [], // 清空 fileUrls
      ...(submissionId.value && { id: submissionId.value }), // 动态添加 id 属性
    };

    const result = await createOrUpdateHomeworkSubmissionDetail(submitData);
    if (result.isSuccess()) {
      uni.showToast({
        title: "提交成功",
        icon: "success",
      });
      emit("success");
      handleClose();
    } else {
      throw new Error(result.msg);
    }
  } catch (error: any) {
    console.error("提交作业失败:", error);
    uni.showToast({
      title: error.message || "提交失败",
      icon: "none",
    });
  } finally {
    uni.hideLoading();
  }
};

onMounted(() => {
  // 确保refs已经初始化
  nextTick(() => {
    console.log("Popup ref:", popup.value);
    console.log("Record panel ref:", recordPanel.value);
  });
});

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.homework-submit {
  .submit-popup {
    min-height: 60vh;
    max-height: 90vh;
    width: 100%;
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .popup-header {
      width: 100%;
      padding: 32rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 2rpx solid #f5f5f5;
      box-sizing: border-box;

      .header-placeholder {
        width: 40rpx;
      }

      .title {
        flex: 1;
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
        text-align: center;
      }

      .icon-close {
        width: 40rpx;
        font-size: 40rpx;
        color: #999;
        text-align: right;
      }
    }

    .content {
      flex: 1;
      width: 100%;
      padding: 32rpx;
      overflow-y: auto;
      overflow-x: hidden;
      display: flex;
      flex-direction: column;
      gap: 32rpx;
      box-sizing: border-box;

      .remark {
        width: 100%;
        height: 160rpx;
        min-height: 160rpx;
        max-height: 160rpx;
        background: #f8f8f8;
        border-radius: 8rpx;
        padding: 24rpx;
        font-size: 28rpx !important;
        line-height: 40rpx !important;
        color: #333;
        box-sizing: border-box;
        resize: none;
      }

      .media-list {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 16rpx;
        flex-shrink: 0;

        .audio-list {
          display: flex;
          flex-direction: column;
          gap: 16rpx;

          .audio-item {
            position: relative;
            width: 100%;
            background: #f8f8f8;
            border-radius: 8rpx;
            overflow: hidden;
          }
        }

        :deep(.media-preview) {
          padding: 0;
        }
      }

      .upload-methods {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 16rpx;
        padding: 0 32rpx;

        .method-btn {
          display: flex;
          align-items: center;
          padding: 16rpx 24rpx;
          background: #f8f8f8;
          border-radius: 100rpx;
          gap: 8rpx;

          .iconfont {
            font-size: 32rpx;
            color: #666;
          }

          text:not(.iconfont) {
            font-size: 28rpx;
            color: #333;
          }
        }
      }
    }

    .submit-btn {
      width: 100%;
      padding: 32rpx;
      background: #fff;
      border-top: 2rpx solid #f5f5f5;
      box-sizing: border-box;

      text {
        display: block;
        width: 100%;
        height: 88rpx;
        background: #ffd600;
        border-radius: 44rpx;
        font-size: 32rpx;
        color: #333;
        text-align: center;
        line-height: 88rpx;
      }
    }
  }
}
</style>
