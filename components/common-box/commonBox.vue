<template>
  <view class="commonBox">
    <!-- 背景渐变区域 -->
    <view class="gradient-bg"></view>

    <!-- 自定义导航栏 -->
    <view class="custom-nav" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="nav-content">
        <view class="left">
          <text class="iconfont icon-back back-icon" @click="handleBack"></text>
          <slot name="left-txt"></slot>
        </view>
        <view class="title">{{ title }}</view>
        <view class="right" @click="handleRightClick" v-if="rightIcon">
          <text :class="['iconfont', rightIcon]"></text>
        </view>
      </view>
    </view>

    <view class="content" :style="{ paddingTop: statusBarHeight + 44 + 'px' }">
      <slot name="default"></slot>
    </view>
    <Loading :loading="loading" v-if="loading"></Loading>
  </view>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import Loading from "./loadingBox.vue";

// 获取状态栏高度
const statusBarHeight = ref(0);

const props = withDefaults(
  defineProps<{
    loading: Boolean;
    title: String;
    rightIcon?: string;
    showLeftText?: boolean;
    useDefaultBack?: boolean;
  }>(),
  {
    rightIcon: "",
    showLeftText: true,
    useDefaultBack: true,
  }
);

const emit = defineEmits<{
  (e: "back"): void;
  (e: "rightClick"): void;
}>();

const handleBack = () => {
  // 如果设置了使用默认返回逻辑，则执行返回
  if (props.useDefaultBack) {
    uni.navigateBack();
  } else {
    // 总是触发 back 事件
    emit("back");
  }
};

const handleRightClick = () => {
  emit("rightClick");
};

onMounted(() => {
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight as number;
});
</script>
<style lang="scss">
.commonBox {
  height: 100vh;
  background: #fff;
  position: relative;
  width: 100%;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;

  .gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 200px;
    background: linear-gradient(180deg, #fcf4ab 0%, #ffffff 100%);
    z-index: 1;
  }

  .custom-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: transparent;

    .nav-content {
      position: relative;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      z-index: 101;

      .left {
        position: relative;
        z-index: 102;
        display: flex;
        align-items: center;

        .back-icon {
          font-size: 24px !important;
          margin-right: 5px;
          padding: 10px;
          margin: -10px;
          cursor: pointer;
        }
      }

      .title {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        font-size: 16px;
        color: rgba(0, 0, 0, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 101;
      }

      .right {
        position: relative;
        z-index: 102;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;

        .iconfont {
          font-size: 24px;
          color: #333;
        }

        &:active {
          opacity: 0.7;
        }
      }
    }
  }

  .content {
    flex: 1 1 auto;
    position: relative;
    z-index: 2;
  }
}
</style>
