<template>
  <view class="mine-page">
    <!-- 用户信息卡片 -->
    <view class="user-card" v-if="isLoggedIn">
      <view class="user-info" @click="handleUserCardClick">
        <image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
        <view class="info-content">
          <text class="nickname">{{ userInfo.nickname }}</text>
        </view>
        <view class="user-info-right">
          <text class="hint-text">点击进入设置</text>
          <text class="iconfont icon-arrow"></text>
        </view>
      </view>
      <view class="study-stats">
        <view class="stat-item">
          <text class="num">{{ state.homeworkStats.totalCount }}</text>
          <text class="label">总作业数</text>
        </view>
        <view class="stat-item">
          <text class="num">{{ state.homeworkStats.completedCount }}</text>
          <text class="label">已完成</text>
        </view>
        <view class="stat-item">
          <text class="num">{{ state.homeworkStats.uncompletedCount }}</text>
          <text class="label">待完成</text>
        </view>
      </view>
    </view>

    <!-- 未登录状态 -->
    <view class="user-card" v-else>
      <view class="user-info" @click="handleLogin">
        <image
          class="avatar"
          src="http://eds-local-1259402086.cos.ap-guangzhou.myqcloud.com/4f8416fb4b986cbcff800d9e91e55656e19984d4575d77b735b813bdf7498ad7.png"
          mode="aspectFill"
        ></image>
        <view class="info-content">
          <text class="nickname">未登录</text>
          <text class="login-tip">点击登录账号</text>
        </view>
        <view class="user-info-right">
          <text class="iconfont icon-arrow"></text>
        </view>
      </view>
    </view>

    <!-- 其他功能 -->
    <view class="menu-list" v-if="isLoggedIn">
      <view
        class="menu-item"
        v-for="(item, index) in menuItems"
        :key="index"
        @tap="handleMenuClick(item)"
      >
        <view class="menu-left">
          <u-icon :name="item.icon" size="20" color="#666"></u-icon>
          <text class="menu-title">{{ item.title }}</text>
        </view>
        <u-icon name="arrow-right" size="16" color="#999"></u-icon>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getHomeworkStatistics } from "@/api/homework";
import { useUserStore } from "@/store/user";
import { computed, onActivated, onMounted, reactive, watch } from "vue";
// @ts-ignore
import { onShareAppMessage, onShareTimeline } from "@dcloudio/uni-app";

interface HomeworkStats {
  totalCount: number;
  completedCount: number;
  uncompletedCount: number;
}

interface MenuItem {
  title: string;
  icon: string;
  path: string;
}

const userStore = useUserStore();
const state = reactive({
  homeworkStats: {
    totalCount: 0,
    completedCount: 0,
    uncompletedCount: 0,
  } as HomeworkStats,
});

const menuItems: MenuItem[] = [
  {
    title: "设置",
    icon: "setting",
    path: "/user-package/settings/settings",
  },
];

const userInfo = computed(() => userStore.info);
const isLoggedIn = computed(() => userStore.hasLogin);

// 处理登录
const handleLogin = () => {
  uni.navigateTo({ url: "/user-package/login/social" });
};

// 处理用户卡片点击
const handleUserCardClick = () => {
  uni.navigateTo({ url: "/user-package/settings/settings" });
};

// 处理菜单点击
const handleMenuClick = (item: MenuItem) => {
  uni.navigateTo({ url: item.path });
};

// 获取作业统计
const fetchHomeworkStats = async () => {
  try {
    const { data } = await getHomeworkStatistics();
    state.homeworkStats = data;
  } catch (error) {
    console.error("Failed to fetch homework statistics:", error);
  }
};

onMounted(() => {
  if (isLoggedIn.value) {
    userStore.fetchUserInfo();
    fetchHomeworkStats();
  }
});

onActivated(() => {
  if (isLoggedIn.value) {
    fetchHomeworkStats();
  }
});

// 监听登录状态变化，登录后主动刷新数据
watch(isLoggedIn, (newValue, oldValue) => {
  // 当从未登录变为已登录时，主动刷新数据
  if (newValue && !oldValue) {
    userStore.fetchUserInfo();
    fetchHomeworkStats();
  }
});

// 分享给好友
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  return {
    title: "白熊作业广场",
    path: "/pages/homework-square/homework-square",
    imageUrl: "/static/icon.png", // 默认分享图，需要添加一张默认分享图
    success: function (res: any) {
      // 转发成功的回调函数
      uni.showToast({
        title: "分享成功",
        icon: "success",
      });
    },
    fail: function (res: any) {
      // 转发失败的回调函数
      uni.showToast({
        title: "分享失败",
        icon: "none",
      });
    },
  };
});

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: "白熊作业广场",
    query: "",
    imageUrl: "/static/icon.png", // 默认分享图
  };
});
// #endif
</script>

<style lang="scss" scoped>
.mine-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: env(safe-area-inset-bottom);
}

.user-card {
  background-color: #fff;
  padding: 20px 15px;
  margin-bottom: 10px;

  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 30px;
      margin-right: 15px;
    }

    .info-content {
      flex: 1;

      .nickname {
        font-size: 18px;
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
        display: block;
      }

      .level {
        font-size: 12px;
        color: #1296db;
        background-color: #f0f9ff;
        padding: 2px 8px;
        border-radius: 10px;
      }

      .login-tip {
        font-size: 14px;
        color: #999;
      }
    }

    .user-info-right {
      display: flex;
      align-items: center;

      .hint-text {
        font-size: 24rpx;
        color: #999;
        margin-right: 8rpx;
      }

      .iconfont {
        font-size: 28rpx;
        color: #999;
      }
    }
  }

  .study-stats {
    display: flex;
    justify-content: space-around;
    padding-top: 15px;
    border-top: 1px solid #f5f5f5;

    .stat-item {
      text-align: center;

      .num {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 4px;
      }

      .label {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

.menu-list {
  background-color: #fff;

  .menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .menu-left {
      display: flex;
      align-items: center;

      .menu-title {
        font-size: 14px;
        color: #333;
        margin-left: 10px;
      }
    }
  }
}
</style>
