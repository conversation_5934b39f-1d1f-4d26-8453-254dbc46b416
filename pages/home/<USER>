<template>
  <view class="home-page">
    <!-- 课程列表 -->
    <z-paging ref="pagingRef" @query="fetchCourseList" :auto="true">
      <view class="course-list">
        <view
          v-for="item in courseList"
          :key="item.id"
          class="course-item"
          @click="goToCourseDetail(item)"
        >
          <image class="course-image" :src="item.coverUrl" mode="aspectFill" />
          <view class="course-info">
            <text class="course-name">{{ item.name }}</text>
            <text class="course-desc" v-if="item.shortDescription">{{
              item.shortDescription
            }}</text>

            <!-- 价格信息区域 -->
            <view class="price-section">
              <view class="price-info">
                <text class="course-price">
                  ¥{{ (item.salePrice / 100).toFixed(2) }}
                </text>
                <text
                  class="original-price"
                  v-if="item.originalPrice !== item.salePrice"
                >
                  ¥{{ (item.originalPrice / 100).toFixed(2) }}
                </text>
              </view>
              <!-- 折扣标签 -->
              <view
                class="discount-badge"
                v-if="item.originalPrice !== item.salePrice"
              >
                {{
                  Math.round((1 - item.salePrice / item.originalPrice) * 10)
                }}折
              </view>
            </view>

            <!-- 购买统计信息 -->
            <view class="course-stats">
              <text class="stats-text">
                <text class="stats-number">{{ item.salesCount }}</text>
                <text class="stats-label">人已报名</text>
              </text>
              <text class="stats-separator">•</text>
              <text class="stats-text">
                <text class="stats-number">{{ item.viewCount || 0 }}</text>
                <text class="stats-label">人浏览</text>
              </text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <!-- #ifdef MP-WEIXIN -->
    <zero-privacy :onNeed="false" :hideTabBar="true"></zero-privacy>
    <!-- #endif -->
  </view>
</template>

<script lang="ts" setup>
import { getCoursePage, type CourseItem } from "@/api/product";
import { ref } from "vue";
// @ts-ignore
import { onShareAppMessage, onShareTimeline } from "@dcloudio/uni-app";

const pagingRef = ref();
const courseList = ref<CourseItem[]>([]);

// 获取课程列表
const fetchCourseList = async (pageNo: number, pageSize: number) => {
  try {
    const res = await getCoursePage({
      pageNo,
      pageSize,
    });

    console.log("res", res);
    if (res.data?.list) {
      // 过滤状态为启用的课程
      const filteredList = res.data.list.filter((item) => item.status === 1);
      console.log("filteredList", filteredList);

      // 更新本地响应式状态
      courseList.value = filteredList;
      console.log("courseList.value", courseList.value);

      // 通知z-paging组件
      pagingRef.value?.complete(filteredList);

      // 调试z-paging的状态
      setTimeout(() => {
        console.log("pagingRef.value?.finalList", pagingRef.value?.finalList);
      }, 100);
    } else {
      courseList.value = [];
      pagingRef.value?.complete([]);
    }
  } catch (error) {
    console.error("获取课程列表失败:", error);
    courseList.value = [];
    pagingRef.value?.complete(false);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
    });
  }
};

// 跳转到课程详情
const goToCourseDetail = (item: CourseItem) => {
  uni.navigateTo({
    url: `/pages/course-detail/course-detail?id=${item.id}`,
  });
};

// 分享给好友
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  return {
    title: "白熊课堂首页",
    path: "/pages/home/<USER>",
    imageUrl: "/static/icon.png", // 默认分享图
    success: function (res: any) {
      // 转发成功的回调函数
      uni.showToast({
        title: "分享成功",
        icon: "success",
      });
    },
    fail: function (res: any) {
      // 转发失败的回调函数
      uni.showToast({
        title: "分享失败",
        icon: "none",
      });
    },
  };
});

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: "白熊课堂首页",
    query: "",
    imageUrl: "/static/icon.png", // 默认分享图
  };
});
// #endif
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;

  .course-list {
    padding: 20rpx;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .course-item {
      width: 48%;
      background-color: #fff;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      .course-image {
        width: 100%;
        height: 300rpx;
        background-color: #f8f9fa;
      }

      .course-info {
        padding: 20rpx;

        .course-name {
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          display: block;
          margin-bottom: 12rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          line-height: 1.4;
        }

        .course-desc {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 16rpx;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          line-height: 1.3;
        }

        .price-section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16rpx;

          .price-info {
            display: flex;
            align-items: baseline;
            gap: 8rpx;

            .course-price {
              font-size: 36rpx;
              color: #ff6b6b;
              font-weight: bold;
            }

            .original-price {
              font-size: 24rpx;
              color: #999;
              text-decoration: line-through;
            }
          }

          .discount-badge {
            background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
            color: #fff;
            font-size: 20rpx;
            padding: 4rpx 8rpx;
            border-radius: 12rpx;
            font-weight: bold;
          }
        }

        .course-stats {
          display: flex;
          align-items: center;
          gap: 12rpx;
          padding-top: 12rpx;
          border-top: 1rpx solid #f0f0f0;

          .stats-text {
            display: flex;
            align-items: baseline;
            gap: 2rpx;

            .stats-number {
              font-size: 22rpx;
              color: #333;
              font-weight: 600;
            }

            .stats-label {
              font-size: 20rpx;
              color: #999;
            }
          }

          .stats-separator {
            font-size: 20rpx;
            color: #ddd;
          }
        }
      }

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
      }
    }
  }
}
</style>
