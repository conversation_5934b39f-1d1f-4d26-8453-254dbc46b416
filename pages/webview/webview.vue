<template>
  <view class="webview-container">
    <web-view :src="url" @message="handleMessage"></web-view>
  </view>
</template>

<script lang="ts" setup>
// @ts-ignore
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { ref } from "vue";

const url = ref("");

// @ts-ignore
onLoad((options: Record<string, any>) => {
  if (options.url) {
    url.value = decodeURIComponent(options.url);
  } else {
    uni.showToast({
      title: "链接无效",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

const handleMessage = (event: any) => {
  console.log("webview message:", event);
};

onUnload(() => {
  // 页面卸载时的清理工作
});
</script>

<style lang="scss" scoped>
.webview-container {
  width: 100%;
  height: 100vh;
}
</style>
