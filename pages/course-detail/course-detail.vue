<template>
  <view class="course-detail-page">
    <view v-if="state.loading" class="loading-container">
      <text>加载中...</text>
    </view>

    <view v-else-if="state.course" class="detail-content">
      <!-- 课程封面图 -->
      <view class="course-cover-section">
        <image
          class="course-cover"
          :src="state.course.coverUrl"
          mode="aspectFill"
          @click="previewImage"
        />
        <view class="cover-overlay">
          <view class="play-button">
            <text class="play-icon">▶</text>
          </view>
        </view>
      </view>

      <!-- 课程基本信息 -->
      <view class="course-info-card">
        <text class="course-name">{{ state.course.name }}</text>
        <text class="course-desc" v-if="state.course.shortDescription">
          {{ state.course.shortDescription }}
        </text>

        <view class="price-section">
          <text class="current-price">
            ¥{{ (state.course.salePrice / 100).toFixed(2) }}
          </text>
          <text
            class="original-price"
            v-if="state.course.originalPrice !== state.course.salePrice"
          >
            ¥{{ (state.course.originalPrice / 100).toFixed(2) }}
          </text>
          <view
            class="discount-tag"
            v-if="state.course.originalPrice !== state.course.salePrice"
          >
            {{
              Math.round(
                (1 - state.course.salePrice / state.course.originalPrice) * 10
              )
            }}折
          </view>
        </view>

        <view class="course-stats">
          <view class="stat-item">
            <text class="stat-number">{{ state.course.salesCount }}</text>
            <text class="stat-label">人已报名</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ state.course.viewCount }}</text>
            <text class="stat-label">人浏览</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{
              formatDate(state.course.createTime)
            }}</text>
            <text class="stat-label">开课时间</text>
          </view>
        </view>
      </view>

      <!-- 课程详情描述 -->
      <view class="description-section">
        <view class="section-title">课程详情</view>
        <view class="description-content">
          <rich-text :nodes="state.course.description"></rich-text>
        </view>
      </view>

      <!-- 课程特色 -->
      <view class="features-section">
        <view class="section-title">课程特色</view>
        <view class="features-list">
          <view class="feature-item">
            <text class="feature-icon">📚</text>
            <text class="feature-text">系统化学习路径</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">👨‍🏫</text>
            <text class="feature-text">专业讲师授课</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">🎯</text>
            <text class="feature-text">实战项目练习</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">🏆</text>
            <text class="feature-text">完课证书认证</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions" v-if="state.course">
      <button class="action-btn secondary" @click="collectCourse">
        <text class="btn-icon">❤️</text>
        <text>收藏课程</text>
      </button>
      <button
        class="action-btn primary"
        @click="enrollNow"
        :disabled="state.enrollLoading"
      >
        {{ state.enrollLoading ? "报名中..." : "立即报名" }}
      </button>
    </view>

    <!-- #ifdef MP-WEIXIN -->
    <zero-privacy :onNeed="false" :hideTabBar="true"></zero-privacy>
    <!-- #endif -->
  </view>
</template>

<script lang="ts" setup>
import { getCourseDetail, type CourseDetail } from "@/api/product";
import { reactive } from "vue";
// @ts-ignore
import { onLoad, onShareAppMessage, onShareTimeline } from "@dcloudio/uni-app";

const state = reactive({
  course: null as CourseDetail | null,
  loading: true,
  courseId: 0,
  // 添加报名加载状态
  enrollLoading: false,
});

// 页面加载时获取课程ID
// @ts-ignore
onLoad((options: Record<string, any>) => {
  if (options.id) {
    state.courseId = parseInt(options.id);
    fetchCourseDetail();
  } else {
    uni.showToast({
      title: "课程ID无效",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 获取课程详情
const fetchCourseDetail = async () => {
  try {
    state.loading = true;
    const res = await getCourseDetail(state.courseId);
    console.log("res", res);
    if (res.data) {
      state.course = res.data;
    } else {
      uni.showToast({
        title: "获取课程详情失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("获取课程详情失败:", error);
    uni.showToast({
      title: "获取课程详情失败",
      icon: "none",
    });
  } finally {
    state.loading = false;
  }
};

// 预览封面图片
const previewImage = () => {
  if (!state.course) return;

  uni.previewImage({
    current: state.course.coverUrl,
    urls: [state.course.coverUrl],
  });
};

// 收藏课程
const collectCourse = () => {
  uni.showToast({
    title: "已收藏课程",
    icon: "success",
  });
};

// 立即报名
const enrollNow = async () => {
  try {
    state.enrollLoading = true;

    // TODO: 这里后续可以调用报名接口
    await new Promise((resolve) => setTimeout(resolve, 1000)); // 模拟API调用

    uni.showToast({
      title: "报名成功",
      icon: "success",
    });

    // TODO: 跳转到学习页面或支付页面
    console.log("课程报名成功，课程ID:", state.courseId);
  } catch (error) {
    console.error("课程报名失败:", error);
    uni.showToast({
      title: "报名失败，请重试",
      icon: "none",
    });
  } finally {
    state.enrollLoading = false;
  }
};

// 格式化日期
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")}`;
};

// 分享给好友
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  return {
    title: state.course?.name || "课程详情",
    path: `/pages/course-detail/course-detail?id=${state.courseId}`,
    imageUrl: state.course?.coverUrl || "/static/icon.png",
    success: function (res: any) {
      uni.showToast({
        title: "分享成功",
        icon: "success",
      });
    },
    fail: function (res: any) {
      uni.showToast({
        title: "分享失败",
        icon: "none",
      });
    },
  };
});

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: state.course?.name || "课程详情",
    query: `id=${state.courseId}`,
    imageUrl: state.course?.coverUrl || "/static/icon.png",
  };
});
// #endif
</script>

<style lang="scss" scoped>
.course-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; // 为底部操作栏留空间
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;

  text {
    color: #666;
    font-size: 28rpx;
  }
}

.detail-content {
  .course-cover-section {
    position: relative;
    width: 100%;
    height: 500rpx;
    background-color: #000;

    .course-cover {
      width: 100%;
      height: 100%;
    }

    .cover-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);

      .play-button {
        width: 120rpx;
        height: 120rpx;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);

        .play-icon {
          font-size: 40rpx;
          color: #333;
          margin-left: 8rpx;
        }
      }
    }
  }

  .course-info-card {
    background-color: #fff;
    padding: 32rpx;
    margin-top: 20rpx;

    .course-name {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 16rpx;
      line-height: 1.4;
    }

    .course-desc {
      font-size: 28rpx;
      color: #666;
      display: block;
      margin-bottom: 24rpx;
      line-height: 1.5;
    }

    .price-section {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
      gap: 16rpx;

      .current-price {
        font-size: 48rpx;
        color: #ff6b6b;
        font-weight: bold;
      }

      .original-price {
        font-size: 28rpx;
        color: #999;
        text-decoration: line-through;
      }

      .discount-tag {
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        color: #fff;
        font-size: 24rpx;
        padding: 4rpx 12rpx;
        border-radius: 20rpx;
        font-weight: bold;
      }
    }

    .course-stats {
      display: flex;
      justify-content: space-between;
      padding: 24rpx 0;
      border-top: 1rpx solid #f0f0f0;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;

        .stat-number {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }

        .stat-label {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }

  .description-section {
    background-color: #fff;
    padding: 32rpx;
    margin-top: 20rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 24rpx;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8rpx;
        height: 32rpx;
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        border-radius: 4rpx;
      }
    }

    .description-content {
      line-height: 1.6;

      // 富文本内容样式
      :deep(p) {
        margin-bottom: 16rpx;
        font-size: 28rpx;
        color: #666;
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 8rpx;
        margin: 16rpx 0;
      }
    }
  }

  .features-section {
    background-color: #fff;
    padding: 32rpx;
    margin-top: 20rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 24rpx;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8rpx;
        height: 32rpx;
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        border-radius: 4rpx;
      }
    }

    .features-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24rpx;

      .feature-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        gap: 16rpx;

        .feature-icon {
          font-size: 32rpx;
        }

        .feature-text {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 32rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

  .action-btn {
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;

    &.secondary {
      flex: 0 0 200rpx;
      background-color: #f0f0f0;
      color: #333;

      .btn-icon {
        font-size: 28rpx;
      }
    }

    &.primary {
      flex: 1;
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      color: #fff;
      box-shadow: 0 4rpx 20rpx rgba(255, 107, 107, 0.3);

      &:disabled {
        background: #ccc;
        color: #999;
        cursor: not-allowed;
        box-shadow: none;
      }
    }

    &:active {
      transform: scale(0.98);
      transition: transform 0.2s ease;
    }

    &:disabled:active {
      transform: none;
    }
  }
}
</style>
