image.png
<template>
  <view class="study-page">
    <z-paging
      ref="pagingRef"
      v-model="state.homeworkList"
      :default-page-no="1"
      :default-page-size="500"
      @query="fetchHomeworkList"
    >
      <view class="study-tags">
        <view
          v-for="item in state.studyData"
          :key="item.id"
          :class="['study-tag', { active: state.activeStudyId === item.id }]"
          @click="handleTagClick(item.id)"
        >
          {{ item.name }}
        </view>
      </view>
      <view class="homework-list">
        <view
          v-for="group in state.homeworkList"
          :key="`${group.year}-${group.month}`"
        >
          <view class="month-title">{{ formatMonthTitle(group) }}</view>
          <view
            v-for="item in group.homeworkList"
            :key="item.id"
            class="homework-item"
            @click="goToHomeworkDetail(item.id, item.homeworkType)"
          >
            <view class="homework-info">
              <view class="title-row">
                <view class="left">
                  <text class="iconfont icon-paper"></text>
                  <text class="title">{{ item.title }}</text>
                </view>
                <text
                  v-if="item.extMySubmitAssignees?.allSubmitted"
                  class="status submitted"
                  >已提交</text
                >
                <text v-else class="status unsubmitted">去提交</text>
              </view>
              <text class="date">发布于{{ formatDate(item.createTime) }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { getHomeworkList, getMyStudyData, StudyDataItem } from "@/api/homework";
import { useUserStore } from "@/store/user";
import { computed, reactive, ref, watch } from "vue";
// @ts-ignore
import { onShareAppMessage, onShareTimeline } from "@dcloudio/uni-app";

interface HomeworkItem {
  id: number;
  title: string;
  description: string;
  createTime: number;
  homeworkType: number;
  extMySubmitAssignees: {
    submitted: boolean;
    allSubmitted: boolean;
  } | null;
}

interface HomeworkGroup {
  year: number;
  month: number;
  homeworkList: HomeworkItem[];
}

const pagingRef = ref();
const userStore = useUserStore();
const state = reactive({
  homeworkList: [] as HomeworkGroup[],
  studyData: [] as StudyDataItem[],
  activeStudyId: 0, // 默认选中id=0的标签
});

const isLoggedIn = computed(() => userStore.hasLogin);

// 格式化月份标题
const formatMonthTitle = (group: HomeworkGroup): string => {
  const currentYear = new Date().getFullYear();
  if (group.year === currentYear) {
    return `${group.month}月`;
  }
  return `${group.year}年${group.month}月`;
};

// 格式化日期
const formatDate = (timestamp: number): string => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日 ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 获取学习数据
const fetchStudyData = async () => {
  try {
    const res = await getMyStudyData();
    const data = res.getData();
    if (Array.isArray(data)) {
      state.studyData = data;
      // 如果当前没有选中的标签或者选中的标签不在列表中，则选中第一个标签
      if (
        state.studyData.length > 0 &&
        (!state.activeStudyId ||
          !state.studyData.some((item) => item.id === state.activeStudyId))
      ) {
        state.activeStudyId = state.studyData[0].id;
      }
    } else {
      state.studyData = [];
    }
    return state.studyData;
  } catch (error) {
    console.error("获取学习数据失败:", error);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
    });
    state.studyData = [];
    return state.studyData;
  }
};

// 处理标签点击事件
const handleTagClick = (id: number) => {
  if (state.activeStudyId !== id) {
    state.activeStudyId = id;
    pagingRef.value?.reload();
  }
};

// 获取作业列表
const fetchHomeworkList = async (pageNo: number, pageSize: number) => {
  try {
    // 先获取标签数据
    await fetchStudyData();

    // 如果没有标签数据或者标签数据为空，则返回空数组
    if (!state.studyData || state.studyData.length === 0) {
      pagingRef.value?.complete([]);
      return;
    }

    // 如果 activeStudyId 不存在于当前标签列表中，则设置为第一个标签的 id
    const studyIds = state.studyData.map((item) => item.id);
    if (!studyIds.includes(state.activeStudyId)) {
      state.activeStudyId = state.studyData[0].id;
    }

    // 只有当 activeStudyId 为 0 时才获取作业列表
    if (state.activeStudyId !== 0) {
      pagingRef.value?.complete([]);
      return;
    }

    const res = await getHomeworkList({
      pageNo,
      pageSize,
    });
    const data = res.getData();

    // 确保返回的是数组
    console.log("data", data);
    if (Array.isArray(data)) {
      pagingRef.value?.complete(data);
    } else {
      pagingRef.value?.complete([]);
    }
  } catch (error) {
    console.error("获取作业列表失败:", error);
    pagingRef.value?.complete(false);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
    });
  }
};

// 跳转到作业详情
const goToHomeworkDetail = (id: number, homeworkType: number) => {
  const path =
    homeworkType === 1
      ? "/homework-package/homework-detail/homework-detail"
      : "/homework-package/homework-detail-daily/homework-detail-daily";

  uni.navigateTo({
    url: `${path}?id=${id}&from=study`,
  });
};

// 监听登录状态变化，登录后主动刷新数据
watch(isLoggedIn, (newValue, oldValue) => {
  // 当从未登录变为已登录时，主动重新加载作业列表数据
  if (newValue && !oldValue) {
    pagingRef.value?.reload();
  }
});

// 分享给好友
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  return {
    title: "白熊作业广场",
    path: "/pages/homework-square/homework-square",
    imageUrl: "/static/icon.png", // 默认分享图，需要添加一张默认分享图
    success: function (res: any) {
      // 转发成功的回调函数
      uni.showToast({
        title: "分享成功",
        icon: "success",
      });
    },
    fail: function (res: any) {
      // 转发失败的回调函数
      uni.showToast({
        title: "分享失败",
        icon: "none",
      });
    },
  };
});

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: "白熊作业广场",
    query: "",
    imageUrl: "/static/icon.png", // 默认分享图
  };
});
// #endif
</script>

<style lang="scss" scoped>
.study-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;

  .study-tags {
    display: flex;
    flex-wrap: wrap;
    padding: 12px 16px 4px;
    gap: 8px;

    .study-tag {
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 14px;
      background-color: #f0f0f0;
      color: #666;

      &.active {
        background-color: #ffe161;
        color: #333;
      }
    }
  }

  .homework-list {
    padding: 12px 16px;

    .month-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin: 8px 0;
    }

    .homework-item {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;

      .homework-info {
        .title-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .left {
            display: flex;
            align-items: flex-start;
            flex: 1;

            .iconfont {
              font-size: 20px;
              color: #fff200;
              margin-right: 8px;
              margin-top: 2px;
            }

            .title {
              font-size: 15px;
              color: #333;
              line-height: 1.4;
            }
          }

          .status {
            padding: 4px 12px;
            border-radius: 24px;
            font-size: 13px;

            &.submitted {
              background-color: #f0f0f0;
              color: #999999;
            }

            &.unsubmitted {
              background-color: #ffe161;
              color: #333333;
            }
          }
        }

        .date {
          font-size: 13px;
          color: #999;
          padding-left: 28px;
        }
      }
    }
  }
}
</style>
