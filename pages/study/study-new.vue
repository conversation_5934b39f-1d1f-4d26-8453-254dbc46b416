<template>
  <view class="study-page">
    <z-paging
      ref="pagingRef"
      v-model="state.homeworkList"
      :default-page-no="1"
      :default-page-size="500"
      @query="fetchHomeworkList"
    >
      <view class="study-tags">
        <view
          v-for="item in state.studyData"
          :key="item.id"
          :class="['study-tag', { active: state.activeStudyId === item.id }]"
          @click="handleTagClick(item.id)"
        >
          {{ item.name }}
        </view>
      </view>

      <!-- 课程系列内容切换 -->
      <view
        v-if="state.activeStudyId !== 0 && state.selectedSeries"
        class="course-content"
      >
        <view class="course-switch">
          <view
            v-for="course in state.selectedSeries.allCourses"
            :key="course.id"
            :class="[
              'course-option',
              { active: state.selectedCourseId === course.id },
            ]"
            @click="handleCourseSwitch(course.id)"
          >
            {{ course.volumeName || course.name }}
          </view>
        </view>
        <view class="course-detail">
          <view v-if="state.selectedCourse" class="course-info">
            <image
              v-if="state.selectedCourse.coverUrl"
              :src="state.selectedCourse.coverUrl"
              class="course-cover"
            />
            <text class="course-name">{{ state.selectedCourse.name }}</text>
            <text
              v-if="state.selectedCourse.shortDescription"
              class="course-desc"
              >{{ state.selectedCourse.shortDescription }}</text
            >
          </view>
        </view>
      </view>

      <!-- 作业列表 -->
      <view v-else class="homework-list">
        <view
          v-for="group in state.homeworkList"
          :key="`${group.year}-${group.month}`"
        >
          <view class="month-title">{{ formatMonthTitle(group) }}</view>
          <view
            v-for="item in group.homeworkList"
            :key="item.id"
            class="homework-item"
            @click="goToHomeworkDetail(item.id, item.homeworkType)"
          >
            <view class="homework-info">
              <view class="title-row">
                <view class="left">
                  <text class="iconfont icon-paper"></text>
                  <text class="title">{{ item.title }}</text>
                </view>
                <text
                  v-if="item.extMySubmitAssignees?.allSubmitted"
                  class="status submitted"
                  >已提交</text
                >
                <text v-else class="status unsubmitted">去提交</text>
              </view>
              <text class="date">发布于{{ formatDate(item.createTime) }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import {
  CourseInfo,
  CourseSeries,
  CourseSeriesData,
  getHomeworkList,
  getMyCoursesSeries,
  getMyStudyData,
  StudyDataItem,
} from "@/api/homework";
import { useUserStore } from "@/store/user";
import { computed, reactive, ref, watch } from "vue";
// @ts-ignore
import { onShareAppMessage, onShareTimeline } from "@dcloudio/uni-app";

interface HomeworkItem {
  id: number;
  title: string;
  description: string;
  createTime: number;
  homeworkType: number;
  extMySubmitAssignees: {
    submitted: boolean;
    allSubmitted: boolean;
  } | null;
}

interface HomeworkGroup {
  year: number;
  month: number;
  homeworkList: HomeworkItem[];
}

const pagingRef = ref();
const userStore = useUserStore();
const state = reactive({
  homeworkList: [] as HomeworkGroup[],
  studyData: [] as StudyDataItem[],
  activeStudyId: 0, // 默认选中id=0的标签
  courseSeriesData: null as CourseSeriesData | null,
  selectedSeries: null as CourseSeries | null,
  selectedCourseId: null as number | null,
  selectedCourse: null as CourseInfo | null,
});

const isLoggedIn = computed(() => userStore.hasLogin);

// 格式化月份标题
const formatMonthTitle = (group: HomeworkGroup): string => {
  const currentYear = new Date().getFullYear();
  if (group.year === currentYear) {
    return `${group.month}月`;
  }
  return `${group.year}年${group.month}月`;
};

// 格式化日期
const formatDate = (timestamp: number): string => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日 ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 获取课程系列数据
const fetchCourseSeriesData = async () => {
  try {
    const res = await getMyCoursesSeries();
    const data = res.getData();
    state.courseSeriesData = data;

    // 构建学习数据，包含"我的作业"和课程系列
    const studyData: StudyDataItem[] = [{ id: 0, name: "我的作业" }];

    // 添加课程系列作为标签
    if (data?.courseSeries) {
      data.courseSeries.forEach((series, index) => {
        studyData.push({
          id: index + 1, // 从1开始编号
          name: series.seriesName,
        });
      });
    }

    // 添加单独的课程
    if (data?.individualCourses) {
      data.individualCourses.forEach((course, index) => {
        studyData.push({
          id: 1000 + index, // 从1000开始编号，避免冲突
          name: course.name,
        });
      });
    }

    state.studyData = studyData;

    // 如果当前没有选中的标签，则选中第一个标签
    if (state.studyData.length > 0 && !state.activeStudyId) {
      state.activeStudyId = state.studyData[0].id;
    }

    return state.studyData;
  } catch (error) {
    console.error("获取课程系列数据失败:", error);
    // 回退到原来的方式
    return await fetchStudyDataFallback();
  }
};

// 回退方案：获取原来的学习数据
const fetchStudyDataFallback = async () => {
  try {
    const res = await getMyStudyData();
    const data = res.getData();
    if (Array.isArray(data)) {
      state.studyData = data;
      if (state.studyData.length > 0 && !state.activeStudyId) {
        state.activeStudyId = state.studyData[0].id;
      }
    } else {
      state.studyData = [];
    }
    return state.studyData;
  } catch (error) {
    console.error("获取学习数据失败:", error);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
    });
    state.studyData = [];
    return state.studyData;
  }
};

// 处理标签点击事件
const handleTagClick = (id: number) => {
  if (state.activeStudyId !== id) {
    state.activeStudyId = id;

    // 如果点击的是课程系列标签
    if (id > 0 && id < 1000 && state.courseSeriesData?.courseSeries) {
      const seriesIndex = id - 1;
      const series = state.courseSeriesData.courseSeries[seriesIndex];
      if (series) {
        state.selectedSeries = series;
        state.selectedCourseId = series.defaultCourse.id;
        state.selectedCourse = series.defaultCourse;
      }
    } else if (id >= 1000 && state.courseSeriesData?.individualCourses) {
      // 如果点击的是单独课程标签
      const courseIndex = id - 1000;
      const course = state.courseSeriesData.individualCourses[courseIndex];
      if (course) {
        state.selectedSeries = null;
        state.selectedCourseId = course.id;
        state.selectedCourse = course;
      }
    } else {
      // 点击的是"我的作业"
      state.selectedSeries = null;
      state.selectedCourseId = null;
      state.selectedCourse = null;
    }

    pagingRef.value?.reload();
  }
};

// 处理课程切换
const handleCourseSwitch = (courseId: number) => {
  if (state.selectedSeries && state.selectedCourseId !== courseId) {
    const course = state.selectedSeries.allCourses.find(
      (c) => c.id === courseId
    );
    if (course) {
      state.selectedCourseId = courseId;
      state.selectedCourse = course;
    }
  }
};

// 获取作业列表
const fetchHomeworkList = async (pageNo: number, pageSize: number) => {
  try {
    // 先获取课程系列数据
    await fetchCourseSeriesData();

    // 如果没有标签数据或者标签数据为空，则返回空数组
    if (!state.studyData || state.studyData.length === 0) {
      pagingRef.value?.complete([]);
      return;
    }

    // 只有当 activeStudyId 为 0 时才获取作业列表
    if (state.activeStudyId !== 0) {
      pagingRef.value?.complete([]);
      return;
    }

    const res = await getHomeworkList({
      pageNo,
      pageSize,
    });
    const data = res.getData();

    // 确保返回的是数组
    console.log("data", data);
    if (Array.isArray(data)) {
      pagingRef.value?.complete(data);
    } else {
      pagingRef.value?.complete([]);
    }
  } catch (error) {
    console.error("获取作业列表失败:", error);
    pagingRef.value?.complete(false);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
    });
  }
};

// 跳转到作业详情
const goToHomeworkDetail = (id: number, homeworkType: number) => {
  const path =
    homeworkType === 1
      ? "/homework-package/homework-detail/homework-detail"
      : "/homework-package/homework-detail-daily/homework-detail-daily";

  uni.navigateTo({
    url: `${path}?id=${id}&from=study`,
  });
};

// 监听登录状态变化，登录后主动刷新数据
watch(isLoggedIn, (newValue, oldValue) => {
  // 当从未登录变为已登录时，主动重新加载作业列表数据
  if (newValue && !oldValue) {
    pagingRef.value?.reload();
  }
});

// 分享给好友
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  return {
    title: "白熊作业广场",
    path: "/pages/homework-square/homework-square",
    imageUrl: "/static/icon.png",
  };
});

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: "白熊作业广场",
    query: "",
    imageUrl: "/static/icon.png",
  };
});
// #endif
</script>

<style lang="scss" scoped>
.study-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;

  .study-tags {
    display: flex;
    flex-wrap: wrap;
    padding: 12px 16px 4px;
    gap: 8px;

    .study-tag {
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 14px;
      background-color: #f0f0f0;
      color: #666;

      &.active {
        background-color: #ffe161;
        color: #333;
      }
    }
  }

  .course-content {
    padding: 12px 16px;

    .course-switch {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 16px;

      .course-option {
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 13px;
        background-color: #f0f0f0;
        color: #666;
        border: 1px solid #e0e0e0;

        &.active {
          background-color: #007aff;
          color: #fff;
          border-color: #007aff;
        }
      }
    }

    .course-detail {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;

      .course-info {
        text-align: center;

        .course-cover {
          width: 120px;
          height: 120px;
          border-radius: 8px;
          margin-bottom: 12px;
        }

        .course-name {
          display: block;
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }

        .course-desc {
          display: block;
          font-size: 14px;
          color: #666;
          line-height: 1.4;
        }
      }
    }
  }

  .homework-list {
    padding: 12px 16px;

    .month-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin: 8px 0;
    }

    .homework-item {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;

      .homework-info {
        .title-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .left {
            display: flex;
            align-items: flex-start;
            flex: 1;

            .iconfont {
              font-size: 20px;
              color: #fff200;
              margin-right: 8px;
              margin-top: 2px;
            }

            .title {
              font-size: 15px;
              color: #333;
              line-height: 1.4;
            }
          }

          .status {
            padding: 4px 12px;
            border-radius: 24px;
            font-size: 13px;

            &.submitted {
              background-color: #f0f0f0;
              color: #999999;
            }

            &.unsubmitted {
              background-color: #ffe161;
              color: #333333;
            }
          }
        }

        .date {
          font-size: 13px;
          color: #999;
          padding-left: 28px;
        }
      }
    }
  }
}
</style>
