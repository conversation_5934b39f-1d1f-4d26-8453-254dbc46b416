import { ResponseData } from "./api";
import { UploadFileInfo } from "./file";
import { HomeworkExercise } from "./homework-exercise";

declare namespace Homework {
  // 用户信息
  interface UserInfo {
    id: number;
    nickname: string;
    avatar: string;
  }

  // 老师评语
  interface Feedback {
    id: number;
    assigneeId: number;
    memberId: number;
    type: number;
    content: string;
    voiceUrl: string | null;
    voiceDuration: number | null;
    createTime: number;
    extUserInfo: UserInfo;
    tag?: string;
  }

  // 批改图片
  interface GradingImage {
    gradedUrl: string;
    originalUrl: string;
  }

  // 每日作业提交
  interface DailySubmission {
    homeworkId: number;
    exerciseId: null;
    studentId: number;
    dayNumber: number;
    assigneeId: number;
    submissionTime: number;
    contentText: string;
    contentImages: UploadFileInfo[];
    recordFileUrls: UploadFileInfo[];
    fileUrls: UploadFileInfo[];
    gradedTime: number | null;
    graded: boolean;
    grade: string | null;
    dayStatus: string | null;
    comment: string | null;
    id: number;
    createTime: number;
    gradeName: string | null;
    feedbacks: any | null;
    gradingImages: GradingImage[];
  }

  // 作业分配信息
  interface AssigneeInfo {
    homeworkId: number;
    memberId: number;
    assignTime: number;
    submitted: boolean;
    submissionTime: number;
    graded: boolean;
    gradedTime: number | null;
    graderId: number | null;
    id: number;
    createTime: number;
    extUserInfo: UserInfo | null;
    extSubmission: any | null;
    extDailySubmission: DailySubmission[];
    extFeedbackList: Feedback[];
  }

  // 班级信息
  interface ClassInfo {
    id: number;
    name: string;
  }

  // 作业详情
  interface HomeworkDetail {
    id: number;
    classId: number;
    title: string;
    lessonUnitName: string;
    description: string;
    homeworkType: number;
    contentImages: UploadFileInfo[];
    recordFileUrls: UploadFileInfo[] | null;
    fileUrls: UploadFileInfo[];
    visibleStatus: number;
    createTime: number;
    extSubmitAssigneesList: AssigneeInfo[];
    extUnsubmitAssigneesList: AssigneeInfo[];
    extMySubmitAssignees: AssigneeInfo;
    extAssigneesId: number;
    extExercises: HomeworkExercise[];
    extClassInfo: ClassInfo;
  }

  type HomeworkResponse = ResponseData<HomeworkDetail>;
}

export = Homework;
