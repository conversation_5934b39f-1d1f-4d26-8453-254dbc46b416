import { HomeworkExerciseSubmissionDetail } from "./homework-submission";

// 声明枚举
declare enum ExerciseCategory {
  CUSTOM = 0,
  WORD = 1,
}

declare enum QuestionType {
  WORD_RECOGNITION = 1,
  PICTURE_WORD_SELECTION = 2,
  AUDIO_PICTURE_SELECTION = 3,
  WORD_PICTURE_SELECTION = 4,
  AUDIO_SENTENCE_SELECTION = 5,
  WORD_PHONICS = 6,
}

export interface WordExampleSentence {
  imageUrl: string;
  sentence: string;
  audio_url: string;
}

export interface WordInfo {
  id: number;
  word: string;
  phonetic: string;
  partOfSpeech: string;
  syllable: string;
  definition: string;
  chineseMeaning: string;
  audioUrl: string;
  exampleSentences: WordExampleSentence[];
  imageUrl: string | null;
  textOptions: any | null;
  imageOptions: any | null;
  wordOptions?: Array<{
    id: number;
    word: string;
    phonetic: string;
    partOfSpeech: string;
    syllable: string;
    definition: string;
    chineseMeaning: string;
  }>;
}

export interface WordExerciseOriginData {
  wordRange: string;
  wordCount: number;
  wordIds: number[];
}

export interface WordExerciseData {
  originData: WordExerciseOriginData;
  words?: WordInfo[];
  questions?: Question[];
}

export interface Question {
  questionType: QuestionType;
  words: WordInfo;
}

export interface HomeworkExercise {
  id: number;
  homeworkId: number;
  dayNumber: number;
  exerciseType: "listening" | "vocabulary" | string;
  content: string;
  sort: number;
  submitType: string;
  exerciseCategory: ExerciseCategory;
  wordExerciseModel?: string;
  extraWordExerciseOriginData?: any;
  extraWordData?: WordExerciseData;
  extSubmissionDetail?: HomeworkExerciseSubmissionDetail;
}

// 导出枚举类型
export { ExerciseCategory, QuestionType };
