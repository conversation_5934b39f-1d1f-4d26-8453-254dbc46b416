import type { UploadFileInfo } from "./file";

export interface HomeworkExerciseSubmissionRequest {
  id?: number;
  exerciseId: number;
  homeworkId: number;
  contentText: string;
  contentImages?: UploadFileInfo[];
  recordFileUrls?: UploadFileInfo[];
  fileUrls?: UploadFileInfo[];
}

export interface HomeworkExerciseSubmissionDetail {
  id: number;
  submissionId: number;
  exerciseId: number;
  gradingStatus: string | null;
  contentText: string;
  contentImages: UploadFileInfo[];
  recordFileUrls: UploadFileInfo[];
  fileUrls: UploadFileInfo[];
  submitTime: number;
  exerciseCategory?: number;
  extraWordData?: {
    questions: Array<{
      questionType: number;
      words: {
        id: number;
        word: string;
        partOfSpeech: string;
        chineseMeaning: string;
      };
    }>;
  };
  wordEvaluationResult?: {
    isFinished: boolean;
    spentTime?: number;
    results: Array<{
      wordId: number;
      questionType: number;
      word: string;
      correct: boolean | null;
      readingScore: number | null;
      readingAudioUrl: string | null;
      recognitionStatus: "forget" | "fuzzy" | "remember";
    }>;
  };
}

export interface WordExerciseSubmissionRequest {
  exerciseId: number;
  questionType: number;
  wordId: number;
  word: string;
  recognitionStatus?: "forget" | "fuzzy" | "remember";
  index: number;
  answer?: string;
  correct?: boolean;
  score?: number;
  isFinish?: boolean;
  spendTime?: number;
}
