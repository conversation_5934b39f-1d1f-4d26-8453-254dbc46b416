// 登录凭证信息
export interface TokenInfo {
  userId: number;
  accessToken: string;
  refreshToken: string;
  expiresTime: number;
  firstLogin: boolean;
}

// 登录参数
export interface LoginParams {
  type: "password" | "sms" | "wechat";
  data: any;
}

// 微信登录参数
export interface WechatLoginParams {
  phoneCode: string;
  loginCode: string;
}

// 密码登录参数
export interface PasswordLoginParams {
  username: string;
  password: string;
}

// 短信登录参数
export interface SmsLoginParams {
  mobile: string;
  code: string;
}

// 用户信息
export interface UserInfo {
  avatar: string;
  nickname: string;
  mobile: string;
  [key: string]: any;
}

// 用户状态
export interface UserState {
  userInfo: UserInfo;
  userId: number;
  accessToken: string;
  refreshToken: string;
  expiresTime: number;
  firstLogin: boolean;
}

// 修改密码参数
export interface UpdatePasswordParams {
  oldPassword: string;
  password: string;
}
