declare interface Uni {
  $u: {
    http: {
      interceptors: {
        request: {
          use(
            onFulfilled?: (config: any) => Promise<any> | any,
            onRejected?: (error: any) => Promise<any>
          ): void;
        };
        response: {
          use(
            onFulfilled?: (response: any) => Promise<any> | any,
            onRejected?: (error: any) => Promise<any>
          ): void;
        };
      };
      request(config: any): Promise<any>;
    };
    toast(msg: string): void;
  };
}

declare interface UniNamespace {
  $u: Uni["$u"];
}
