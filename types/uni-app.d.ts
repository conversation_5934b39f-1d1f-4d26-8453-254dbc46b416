declare namespace UniApp {
  interface ShowToastOptions {
    title: string;
    icon?: "success" | "loading" | "none" | "error";
    image?: string;
    duration?: number;
    mask?: boolean;
    success?: () => void;
    fail?: () => void;
    complete?: () => void;
  }

  interface NavigateToOptions {
    url: string;
    success?: () => void;
    fail?: () => void;
    complete?: () => void;
  }
}

declare const uni: {
  showToast(options: UniApp.ShowToastOptions): void;
  navigateTo(options: UniApp.NavigateToOptions): void;
  $on(event: string, callback: Function): void;
  $off(event: string, callback: Function): void;
};

export {};
