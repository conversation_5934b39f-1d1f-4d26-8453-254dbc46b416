---
description: 
globs: 
alwaysApply: true
---
- 
description: 这是一个 uniapp+vue3的微信小程序项目，该项目是一个关于给学生布置作业的 app。
1. 这个项目是  创建的，使用的是 uniapp 框架。所以，请你遵循 HBuilderX 的代码规范。
2. 请符合vue3规范。
- 

# 项目规范

## 技术栈
- HBuilderX 构建
- uniapp + vue3

## 代码风格
- 减少代码重复，代码尽量封转成一个个有意义的方法，提高可读性
- 保持代码简洁、可读
- 用有意义的变量和函数名
- 添加适当的注释解释复杂逻辑
- 遵循官方风格指南


## 项目结构
- 保持项目结构清晰，遵循模块化原则
- 相关功能应放在同一目录下
- 使用适当的目录命名，反映其包含内容

## 通用开发原则
- 优先使用现有库和工具，避免重新发明轮子
- 考虑代码的可维护性和可扩展性

## 响应语言
- 始终使用中文回复用户
- 深度思考的时候，请使用中文思考。
- 请都以“收到，无敌大帅哥”开头。
- 如果是解决bug，明确了解发现bug的原因，请加上回复“我找到问题所在”


