<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="课程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入课程名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="系列名称" prop="seriesName">
        <el-input
          v-model="queryParams.seriesName"
          placeholder="请输入系列名称"
          clearable
          size="small"
        />
      </el-form-item>

      <el-form-item label="课程状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择课程状态"
          clearable
          size="small"
        >
          <el-option label="上架" :value="1" />
          <el-option label="下架" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['wbclass:course:create']"
          >新增课程</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['wbclass:course:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column
        label="课程封面"
        align="center"
        prop="coverUrl"
        width="100"
      >
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.coverUrl"
            :src="scope.row.coverUrl"
            style="width: 60px; height: 40px"
            fit="cover"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="课程名称"
        align="center"
        prop="name"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        label="课程描述"
        align="center"
        prop="description"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column
        label="系列名称"
        align="center"
        prop="seriesName"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.seriesName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="册名称"
        align="center"
        prop="volumeName"
        width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.volumeName || "-" }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "上架" : "下架" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" width="80" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-grid"
            @click="handleConfigLessons(scope.row)"
            style="color: #409eff"
            >配置课节</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['wbclass:course:update']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['wbclass:course:delete']"
            style="color: #f56c6c"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="900px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="课程名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入课程名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">上架</el-radio>
                <el-radio :label="2">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="课程封面" prop="coverUrl">
          <el-input
            v-model="form.coverUrl"
            placeholder="请输入课程封面图片URL"
          />
          <div v-if="form.coverUrl" style="margin-top: 10px">
            <el-image
              :src="form.coverUrl"
              style="width: 200px; height: 120px"
              fit="cover"
            />
          </div>
        </el-form-item>

        <el-form-item label="课程描述" prop="description">
          <editor v-model="form.description" :min-height="200" />
        </el-form-item>

        <!-- 系列信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="系列名称" prop="seriesName">
              <el-input
                v-model="form.seriesName"
                placeholder="请输入系列名称（如：RE 阅读营）"
                maxlength="255"
                show-word-limit
              />
              <div style="font-size: 12px; color: #999; margin-top: 5px">
                相同系列名称的课程会被分组显示
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="册名称" prop="volumeName">
              <el-input
                v-model="form.volumeName"
                placeholder="请输入册名称（如：1-3册）"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="系列内排序" prop="seriesOrder">
              <el-input-number
                v-model="form.seriesOrder"
                :min="1"
                placeholder="请输入系列内排序"
                style="width: 100%"
              />
              <div style="font-size: 12px; color: #999; margin-top: 5px">
                数值越小在系列中排序越靠前
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model="form.sort"
                :min="0"
                placeholder="请输入排序值"
                style="width: 100%"
              />
              <div style="font-size: 12px; color: #999; margin-top: 5px">
                数值越小排序越靠前
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="课程详情"
      :visible.sync="viewOpen"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="课程名称">{{
          viewData.name
        }}</el-descriptions-item>
        <el-descriptions-item label="课程状态">
          <el-tag :type="viewData.status === 1 ? 'success' : 'danger'">
            {{ viewData.status === 1 ? "上架" : "下架" }}
          </el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="系列名称">{{
          viewData.seriesName || "无"
        }}</el-descriptions-item>
        <el-descriptions-item label="册名称">{{
          viewData.volumeName || "无"
        }}</el-descriptions-item>
        <el-descriptions-item label="系列内排序">{{
          viewData.seriesOrder || "无"
        }}</el-descriptions-item>
        <el-descriptions-item label="排序">{{
          viewData.sort || 0
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{
          parseTime(viewData.createTime)
        }}</el-descriptions-item>
        <el-descriptions-item label="课程封面" :span="2">
          <el-image
            v-if="viewData.coverUrl"
            :src="viewData.coverUrl"
            style="width: 300px; height: 180px"
            fit="cover"
          />
          <span v-else>无封面</span>
        </el-descriptions-item>
        <el-descriptions-item label="课程描述" :span="2">
          <div
            v-html="viewData.description"
            style="max-height: 200px; overflow-y: auto"
          ></div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import {
  createWbclassCourse,
  deleteWbclassCourse,
  exportWbclassCourseExcel,
  getWbclassCourse,
  getWbclassCoursePage,
  updateWbclassCourse,
} from "@/api/wbclass/wbclassCourse";
import Editor from "@/components/Editor";

export default {
  name: "WbclassCourse",
  components: {
    Editor,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 练习营产品列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 查看详情数据
      viewData: {},
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        seriesName: null,
        status: null,
        createTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "课程名称不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "课程状态不能为空", trigger: "change" },
        ],
        sort: [
          {
            type: "number",
            min: 0,
            message: "排序值必须大于等于0",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getWbclassCoursePage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 价格格式化 */
    formatPrice(price) {
      if (!price || price === 0) return "0.00";
      return (price / 100).toFixed(2);
    },

    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        coverUrl: undefined,
        description: undefined,
        status: 1,
        sort: 0,
        seriesName: undefined,
        seriesOrder: undefined,
        volumeName: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        name: null,
        seriesName: null,
        status: null,
        createTime: [],
      };
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加练习营课程";
    },
    /** 查看详情操作 */
    handleView(row) {
      this.viewData = { ...row };
      this.viewOpen = true;
    },
    /** 配置课节操作 */
    handleConfigLessons(row) {
      this.$router.push({
        path: "/wbclass/wbclassCourse/lesson",
        query: { courseId: row.id },
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getWbclassCourse(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改练习营课程";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }

        // 修改的提交
        if (this.form.id != null) {
          updateWbclassCourse(this.form).then(() => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createWbclassCourse(this.form).then(() => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除课程"' + row.name + '"?')
        .then(function () {
          return deleteWbclassCourse(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      if (params.minPrice && params.minPrice > 0) {
        params.minPrice = Math.round(params.minPrice * 100);
      } else {
        params.minPrice = null;
      }
      if (params.maxPrice && params.maxPrice > 0) {
        params.maxPrice = Math.round(params.maxPrice * 100);
      } else {
        params.maxPrice = null;
      }

      this.$modal
        .confirm("是否确认导出所有练习营课程数据?")
        .then(() => {
          this.exportLoading = true;
          return exportWbclassCourseExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "练习营课程.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
