{
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/home/<USER>",
      "style": {
        "navigationBarTitleText": "首页",
        "enableShareAppMessage": true,
        "enableShareTimeline": true
      }
    },
    {
      "path": "pages/homework-square/homework-square",
      "style": {
        "navigationBarTitleText": "作业广场",
        "enableShareAppMessage": true,
        "enableShareTimeline": true
      }
    },
    {
      "path": "pages/study/study",
      "style": {
        "navigationBarTitleText": "我的作业",
        "enableShareAppMessage": true,
        "enableShareTimeline": true
      }
    },
    {
      "path": "pages/mine/mine",
      "style": {
        "navigationBarTitleText": "我的",
        "enableShareAppMessage": true,
        "enableShareTimeline": true
      }
    },
    {
      "path": "pages/webview/webview",
      "style": {
        "navigationBarTitleText": "网页浏览"
      }
    },
    {
      "path": "pages/product-detail/product-detail",
      "style": {
        "navigationBarTitleText": "商品详情",
        "enableShareAppMessage": true,
        "enableShareTimeline": true
      }
    },
    {
      "path": "pages/course-detail/course-detail",
      "style": {
        "navigationBarTitleText": "课程详情",
        "enableShareAppMessage": true,
        "enableShareTimeline": true
      }
    }
  ],
  "subPackages": [
    {
      "root": "homework-package",
      "pages": [
        {
          "path": "homework-detail-daily/homework-detail-daily",
          "style": {
            "navigationBarTitleText": "每日作业",
            "enablePullDownRefresh": false,
            "enableShareAppMessage": true,
            "enableShareTimeline": true
          }
        }
      ]
    },
    {
      "root": "user-package",
      "pages": [
        {
          "path": "login/social",
          "style": {
            "navigationBarTitleText": "登录"
          }
        },
        {
          "path": "login/password",
          "style": {
            "navigationBarTitleText": "账号密码登录"
          }
        },
        {
          "path": "login/mobile",
          "style": {
            "navigationBarTitleText": "手机号登录"
          }
        },
        {
          "path": "settings/settings",
          "style": {
            "navigationBarTitleText": "设置"
          }
        },
        {
          "path": "user-info/index",
          "style": {
            "navigationBarTitleText": "完善资料"
          }
        },
        {
          "path": "update-password/update-password",
          "style": {
            "navigationBarTitleText": "修改密码"
          }
        }
      ]
    },
    {
      "root": "common-package",
      "pages": [
        {
          "path": "audio-player/index",
          "style": {
            "navigationBarTitleText": "音频播放"
          }
        }
      ]
    },
    {
      "root": "sub-exercise",
      "pages": [
        {
          "path": "word-exercise/word-recognition-review",
          "style": {
            "navigationBarTitleText": "单词复习",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "word-exercise/word-recognition-review-result",
          "style": {
            "navigationBarTitleText": "单词复习",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "word-exercise/word-play",
          "style": {
            "navigationBarTitleText": "单词选择题",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "word-exercise/word-play-result",
          "style": {
            "navigationBarTitleText": "单词练习",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "word-dictation/word-dictation",
          "style": {
            "navigationBarTitleText": "单词听写",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "word-dictation/word-dictation-result",
          "style": {
            "navigationBarTitleText": "听写结果",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "article-follow/article-follow",
          "style": {
            "navigationBarTitleText": "文章跟读",
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "preloadRule": {
    "pages/home/<USER>": {
      "network": "all",
      "packages": ["common-package"]
    },
    "pages/homework-square/homework-square": {
      "network": "all",
      "packages": ["common-package", "homework-package"]
    },
    "pages/study/study": {
      "network": "all",
      "packages": ["common-package", "homework-package"]
    },
    "pages/mine/mine": {
      "network": "all",
      "packages": ["common-package", "user-package"]
    }
  },
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#4c4c5d",
    "backgroundColor": "#ffffff",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "首页",
        "iconPath": "static/tabbar/compass.png",
        "selectedIconPath": "static/tabbar/compass-fill.png"
      },
      {
        "pagePath": "pages/homework-square/homework-square",
        "text": "作业广场",
        "iconPath": "static/tabbar/tab-compass.png",
        "selectedIconPath": "static/tabbar/tab-compass-fill.png"
      },
      {
        "pagePath": "pages/study/study",
        "text": "我的作业",
        "iconPath": "static/tabbar/tab-book-open.png",
        "selectedIconPath": "static/tabbar/tab-book-open-fill.png"
      },
      {
        "pagePath": "pages/mine/mine",
        "text": "我的",
        "iconPath": "static/tabbar/tab-smile.png",
        "selectedIconPath": "static/tabbar/tab-smile-fill.png"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "白熊作业",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8",
    "rpxCalcMaxDeviceWidth": 0,
    "rpxCalcBaseDeviceWidth": 750
  },
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uni-(.*)": "@/uni_modules/uni-$1/components/uni-$1/uni-$1.vue",
      "uni-popup": "@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue",
      "^lottie-(.*)": "@/components/lottie-$1/index.vue",
      "^zero-(.*)": "@/uni_modules/zero-$1/components/zero-$1/zero-$1.vue"
    }
  }
}
