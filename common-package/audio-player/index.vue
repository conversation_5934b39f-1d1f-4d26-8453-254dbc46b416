<template>
  <view class="audio-player">
    <view class="player-container">
      <view class="title">{{ state.title }}</view>
      <view class="cover-image">
        <text
          class="iconfont icon-audio"
          :class="{ 'is-playing': state.isPlaying }"
        ></text>
      </view>

      <view class="controls">
        <view class="progress-bar">
          <slider
            :value="state.progress"
            @change="handleSliderChange"
            @changing="handleSliderChanging"
            step="0.1"
            block-size="12"
            activeColor="#f63971"
          />
          <view class="time-info">
            <text>{{ state.currentTime }}</text>
            <text>{{ state.duration }}</text>
          </view>
        </view>

        <view class="control-buttons">
          <view class="speed-btn" @click="handleSpeedChange">
            <text>{{ state.playbackSpeed }}x</text>
          </view>
          <view class="play-btn" @click="handlePlayPause">
            <text
              class="iconfont"
              :class="
                state.isPlaying ? 'icon-pause-circle' : 'icon-play-circle'
              "
            ></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { uploadObjectLog } from "@/utils/log/uploader";
import { onMounted, onUnmounted, reactive, ref } from "vue";

interface State {
  audioUrl: string;
  title: string;
  isPlaying: boolean;
  progress: number;
  currentTime: string;
  duration: string;
  isDragging: boolean;
  hasDuration: boolean;
  playbackSpeed: number;
  hasEnded: boolean;
  loadRetried: boolean;
}

const audioContext = ref<UniApp.InnerAudioContext | null>(null);
const playbackSpeeds = [1, 1.25, 1.5, 2];

const state = reactive<State>({
  audioUrl: "",
  title: "",
  isPlaying: false,
  progress: 0,
  currentTime: "00:00",
  duration: "00:00",
  isDragging: false,
  hasDuration: false,
  playbackSpeed: 1,
  hasEnded: false,
  loadRetried: false,
});

// 格式化时间
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${String(minutes).padStart(2, "0")}:${String(
    remainingSeconds
  ).padStart(2, "0")}`;
};

// 切换播放速度
const handleSpeedChange = () => {
  const currentIndex = playbackSpeeds.indexOf(state.playbackSpeed);
  const nextIndex = (currentIndex + 1) % playbackSpeeds.length;
  state.playbackSpeed = playbackSpeeds[nextIndex];

  if (audioContext.value) {
    audioContext.value.playbackRate = state.playbackSpeed;
  }
};

// 初始化音频上下文
const initAudioContext = () => {
  console.log("initAudioContext", state.audioUrl);
  if (audioContext.value) {
    try {
      audioContext.value.stop();
      audioContext.value.destroy();
    } catch (err: any) {
      uploadObjectLog("Error destroying previous audio context:", {
        ...err,
      });
      console.error("Error destroying previous audio context:", err);
    }
  }

  try {
    const audio = uni.createInnerAudioContext();
    console.log("Created new audio context");

    // 先保存音频上下文，确保后续事件处理能获取到
    audioContext.value = audio;

    // 重置状态
    state.progress = 0;
    state.currentTime = "00:00";
    state.hasEnded = false;
    state.isDragging = false;

    // 设置音频属性（src需要放在事件绑定之后设置）
    audio.volume = 1;
    audio.playbackRate = state.playbackSpeed;

    // 先绑定事件，再设置src
    audio.onError((res) => {
      console.error(
        "Audio Error:",
        res,
        "错误代码:",
        res.errCode,
        "错误信息:",
        res.errMsg,
        "音频地址:",
        state.audioUrl
      );
      // 使用新方法上报错误对象
      uploadObjectLog("001音频播放器错误", {
        ...res,
        audioUrl: state.audioUrl,
        title: state.title,
        duration: state.duration,
        playbackSpeed: state.playbackSpeed,
      });
      const errMsg = res.errMsg || "未知错误";
      const errCode = res.errCode || "未知错误码";

      // 首次加载失败的特殊处理 - 尝试重新加载
      if (!state.loadRetried) {
        state.loadRetried = true;
        console.log("First load failed, retrying...");
        setTimeout(() => {
          // 尝试再次初始化
          initAudioContext();
        }, 1000);
        return;
      }

      uni.showToast({
        title: `音频加载失败: ${errCode} - ${errMsg}`,
        icon: "none",
        duration: 3000,
      });
      handleError();
    });

    audio.onCanplay(() => {
      console.log("Audio can play now", {
        duration: audio.duration,
        currentTime: audio.currentTime,
        paused: audio.paused,
        src: audio.src,
        volume: audio.volume,
        buffered: audio.buffered,
      });

      // 更新持续时间，确保在能播放时正确设置
      if (
        audio.duration &&
        audio.duration !== Infinity &&
        !isNaN(audio.duration)
      ) {
        state.duration = formatTime(audio.duration);
        state.hasDuration = true;
      }

      // 确保自动播放
      if (!state.hasEnded) {
        audio.play();
      }

      // 标记加载成功
      state.loadRetried = false;
    });

    // 设置音频源（放在事件绑定后面）
    console.log("Setting audio source:", state.audioUrl);
    audio.src = state.audioUrl;

    // 其他事件绑定不变
    audio.onPlay(() => {
      console.log("Audio started playing", {
        currentTime: audio.currentTime,
        duration: audio.duration,
        paused: audio.paused,
      });
      state.isPlaying = true;
      state.hasEnded = false;

      // 强制更新一次进度
      updateProgress();
    });

    audio.onPause(() => {
      console.log("Audio paused", {
        currentTime: audio.currentTime,
        duration: audio.duration,
        paused: audio.paused,
      });
      state.isPlaying = false;
    });

    audio.onTimeUpdate(() => {
      if (!state.isDragging) {
        updateProgress();
      }

      // 持续检查并更新音频持续时间
      if (
        audio.duration &&
        audio.duration !== Infinity &&
        !isNaN(audio.duration)
      ) {
        // 只有当持续时间有变化时才更新
        const newDuration = formatTime(audio.duration);
        if (state.duration !== newDuration) {
          state.duration = newDuration;
          state.hasDuration = true;
        }
      }
    });

    audio.onEnded(() => {
      console.log("Audio ended");
      state.isPlaying = false;
      state.progress = 100;
      state.currentTime = state.duration;
      state.hasEnded = true;

      // 不需要调用stop，这会导致无法重新播放
      // audio.stop();
    });

    audio.onStop(() => {
      console.log("Audio stopped");
      // 在某些情况下可能需要重置状态
      if (!state.hasEnded) {
        state.isPlaying = false;
      }
    });

    console.log("Audio context initialized successfully");
  } catch (err: any) {
    uploadObjectLog("Error initializing audio context:", {
      ...err,
    });
    console.error("Error initializing audio context:", err);
    handleError();
  }
};

// 更新进度
const updateProgress = () => {
  const audio = audioContext.value;
  if (audio?.duration && audio.duration > 0 && !isNaN(audio.duration)) {
    // 保留一位小数来平滑进度条
    state.progress = parseFloat(
      ((audio.currentTime / audio.duration) * 100).toFixed(1)
    );
    state.currentTime = formatTime(audio.currentTime);

    // 检查是否接近结束但未触发onEnded (防止一些平台的onEnded事件不可靠)
    if (audio.currentTime >= audio.duration - 0.5 && !state.hasEnded) {
      state.progress = 100;
      state.currentTime = state.duration;
      state.hasEnded = true;
      state.isPlaying = false;
    }
  }
};

// 处理播放/暂停
const handlePlayPause = () => {
  console.log("handlePlayPause clicked", {
    audioContext: audioContext.value,
    isPlaying: state.isPlaying,
    audioSrc: audioContext.value?.src,
    hasEnded: state.hasEnded,
  });

  const audio = audioContext.value;
  if (!audio) {
    console.error("No audio context available");
    return;
  }

  try {
    if (state.isPlaying) {
      console.log("Attempting to pause audio...");
      audio.pause();
    } else {
      console.log("Attempting to play audio...");

      // 如果已经结束，需要重新开始播放
      if (state.hasEnded) {
        audio.seek(0);
        state.hasEnded = false;
        state.progress = 0;
        state.currentTime = "00:00";
      }

      // 重置音频上下文如果需要
      if (!audio.src || audio.src !== state.audioUrl) {
        console.log("Reinitializing audio context...");
        initAudioContext();
        return;
      }

      // 尝试播放
      try {
        audio.play();
        console.log("Play command issued successfully");
      } catch (playErr: any) {
        uploadObjectLog("Error playing audio:", {
          ...playErr,
        });
        console.error("Error playing audio:", playErr);
        handleError();
      }
    }
  } catch (err: any) {
    uploadObjectLog("Error in handlePlayPause:", {
      ...err,
    });
    console.error("Error in handlePlayPause:", err);
    handleError();
  }
};

// 处理滑块改变
const handleSliderChange = (e: any) => {
  const audio = audioContext.value;
  if (audio) {
    const position = e.detail.value / 100;
    const time = position * audio.duration;

    // 立即更新当前时间显示，确保用户体验一致
    state.currentTime = formatTime(time);
    state.progress = e.detail.value;

    // 设置音频位置
    audio.seek(time);

    // 如果拖动到结尾，标记为结束状态
    if (position >= 0.99) {
      state.hasEnded = true;
      state.isPlaying = false;
    } else {
      // 如果之前已结束且没有播放，则重置结束状态
      if (state.hasEnded) {
        state.hasEnded = false;
      }
    }

    state.isDragging = false;
  }
};

// 处理滑块拖动中
const handleSliderChanging = (e: any) => {
  state.isDragging = true;

  // 实时更新时间显示，提高用户体验
  const audio = audioContext.value;
  if (audio && audio.duration) {
    const position = e.detail.value / 100;
    const time = position * audio.duration;
    state.currentTime = formatTime(time);
    state.progress = e.detail.value;
  }
};

// 处理错误
const handleError = () => {
  // 先停止任何正在播放的音频
  if (audioContext.value) {
    try {
      audioContext.value.stop();
      audioContext.value.destroy();
      audioContext.value = null;
    } catch (err: any) {
      uploadObjectLog("Error stopping audio:", {
        ...err,
      });
      console.error("Error stopping audio:", err);
    }
  }

  // 如果已经重试过，才显示错误并退出
  if (state.loadRetried) {
    uni.showToast({
      title: "音频加载失败",
      icon: "none",
    });
  }
};

// 页面加载
onMounted(() => {
  console.log("onMounted");
  uni.showLoading({
    title: "加载中...",
  });

  // 添加延迟确保页面完全加载
  setTimeout(() => {
    const pages = getCurrentPages();
    if (!pages || pages.length === 0) {
      console.error("No pages available");
      uni.hideLoading();
      handleError();
      return;
    }

    const page = pages[pages.length - 1] as any;
    if (!page) {
      console.error("Cannot get current page");
      uni.hideLoading();
      handleError();
      return;
    }

    // 根据环境获取options (兼容不同平台)
    let options;
    if (page.options) {
      options = page.options;
    } else if (page.$page && page.$page.options) {
      options = page.$page.options;
    } else if (page.$vm && page.$vm.$options) {
      options = page.$vm.$options;
    } else {
      options = {};
    }

    console.log("page options:", options);

    if (options?.url) {
      try {
        state.audioUrl = decodeURIComponent(options.url);
        state.title = decodeURIComponent(options.title || "音频播放");
        state.hasDuration = !!options.duration;
        if (state.hasDuration && options.duration) {
          state.duration = formatTime(Number(options.duration));
        }
        console.log("Audio URL:", state.audioUrl);
        console.log("Audio title:", state.title);

        // 确保页面已经准备好
        uni.hideLoading();
        initAudioContext();
      } catch (err: any) {
        uploadObjectLog("Error processing options:", {
          ...err,
        });
        console.error("Error processing options:", err);
        uni.hideLoading();
        handleError();
      }
    } else {
      console.error("No URL provided");
      uni.hideLoading();
      handleError();
    }
  }, 1000); // 延长延迟时间到1000ms
});

// 页面卸载
onUnmounted(() => {
  try {
    const audio = audioContext.value;
    if (audio) {
      audio.stop();
      audio.destroy();
      audioContext.value = null;
    }
  } catch (err: any) {
    uploadObjectLog("Error cleaning up audio:", {
      ...err,
    });
    console.error("Error cleaning up audio:", err);
  }
});
</script>

<style lang="scss" scoped>
.audio-player {
  min-height: 100vh;
  background-color: #fff;
  padding: 40rpx;

  .player-container {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 4rpx 24rpx rgba(246, 57, 113, 0.1);

    .title {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
      text-align: center;
      margin-bottom: 30rpx;
      padding: 0 40rpx;
    }

    .cover-image {
      width: 400rpx;
      height: 400rpx;
      margin: 0 auto 60rpx;
      background: rgba(246, 57, 113, 0.05);
      border-radius: 200rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;

      &::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        border: 4rpx solid #f63971;
        border-radius: 50%;
        animation: rotate 10s linear infinite;
        opacity: 0;
      }

      .icon-audio {
        font-size: 120rpx;
        color: #f63971;
        transition: all 0.3s ease;

        &.is-playing + &::after {
          opacity: 1;
        }
      }
    }

    .controls {
      .progress-bar {
        margin-bottom: 40rpx;

        .time-info {
          display: flex;
          justify-content: space-between;
          font-size: 24rpx;
          color: #999;
          margin-top: 10rpx;
        }
      }

      .control-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 40rpx;

        .speed-btn {
          padding: 12rpx 24rpx;
          background: rgba(246, 57, 113, 0.1);
          border-radius: 32rpx;
          font-size: 24rpx;
          color: #f63971;
          cursor: pointer;
        }

        .play-btn {
          padding: 20rpx;
          cursor: pointer;

          .iconfont {
            font-size: 80rpx;
            color: #f63971;
          }
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
