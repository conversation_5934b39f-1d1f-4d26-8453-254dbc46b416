import { ResponseData } from "@/types/api";
import type { HomeworkExercise } from "@/types/homework-exercise";
import http from "@/utils/http/request";

/**
 * Get homework exercise detail
 * @param id - Exercise ID
 * @returns Promise with exercise detail data
 */
export function getHomeworkExerciseDetail(
  id: number
): Promise<ResponseData<HomeworkExercise>> {
  return http.get(`/wbclass/homework-exercise/get?id=${id}`);
}
