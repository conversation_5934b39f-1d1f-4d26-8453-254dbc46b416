import http from "@/utils/http/request";

// 分页参数接口
export interface PageParams {
  pageNo: number;
  pageSize: number;
}

// 课程分页响应接口
export interface CoursePageResult {
  list: CourseItem[];
  total: number;
  extraInfo?: any;
}

// API响应接口
export interface ApiResponse<T> {
  code: number;
  data: T;
  msg: string;
}

// 课程项接口
export interface CourseItem {
  id: number;
  name: string;
  coverUrl: string;
  description: string;
  shortDescription?: string;
  originalPrice: number;
  salePrice: number;
  status: number;
  salesCount: number;
  viewCount: number;
  createTime: number;
}

// 课程详情接口
export interface CourseDetail {
  id: number;
  name: string;
  coverUrl: string;
  description: string;
  shortDescription?: string;
  originalPrice: number;
  salePrice: number;
  status: number;
  salesCount: number;
  viewCount: number;
  createTime: number;
}

/**
 * 获取课程列表分页
 */
export function getCoursePage(params: PageParams) {
  return http.get<CoursePageResult>("/wbclass/course/page", params);
}

/**
 * 获取课程详情
 */
export function getCourseDetail(id: number) {
  return http.get<CourseDetail>(`/wbclass/course/get?id=${id}`);
}
