import type Homework from "@/types/homework";
import http from "@/utils/http/request";

// 分页参数接口
interface PageParams {
  pageNo: number;
  pageSize: number;
}

// 分页响应接口
interface PageResult<T> {
  list: T[];
  total: number;
  pageNo: number;
  pageSize: number;
}

// 学习数据项接口
export interface StudyDataItem {
  id: number;
  name: string;
}

// 作业统计接口
interface HomeworkStatistics {
  totalCount: number;
  completedCount: number;
  uncompletedCount: number;
}

// 作业配置接口
export interface HomeworkConf {
  vcount: number;
}

// 作业提交数据接口
interface HomeworkSubmissionData {
  id?: number;
  assigneeId: number;
  contentText: string;
  contentImages: string[];
  fileUrls: string[];
  recordFileUrls: string[];
}

// 作业评分数据接口
interface HomeworkGradeData {
  submissionId: number;
  grade: "excellent" | "good" | "fair" | "fail" | "revise";
  feedbacks: Array<{
    type: number;
    content: string;
  }>;
}

// 作业广场项目接口
export interface HomeworkSquareItem {
  id: number;
  classId: number;
  title: string;
  description: string | null;
  homeworkType: number;
  contentImages: Array<{
    url: string;
    name: string;
  }> | null;
  recordFileUrls: null;
  fileUrls: Array<{
    name: string;
    size?: string;
    url: string;
    uid?: string;
    status?: string;
    type?: string;
  }> | null;
  visibleStatus: number;
  createTime: number;
  extSubmitAssigneesList: null;
  extUnsubmitAssigneesList: null;
  extMySubmitAssignees: null;
  extAssigneesId: number | null;
  extExercises: null;
}

export interface HomeworkListParams {
  pageNo: number;
  pageSize: number;
}

export interface SubmitHomeworkParams {
  homeworkId: number;
  answers: Array<{
    questionId: number;
    answer: string;
  }>;
}

/**
 * 获取班级作业列表
 */
export function getHomeworkList(params: HomeworkListParams) {
  return http.get<PageResult<any>>("/edusys/app-wbclass-homework/list", params);
}

/**
 * 获取作业详情
 */
export function getHomeworkDetail(
  homeworkId: number
): Promise<Homework.HomeworkResponse> {
  return http.get(`/edusys/app-wbclass-homework/get?id=${homeworkId}`);
}

/**
 * 创建或更新作业提交
 */
export function createOrUpdateHomeworkSubmission(data: HomeworkSubmissionData) {
  return http.post("/edusys/wbclass-homework-submissions/createOrUpdate", data);
}

/**
 * 提交作业评分
 */
export function gradeHomework(data: HomeworkGradeData) {
  return http.post("/edusys/wbclass-homework-assignees/grade", data);
}

/**
 * 删除作业评论
 */
export function deleteHomeworkFeedback(id: number) {
  return http.delete("/wbclass/homework-feedback/delete", { id });
}

/**
 * 获取作业广场列表
 */
export function getHomeworkSquare(params: PageParams) {
  return http.get<PageResult<HomeworkSquareItem>>(
    "/edusys/app-wbclass-homework/square",
    params
  );
}

/**
 * 获取作业广场作业详情
 */
export function getHomeworkSquareDetail(id: number) {
  return http.get("/edusys/app-wbclass-homework/square/get", { id });
}

/**
 * 领取作业广场作业
 */
export function receiveSquareHomework(id: number) {
  return http.post("/edusys/app-wbclass-homework/square/receive", { id });
}

/**
 * 获取作业统计数据
 */
export function getHomeworkStatistics() {
  return http.get<HomeworkStatistics>(
    "/edusys/app-wbclass-homework/statistics"
  );
}

/**
 * 获取作业配置
 */
export function getHomeworkConf() {
  return http.get<HomeworkConf>("/edusys/app-wbclass-homework/conf");
}

// 提交作业
export function submitHomework(data: SubmitHomeworkParams) {
  return http.post("/homework/submit", data);
}

/**
 * 获取我的学习数据
 */
export function getMyStudyData() {
  return http.get<StudyDataItem[]>("/edusys/wbclass-study/get-my-study-data");
}
