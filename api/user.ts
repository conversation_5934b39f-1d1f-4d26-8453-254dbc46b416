import ENV_CONFIG from "@/config";
import { ResponseData } from "@/types/api";
import http from "@/utils/http/request";
import { createResponseData } from "@/utils/http/response";

export interface UserInfo {
  id: number;
  nickname: string;
  avatar: string;
  mobile: string;
  [key: string]: any;
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return http.get<UserInfo>("/member/user/get");
}

/**
 * 更新用户头像
 */
export function updateAvatar(filePath: string): Promise<ResponseData<any>> {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: `${ENV_CONFIG.API_URL}/member/user/update-avatar`,
      filePath,
      name: "avatarFile",
      header: {
        Authorization: `Bearer ${uni.getStorageSync("ACCESS_TOKEN")}`,
      },
      success: (uploadRes) => {
        try {
          const res = JSON.parse(uploadRes.data);
          resolve(createResponseData(res.code || 0, res.data, res.msg));
        } catch (e) {
          reject(createResponseData(500, null, "上传失败"));
        }
      },
      fail: (error) => {
        reject(createResponseData(500, null, error.errMsg || "上传失败"));
      },
    });
  });
}

/**
 * 更新用户昵称
 */
export function updateNickname(nickname: string) {
  return http.put("/member/user/update-nickname", { nickname });
}

/**
 * 更新用户信息
 */
export function updateUserInfo(data: Partial<UserInfo>) {
  return http.put("/member/user/update-info", data);
}
