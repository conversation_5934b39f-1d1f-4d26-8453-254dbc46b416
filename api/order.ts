import http from "@/utils/http/request";

// 订单商品项接口
export interface OrderItem {
  /** 商品 SKU 编号 */
  skuId: number;
  /** 商品 SKU 购买数量 */
  count: number;
}

// 创建订单请求参数
export interface AppTradeOrderCreateReqVO {
  /** 收件地址编号 */
  addressId: number;
  /** 优惠券编号 */
  couponId?: number;
  /** 备注 */
  remark?: string;
  /** 是否来自购物车 */
  fromCart: boolean;
  /** 终端类型 */
  terminal: number;
  /** 订单商品项列表 */
  items: OrderItem[];
}

// 订单创建响应
export interface TradeOrderCreateRespVO {
  /** 订单编号 */
  orderId: number;
  /** 支付订单编号 */
  payOrderId: number;
}

// 确认创建订单信息响应
export interface AppTradeOrderGetCreateInfoRespVO {
  /** 商品信息 */
  spuId: number;
  skuId: number;
  count: number;
  /** 价格信息 */
  price: number;
  totalPrice: number;
  /** 地址信息 */
  addressId?: number;
  /** 优惠券信息 */
  couponId?: number;
}

/**
 * 基于商品，确认创建订单
 */
export function getTradeOrderCreateInfo(params: {
  skuId: number;
  count: number;
  couponId?: number;
}) {
  return http.get<AppTradeOrderGetCreateInfoRespVO>(
    "/trade/order/get-create-info",
    params
  );
}

/**
 * 创建订单
 */
export function createTradeOrder(data: AppTradeOrderCreateReqVO) {
  return http.post<number>("/trade/order/create", data);
}
