import request from "@/utils/http/request";

/**
 * 批改默写作业请求参数
 */
export interface AppWordOcrReqVO {
  /** 图片URL */
  imageUrl: string;
  /** 正确答案数组 */
  answers: string[];
}

/**
 * 批改默写作业响应参数
 */
export interface AppWordOcrRespVO {
  /** 正确识别的单词列表 */
  correctWords: CorrectWord[];
  /** 错误识别的单词列表 */
  wrongWords: WrongWord[];
  /** 批改后的图片地址 */
  correctedImageUrl: string;
}

/**
 * 正确识别的单词
 */
export interface CorrectWord {
  /** OCR识别出的文本 */
  ocrText: string;
  /** 正确的答案 */
  correctAnswer: string;
  /** OCR识别的置信度 */
  confidence: number;
}

/**
 * 错误识别的单词
 */
export interface WrongWord {
  /** OCR识别出的文本 */
  ocrText: string;
  /** 正确的答案 */
  correctAnswer: string;
  /** OCR识别的置信度 */
  confidence: number;
}

/**
 * 批改默写作业
 */
export function gradeDictation(data: AppWordOcrReqVO) {
  return request.post<AppWordOcrRespVO>("/wbclass/words/grade-dictation", data);
}

/**
 * 评估单词发音请求参数
 */
export interface WordPronunciationEvaluateReqVO {
  /** 需要评估的文本 */
  text: string;
  /** 语音文件URL */
  voiceUrl: string;
}

/**
 * 评估单词发音响应参数
 */
export interface WordPronunciationEvaluateRespVO {
  /** 总分 */
  totalScore: number;
  /** 发音准确度 */
  pronunciation: number;
  /** 流利度 */
  fluency: number;
  /** 完整度 */
  integrity: number;
  /** 音调得分 */
  tone: number;
}

/**
 * 评估单词发音
 * @param data 请求参数
 * @returns 评估结果
 */
export function evaluateWordPronunciation(
  data: WordPronunciationEvaluateReqVO
) {
  return request.post<WordPronunciationEvaluateRespVO>(
    "/wbclass/words/evaluate",
    data
  );
}
