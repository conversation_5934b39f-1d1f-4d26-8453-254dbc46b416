import type {
  PasswordLoginParams,
  SmsLoginParams,
  TokenInfo,
  UpdatePasswordParams,
  WechatLoginParams,
} from "@/types/auth";
import http from "@/utils/http/request";

/**
 * 微信小程序登录
 */
export function weixinMiniAppLogin(data: WechatLoginParams) {
  return http.post<TokenInfo>("/member/auth/weixin-mini-app-login", data);
}

/**
 * 账号密码登录
 */
export function passwordLogin(data: PasswordLoginParams) {
  return http.post<TokenInfo>("/member/auth/login", data);
}

/**
 * 手机验证码登录
 */
export function smsLogin(data: SmsLoginParams) {
  return http.post<TokenInfo>("/member/auth/sms-login", data);
}

/**
 * 退出登录
 */
export function logout() {
  return http.post("/member/auth/logout");
}

/**
 * 修改密码
 */
export function updatePassword(data: UpdatePasswordParams) {
  return http.post<Boolean>("/member/auth/update-password", data);
}

/**
 * 刷新令牌
 */
export function refreshToken(refreshToken: string) {
  return http.post<TokenInfo>("/member/auth/refresh-token", { refreshToken });
}

//发送手机验证码
export function sendSmsCode(data: { mobile: string; code: string }) {
  return http.post<TokenInfo>("/member/auth/refresh-token", data);
}
