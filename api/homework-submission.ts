import { ResponseData } from "@/types/api";
import type {
  HomeworkExerciseSubmissionRequest,
  WordExerciseSubmissionRequest,
} from "@/types/homework-submission";
import http from "@/utils/http/request";

/**
 * 创建或更新作业提交明细
 * @example
 * ```ts
 * // 请求示例
 * createOrUpdateHomeworkSubmissionDetail({
 *   id: 6,
 *   exerciseId: 317,
 *   homeworkId: 31,
 *   contentText: "Day 1 作业提交",
 *   contentImages: [{
 *     url: "https://example.com/image.png",
 *     name: "image.png"
 *   }],
 *   recordFileUrls: [],
 *   fileUrls: [{
 *     name: "document.docx",
 *     url: "https://example.com/document.docx",
 *     size: "15970"
 *   }]
 * })
 * ```
 * @param data 提交数据
 * @returns Promise<{code: number, data: number, msg: string}>
 */
export function createOrUpdateHomeworkSubmissionDetail(
  data: HomeworkExerciseSubmissionRequest
): Promise<ResponseData<number>> {
  return http.post(
    "/wbclass/homework-submission-detail/create-or-update",
    data
  );
}

/**
 * 提交单个单词练习
 * @example
 * ```ts
 * // 请求示例
 * submitOneWordExercise({
 *   exerciseId: 1,
 *   exerciseType: 1,
 *   wordId: 1,
 *   word: "hello",
 *   answer: "learning",
 *   correct: true,
 *   score: 100
 * })
 * ```
 * @param data 提交数据
 * @returns Promise<{code: number, data: number, msg: string}>
 */
export function submitOneWordExercise(
  data: WordExerciseSubmissionRequest
): Promise<ResponseData<number>> {
  return http.post(
    "/wbclass/homework-submission-detail/submit-one-word-exercise",
    data
  );
}

/**
 * 批量提交单词练习
 * @example
 * ```ts
 * // 请求示例
 * submitBatchWordExercise([{
 *   exerciseId: 1024,
 *   questionType: 1,
 *   wordId: 1,
 *   word: "apple",
 *   answer: "apple",
 *   correct: true,
 *   score: 100,
 *   recognitionStatus: "remember",
 *   index: 0
 * }])
 * ```
 * @param data 提交数据数组
 * @returns Promise<{code: number, data: number, msg: string}>
 */
export function submitBatchWordExercise(
  data: WordExerciseSubmissionRequest[]
): Promise<ResponseData<number>> {
  return http.post(
    "/wbclass/homework-submission-detail/submit-batch-word-exercise",
    data
  );
}

/**
 * 提交听写作业
 * @example
 * ```ts
 * // 请求示例
 * submitDictation({
 *   exerciseId: 390,
 *   imageUrl: "https://file.bpbl68.cn/orc/orctest7.jpg"
 * })
 * ```
 * @param data 提交数据
 * @param timeout 超时时间(毫秒)，默认为10000ms
 * @returns Promise<{code: number, data: any, msg: string}>
 */
export function submitDictation(
  data: {
    exerciseId: number;
    imageUrl: string;
  },
  timeout: number = 10000
): Promise<ResponseData<any>> {
  return http.post(
    "/wbclass/homework-submission-detail/submit-dictation",
    data,
    { timeout }
  );
}

/**
 * 修正批改单词
 * @example
 * ```ts
 * // 请求示例
 * correctDictation({
 *   id: 1024,
 *   corrections: [
 *     {
 *       word: "hello",
 *       answer: "hello",
 *       correct: true
 *     }
 *   ]
 * })
 * ```
 * @param data 提交数据
 * @returns Promise<{code: number, data: boolean, msg: string}>
 */
export function correctDictation(data: {
  id: number;
  corrections: Array<{
    word: string;
    answer: string;
    correct: boolean;
  }>;
}): Promise<ResponseData<boolean>> {
  return http.post(
    "/wbclass/homework-submission-detail/correct-dictation",
    data
  );
}
