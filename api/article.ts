import http from "@/utils/http/request";

/**
 * 文章详情接口
 */
export interface Article {
  id: number;
  title: string;
  content: string;
  audioUrl: string;
  duration: number; // 音频时长（毫秒）
  coverImage: string;
  extraParagraphs: Array<{
    text: string;
    type: number; // 1-标题，2-概述，0-普通段落
    sentences: Array<{
      id: number;
      index: number;
      content: string;
      start: number;
      end: number;
      startTime: number;
      endTime: number;
      duration: number;
    }>;
  }>;
  createTime: number;
}

/**
 * 文章跟读评分接口返回
 */
export interface ArticleEvaluationResult {
  totalScore: number; // 总得分
  pronunciation: number; // 发音准确度
  fluency: number; // 流利度
  integrity: number; // 完整度
  tone: number; // 音调得分
  audioUrl?: string; // 录音URL
  paragraphIndex?: number; // 段落索引
  sentenceIndex?: number; // 句子索引
  sample?: string; // 原文本
  usertext?: string; // 用户文本
  words?: Array<any>; // 单词评分详情
}

/**
 * 获取文章详情
 * @param id 文章ID
 * @returns Promise<ResponseData<Article>>
 */
export function getArticleDetail(id: number) {
  return http.get<Article>("/wbclass/articles/get", { id });
}

/**
 * 文章跟读评分
 * @param text 跟读文本
 * @param audioFilePath 录音文件路径
 * @param sentenceId 句子ID
 * @param exerciseId 练习ID（可选）
 * @param isFinish 是否完成所有句子评分（可选）
 * @returns Promise<ResponseData<ArticleEvaluationResult>>
 */
export function evaluateArticleReading(
  text: string,
  audioFilePath: string,
  sentenceId: number,
  exerciseId?: number,
  isFinish?: boolean
) {
  // 使用上传文件方式提交录音和文本
  return http.upload<ArticleEvaluationResult>("/wbclass/articles/evaluate", {
    filePath: audioFilePath,
    name: "audioFile",
    formData: {
      text: text,
      sentenceId: sentenceId.toString(),
      exerciseId: exerciseId !== undefined ? exerciseId.toString() : undefined,
      isFinish: isFinish !== undefined ? String(isFinish) : undefined,
    },
  });
}

/**
 * 提交文章跟读作业
 * @param homeworkId 作业ID
 * @param articleId 文章ID
 * @param data 评分结果数据
 */
export function submitArticleReadingHomework(
  homeworkId: number,
  articleId: number,
  data: {
    scores: Array<{
      sentenceIndex: number;
      score: number;
      audioUrl?: string;
    }>;
    totalScore: number;
  }
) {
  return http.post("/wbclass/articles/submit-homework", {
    homeworkId,
    articleId,
    ...data,
  });
}
