import { ResponseData } from "@/types/api";
import { FileInfo, FileUploadInput } from "@/types/file";
import http from "@/utils/http/request";
import { createResponseData } from "@/utils/http/response";

/**
 * 上传单个文件
 * @param file 文件对象（支持 tempFilePath、url、path）
 * @param directory 存储目录，默认为 common
 * @param timeout 上传超时时间（毫秒），默认 60000ms (1分钟)
 * @returns Promise<ResponseData<FileInfo>>
 */
export async function uploadFile(
  file: FileUploadInput,
  timeout = 120000
): Promise<ResponseData<FileInfo>> {
  try {
    const filePath =
      typeof file === "string"
        ? file
        : file.tempFilePath || file.url || file.path;
    if (!filePath) {
      throw new Error("无效的文件路径");
    }

    // 获取原始文件名和扩展名
    const originalFilename =
      typeof file === "string"
        ? filePath.split("/").pop() || ""
        : file.name || filePath.split("/").pop() || "";
    const fileExt = originalFilename.split(".").pop()?.toLowerCase() || "";

    // 生成随机文件名
    const randomStr = Math.random().toString(36).substr(2, 6);
    // const path = `${directory}/${Date.now()}_${randomStr}.${fileExt}`;

    // 准备表单数据
    const formData: Record<string, any> = {
      name: originalFilename,
    };

    // 如果有时长信息，添加到表单数据
    if (typeof file === "object" && file.duration !== undefined) {
      formData.duration = file.duration;
    }

    // 上传文件
    return await http.upload("/infra/file/app/uploadHomework", {
      filePath,
      name: "file",
      formData,
      timeout,
    });
  } catch (error: any) {
    console.error("[File Upload] Upload failed:", error);
    return createResponseData<FileInfo>(
      500,
      {} as FileInfo,
      error.message || "上传失败"
    );
  }
}

/**
 * 批量上传文件
 * @param files 文件列表
 * @param directory 存储目录，默认为 common
 * @param timeout 上传超时时间（毫秒），默认 60000ms (1分钟)
 * @returns Promise<ResponseData<FileInfo>[]>
 */
export async function uploadFiles(
  files: FileUploadInput[],
  directory = "common",
  timeout = 15000
): Promise<ResponseData<FileInfo>[]> {
  try {
    const uploadPromises = files.map((file) => uploadFile(file, timeout));
    return await Promise.all(uploadPromises);
  } catch (error: any) {
    console.error("[File Upload] Batch upload failed:", error);
    return [
      createResponseData<FileInfo>(
        500,
        {} as FileInfo,
        error.message || "批量上传失败"
      ),
    ];
  }
}
