export interface ThumbnailOptions {
  width?: number;
  height?: number;
}

export interface PreviewOptions extends ThumbnailOptions {
  quality?: number;
}

/**
 * 获取图片缩略图URL
 * @param {string} url 原图URL
 * @param {ThumbnailOptions} options 缩略图选项
 * @returns {string} 缩略图URL
 */
export function getThumbnailUrl(
  url: string,
  options: ThumbnailOptions = {}
): string {
  if (!url) return "";

  // 检查URL是否来自指定的COS域名
  if (!url.includes("eds-local-1259402086.cos.ap-guangzhou.myqcloud.com")) {
    return url;
  }

  const { width, height } = options;
  let thumbnailUrl = `${url}?imageView2/2`;

  // 只有在指定了宽度或高度时才添加相应参数
  if (width) {
    thumbnailUrl += `/w/${width}`;
  }
  if (height) {
    thumbnailUrl += `/h/${height}`;
  }

  return thumbnailUrl;
}

/**
 * 获取预览图URL
 * @param {string} url 原图URL
 * @param {PreviewOptions} options 预览图选项
 * @returns {string} 预览图URL
 */
export function getPreviewUrl(
  url: string,
  options: PreviewOptions = { quality: 75 }
): string {
  if (!url) return "";

  // 检查URL是否来自指定的COS域名
  if (!url.includes("eds-local-1259402086.cos.ap-guangzhou.myqcloud.com")) {
    return url;
  }

  const { width, height, quality } = options;
  let previewUrl = `${url}?imageView2/2`;

  // 只有在指定了宽度或高度时才添加相应参数
  if (width) {
    previewUrl += `/w/${width}`;
  }
  if (height) {
    previewUrl += `/h/${height}`;
  }

  // 添加质量压缩参数
  if (quality && quality >= 1 && quality <= 100) {
    previewUrl += `/quality/${quality}`;
  }

  return previewUrl;
}
