import { useUserStore } from "@/store/user";
import { errorCode } from "./errorCode";
import { BASE_URL } from "./request";
import { createResponseData } from "./response";

interface RequestInstance {
  interceptors: {
    response: {
      use: (handler: (response: any) => any) => void;
    };
  };
  request: (options: any) => Promise<any>;
}

// 需要忽略的提示
const ignoreMsgs = [
  "无效的刷新令牌", // 刷新令牌被删除时，不用提示
  "刷新令牌已过期", // 使用刷新令牌，刷新获取新的访问令牌时，结果因为过期失败，此时需要忽略
];

// 请求队列
let requestList: (() => void)[] = [];
// 是否正在刷新中
let isRefreshToken = false;
// 是否正在跳转登录页面
let isNavigatingToLogin = false;

export function setupResponseInterceptor(http: RequestInstance) {
  console.log("setupResponseInterceptor");
  const userStore = useUserStore();

  // 跳转登录页面的方法
  const navigateToLogin = () => {
    requestList = [];
    isRefreshToken = false;
    if (!isNavigatingToLogin) {
      // 获取当前页面栈
      const pages = getCurrentPages();
      // 获取最顶层页面路径
      const currentPage = pages[pages.length - 1]?.route || "";

      // 如果当前页面不是登录页面，才进行跳转
      if (
        !currentPage.includes("login/social") &&
        !currentPage.includes("login/mobile")
      ) {
        isNavigatingToLogin = true;
        userStore.clearLoginInfo();
        // 使用 navigateTo 保留当前页面，跳转到登录页
        uni.navigateTo({
          url: "/user-package/login/social",
          complete: () => {
            isNavigatingToLogin = false;
          },
          fail: () => {
            isNavigatingToLogin = false;
            uni.showToast({
              title: "跳转登录页面失败",
            });
          },
        });
      }
    }
  };

  http.interceptors.response.use(async (res) => {
    const code = res.data.code || 0;
    const msg = res.data.msg || errorCode[code] || errorCode["default"];

    if (ignoreMsgs.indexOf(msg) !== -1) {
      // 如果是忽略的错误码，直接返回 msg 异常
      return Promise.reject(createResponseData(code, null, msg));
    } else if (code === 401) {
      console.log("401");
      // 如果未认证，并且未进行刷新令牌，说明可能是访问令牌过期了
      if (!isRefreshToken) {
        isRefreshToken = true;
        // 获取刷新令牌
        const refreshToken = userStore.refreshTokenValue;

        // 1. 如果获取不到刷新令牌，则只能执行登出操作
        if (!refreshToken) {
          navigateToLogin();
          return Promise.reject(createResponseData(code, null, msg));
        }

        // 2. 进行刷新访问令牌
        try {
          const refreshRes = await uni.request({
            url: BASE_URL + "/auth/refresh-token",
            method: "POST",
            data: { refreshToken },
          });
          const refreshTokenRes = (refreshRes.data as any).data;
          // 2.1 刷新成功，则回放队列的请求 + 当前请求
          userStore.setToken(refreshTokenRes);
          requestList.forEach((cb) => cb());
          const retryRes = await http.request(res.config);
          return retryRes;
        } catch (e) {
          // 刷新失败，跳转到登录页
          navigateToLogin();
          return Promise.reject(createResponseData(code, null, msg));
        } finally {
          requestList = [];
          isRefreshToken = false;
        }
      } else {
        // 添加到队列，等待刷新获取到新的令牌
        return new Promise((resolve) => {
          requestList.push(() => {
            res.config.header.Authorization = "Bearer " + userStore.token;
            resolve(http.request(res.config));
          });
        });
      }
    } else if (code === 500) {
      uni.showToast({
        title: msg,
        icon: "none",
      });
      return Promise.reject(createResponseData(code, null, msg));
    } else if (code === 901) {
      uni.showToast({
        title: "演示模式，无法进行写操作",
        icon: "none",
      });
      return Promise.reject(createResponseData(code, null, msg));
    } else if (code !== 0) {
      if (msg === "无效的刷新令牌") {
        navigateToLogin();
      } else {
        uni.showToast({
          title: msg,
          icon: "none",
        });
      }
      return Promise.reject(createResponseData(code, null, msg));
    } else {
      return createResponseData(code, res.data.data, msg);
    }
  });
}
