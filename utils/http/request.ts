import ENV_CONFIG from "@/config";
import { useUserStore } from "@/store/user";
import { ResponseData } from "@/types/api";
import { errorCode } from "@/utils/http/errorCode";
import { createResponseData } from "@/utils/http/response";

// 基础请求地址
export const BASE_URL = ENV_CONFIG.API_URL;

// 请求队列
let requestList: (() => void)[] = [];
// 是否正在刷新中
let isRefreshToken = false;
// 是否正在跳转登录页面
let isNavigatingToLogin = false;

// 需要忽略的提示
const ignoreMsgs = [
  "无效的刷新令牌", // 刷新令牌被删除时，不用提示
  "刷新令牌已过期", // 使用刷新令牌，刷新获取新的访问令牌时，结果因为过期失败，此时需要忽略
];

// 添加拦截器
const httpInterceptor = {
  invoke(options: UniApp.RequestOptions) {
    // 1. 非 http 开头需要拼接地址
    if (!options.url.startsWith("http")) {
      options.url = BASE_URL + options.url;
    }
    // 2. 请求超时已在 request 函数中处理

    // 3. 添加小程序端请求头标识
    options.header = {
      "Content-Type": "application/json",
      ...options.header,
    };

    // 4. 添加token
    const userStore = useUserStore();
    if (userStore.hasLogin) {
      options.header.Authorization = `Bearer ${userStore.token}`;
    }

    return options;
  },
};

// 添加拦截器
uni.addInterceptor("request", httpInterceptor);
// uni.addInterceptor("uploadFile", httpInterceptor);

// 跳转登录页面的方法
const navigateToLogin = () => {
  requestList = [];
  isRefreshToken = false;
  if (!isNavigatingToLogin) {
    // 获取当前页面栈
    const pages = getCurrentPages();
    // 获取最顶层页面路径
    const currentPage = pages[pages.length - 1]?.route || "";

    // 如果当前页面不是登录页面，才进行跳转
    if (
      !currentPage.includes("login/social") &&
      !currentPage.includes("login/mobile")
    ) {
      isNavigatingToLogin = true;
      const userStore = useUserStore();
      userStore.clearLoginInfo();
      // 使用 navigateTo 保留当前页面，跳转到登录页
      uni.navigateTo({
        url: "/user-package/login/social",
        complete: () => {
          isNavigatingToLogin = false;
        },
        fail: () => {
          isNavigatingToLogin = false;
          uni.showToast({
            title: "跳转登录页面失败",
            icon: "none",
          });
        },
      });
    }
  }
};

/**
 * 请求函数
 * @param UniApp.RequestOptions
 * @returns Promise
 */
function request<T = any>(options: UniApp.RequestOptions) {
  // 应用默认超时时间，除非明确指定了超时时间
  if (!options.timeout) {
    options.timeout = 10000;
  }

  // 添加请求日志
  console.log("Request:", {
    url: options.url,
    method: options.method,
    data: options.data,
  });

  return new Promise<ResponseData<T>>((resolve, reject) => {
    uni.request({
      ...options,
      success: async (res: any) => {
        // 添加响应日志
        console.log("Response:", res.data);

        const response = res.data as ResponseData<T>;
        const code = response.code || 0;
        const msg = response.msg || errorCode[code] || errorCode["default"];

        if (ignoreMsgs.indexOf(msg) !== -1) {
          reject(createResponseData(code, null, msg));
        } else if (code === 401) {
          if (!isRefreshToken) {
            isRefreshToken = true;
            const userStore = useUserStore();
            const refreshToken = userStore.refreshTokenValue;

            if (!refreshToken) {
              navigateToLogin();
              reject(createResponseData(code, null, msg));
              return;
            }

            try {
              const refreshRes = await uni.request({
                url: BASE_URL + "/auth/refresh-token",
                method: "POST",
                data: { refreshToken },
              });
              const refreshTokenRes = (refreshRes.data as any).data;
              userStore.setToken(refreshTokenRes);
              requestList.forEach((cb) => cb());
              const retryRes = await uni.request(options);
              const retryResponse = retryRes.data as ResponseData<T>;
              resolve(
                createResponseData(
                  retryResponse.code,
                  retryResponse.data,
                  retryResponse.msg
                )
              );
            } catch (e) {
              navigateToLogin();
              reject(createResponseData(code, null, msg));
            } finally {
              requestList = [];
              isRefreshToken = false;
            }
          } else {
            requestList.push(() => {
              const userStore = useUserStore();
              options.header = {
                ...options.header,
                Authorization: `Bearer ${userStore.token}`,
              };
              uni.request({
                ...options,
                success: (retryRes: any) => {
                  const retryResponse = retryRes.data as ResponseData<T>;
                  resolve(
                    createResponseData(
                      retryResponse.code,
                      retryResponse.data,
                      retryResponse.msg
                    )
                  );
                },
                fail: reject,
              });
            });
          }
        } else if (code === 500) {
          uni.showToast({
            title: msg,
            icon: "none",
          });
          reject(createResponseData(code, null, msg));
        } else if (code === 901) {
          uni.showToast({
            title: "演示模式，无法进行写操作",
            icon: "none",
          });
          reject(createResponseData(code, null, msg));
        } else if (code !== 0) {
          if (msg === "无效的刷新令牌") {
            navigateToLogin();
          } else {
            uni.showToast({
              title: msg,
              icon: "none",
            });
          }
          reject(createResponseData(code, null, msg));
        } else {
          resolve(createResponseData(code, response.data, msg));
        }
      },
      fail: (err) => {
        // 添加错误日志
        console.error("Request failed:", err);

        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
        reject(createResponseData(-1, null, "网络错误"));
      },
    });
  });
}

/**
 * 上传文件
 * @param url 上传地址
 * @param options 上传选项
 * @returns Promise
 */
function upload<T = any>(
  url: string,
  options: Omit<UniApp.UploadFileOption, "url">
) {
  console.log(url, options);
  return new Promise<ResponseData<T>>((resolve, reject) => {
    uni.uploadFile({
      ...options,
      url: url.startsWith("http") ? url : BASE_URL + url,
      header: {
        ...options.header,
        Authorization: `Bearer ${useUserStore().token}`,
      },
      success: (uploadRes) => {
        try {
          const res = JSON.parse(uploadRes.data);
          resolve(createResponseData(res.code || 0, res.data, res.msg));
        } catch (e) {
          reject(createResponseData(500, null, "上传失败"));
        }
      },
      fail: (error) => {
        reject(createResponseData(500, null, error.errMsg || "上传失败"));
      },
    });
  });
}

export default {
  /**
   * GET 请求
   * @param url 请求地址
   * @param data 请求参数
   */
  get<T = any>(url: string, data?: any) {
    return request<T>({ url, method: "GET", data });
  },

  /**
   * POST 请求
   * @param url 请求地址
   * @param data 请求参数
   * @param options 请求选项，如 timeout
   */
  post<T = any>(
    url: string,
    data?: any,
    options?: Partial<UniApp.RequestOptions>
  ) {
    return request<T>({ url, method: "POST", data, ...options });
  },

  /**
   * PUT 请求
   * @param url 请求地址
   * @param data 请求参数
   */
  put<T = any>(url: string, data?: any) {
    return request<T>({ url, method: "PUT", data });
  },

  /**
   * DELETE 请求
   * @param url 请求地址
   * @param data 请求参数
   */
  delete<T = any>(url: string, data?: any) {
    return request<T>({ url, method: "DELETE", data });
  },

  upload<T = any>(url: string, options: Omit<UniApp.UploadFileOption, "url">) {
    return upload<T>(url, options);
  },
};
