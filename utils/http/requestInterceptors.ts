import { BASE_URL } from "./request";

interface RequestInstance {
  interceptors: {
    request: {
      use: (handler: (config: any) => any) => void;
    };
  };
}

export function setupRequestInterceptor(http: RequestInstance) {
  http.interceptors.request.use((config) => {
    // 1. 非 http 开头需要拼接地址
    if (!config.url.startsWith("http")) {
      config.url = BASE_URL + config.url;
    }
    // 2. 请求超时
    config.timeout = 10000;
    return config;
  });
}
