/**
 * 检查微信小程序是否有新版本并提示更新
 */
export const checkUpdate = () => {
  // #ifdef MP-WEIXIN
  if (uni.canIUse("getUpdateManager")) {
    const updateManager = uni.getUpdateManager();

    // 检查是否有新版本
    updateManager.onCheckForUpdate((res) => {
      if (res.hasUpdate) {
        console.log("发现新版本");

        // 新版本下载完成
        updateManager.onUpdateReady(() => {
          uni.showModal({
            title: "更新提示",
            content: "新版本已经准备好，是否重启应用？",
            showCancel: false,
            success: (res) => {
              if (res.confirm) {
                // 重启应用，应用新版本
                updateManager.applyUpdate();
              }
            },
          });
        });

        // 新版本下载失败
        updateManager.onUpdateFailed(() => {
          uni.showModal({
            title: "提示",
            content: "新版本下载失败，请检查网络后重试",
            showCancel: false,
          });
        });
      }
    });
  } else {
    uni.showModal({
      title: "提示",
      content: "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。",
      showCancel: false,
    });
  }
  // #endif
};
