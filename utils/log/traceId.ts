/**
 * traceId 管理工具
 * 用于全局跟踪用户的使用链路
 */

// 存储当前会话的 traceId
let currentTraceId: string = "";

/**
 * 生成唯一的 traceId
 * 格式: 时间戳-随机字符串
 */
export const generateTraceId = (): string => {
  const timestamp = Date.now();
  const randomStr = Math.random().toString(36).substring(2, 10);
  return `${timestamp}-${randomStr}`;
};

/**
 * 初始化 traceId
 * 在应用启动时调用，生成新的 traceId
 */
export const initTraceId = (): string => {
  currentTraceId = generateTraceId();
  try {
    // 将 traceId 保存到本地存储，以便在应用崩溃后重启时能够恢复
    uni.setStorageSync("wb_trace_id", currentTraceId);
  } catch (error) {
    console.error("保存 traceId 到本地存储失败:", error);
  }
  return currentTraceId;
};

/**
 * 获取当前的 traceId
 * 如果不存在，则尝试从本地存储中恢复，或生成新的
 */
export const getTraceId = (): string => {
  // 如果 currentTraceId 已存在，直接返回
  if (currentTraceId) {
    return currentTraceId;
  }

  // 尝试从本地存储中恢复
  try {
    const storedTraceId = uni.getStorageSync("wb_trace_id");
    if (storedTraceId) {
      currentTraceId = storedTraceId;
      return currentTraceId;
    }
  } catch (error) {
    console.error("从本地存储获取 traceId 失败:", error);
  }

  // 如果恢复失败，则生成新的
  return initTraceId();
};

export default {
  generateTraceId,
  initTraceId,
  getTraceId,
};
