/**
 * 日志服务统一导出文件
 */
import { logConfig } from "./config";
import { initLogService } from "./init";
import { getTraceId, initTraceId } from "./traceId";
import {
  initLogUploader,
  LogLevel,
  uploadLog,
  uploadLogNonBlocking,
} from "./uploader";

// 统一导出
export {
  // traceId 相关
  getTraceId,
  // 初始化
  initLogService,
  initLogUploader,
  initTraceId,
  // 配置
  logConfig,
  // 日志级别
  LogLevel,
  // 日志上传
  uploadLog,
  // 非阻塞日志上传（推荐使用）
  uploadLogNonBlocking,
};

export default {
  logConfig,
  initLogService,
  initLogUploader,
  uploadLog,
  uploadLogNonBlocking,
  LogLevel,
  getTraceId,
  initTraceId,
};
