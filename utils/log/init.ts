import { logConfig } from "./config";
import { initTraceId } from "./traceId";
import { initLogUploader } from "./uploader";

/**
 * 初始化日志服务
 * 在应用启动时调用此函数来初始化日志服务
 */
export const initLogService = (): boolean => {
  if (!logConfig.enableLogUpload) {
    console.log("日志上传功能已禁用");
    return false;
  }

  try {
    // 初始化 traceId
    const traceId = initTraceId();
    console.log("traceId 初始化成功:", traceId);

    const result = initLogUploader({
      endpoint: logConfig.endpoint,
      topicId: logConfig.topicId,
      retryTimes: logConfig.retryTimes,
    });

    if (result) {
      console.log("日志服务初始化成功");
    } else {
      console.error("日志服务初始化失败");
    }

    return result;
  } catch (error) {
    console.error("初始化日志服务时发生错误:", error);
    return false;
  }
};

export default initLogService;
