/**
 * 腾讯云日志上传工具
 */

// 导入SDK
import { PRE_VERSION } from "@/config/index";
import { useUserStore } from "@/store/user";
import * as cls from "tencentcloud-cls-sdk-js-mini";
import { getTraceId } from "./traceId";

// 导入类型
type LoggingOptions = {
  endpoint: string; // 域名信息
  topicId: string; // 日志主题ID
  retryTimes?: number; // 重试次数
};

// 定义日志级别枚举
export enum LogLevel {
  INFO = "info",
  ERROR = "error",
}

// 全局变量
let isInitialized: boolean = false;
let globalTopicId: string = "";
let clsClient: any = null;

/**
 * 初始化日志上传工具
 * @param options 配置选项
 */
export const initLogUploader = (options: LoggingOptions): boolean => {
  try {
    const { AsyncClient } = cls;

    // 保存主题ID
    globalTopicId = options.topicId;

    // 初始化客户端
    clsClient = new AsyncClient({
      endpoint: options.endpoint,
      retry_times: options.retryTimes || 3,
    });

    isInitialized = true;
    console.log("日志上传工具初始化成功");
    return true;
  } catch (error) {
    console.error("日志上传工具初始化失败", error);
    isInitialized = false;
    return false;
  }
};

/**
 * 获取当前用户信息
 * @returns 用户信息对象
 */
const getUserInfo = (): Record<string, string> => {
  try {
    // 优先从store中获取用户信息
    const userStore = useUserStore();

    if (userStore.hasLogin && userStore.info) {
      return {
        userId: String(userStore.info.id || ""),
        userName: userStore.info.nickname || "",
        userPhone: userStore.info.mobile || "",
      };
    }
  } catch (err) {
    console.warn("从store获取用户信息失败，将尝试从本地存储获取", err);
  }
  // 如果获取失败，返回空对象
  return {
    userId: "",
    userName: "",
    userPhone: "",
  };
};

/**
 * 上传日志
 * @param level 日志级别
 * @param message 日志消息
 * @returns 是否上传成功
 */
export const uploadLog = async (
  level: LogLevel,
  message: string
): Promise<boolean> => {
  if (!isInitialized || !clsClient) {
    console.error("日志上传工具未初始化");
    return false;
  }

  try {
    const { Log, LogGroup, PutLogsRequest } = cls;

    // 获取设备信息作为来源
    let sourceIp = "unknown_device";
    try {
      const systemInfo = uni.getSystemInfoSync();
      sourceIp = systemInfo.model || sourceIp;
    } catch (e) {
      console.warn("获取设备信息失败", e);
    }

    // 创建日志组
    const logGroup = new LogGroup(sourceIp);

    // 创建日志条目
    const log = new Log(Math.floor(Date.now() / 1000));

    // 添加日志来源
    log.addContent("app", "wbClassMiniApp");
    // 添加应用版本
    log.addContent("appVersion", PRE_VERSION);
    // 添加日志级别
    log.addContent("level", level);

    // 添加 traceId
    log.addContent("traceId", getTraceId());

    // 添加日志消息
    log.addContent("message", message);

    // 添加用户信息
    const userInfo = getUserInfo();
    log.addContent("userId", userInfo.userId);
    log.addContent("userName", userInfo.userName);
    log.addContent("userPhone", userInfo.userPhone);

    // 添加设备信息
    try {
      const systemInfo = uni.getSystemInfoSync();
      log.addContent("platform", systemInfo.platform);
      log.addContent("model", systemInfo.model);
      log.addContent("system", systemInfo.system);
      log.addContent("SDKVersion", systemInfo.SDKVersion);
      log.addContent("hostVersion", systemInfo.hostVersion || "");
      log.addContent("deviceType", systemInfo.deviceType || "");
    } catch (e) {
      log.addContent("device_error", String(e));
    }

    // 将日志添加到日志组
    logGroup.addLog(log);

    // 创建上传请求
    const request = new PutLogsRequest(globalTopicId, logGroup);

    // 执行上传
    await clsClient.PutLogs(request);
    console.log(`${level}级别日志上传成功`);
    return true;
  } catch (error) {
    console.error(`${level}级别日志上传失败`, error);
    return false;
  }
};

/**
 * 非阻塞方式上传日志（推荐使用）
 * 将日志上传操作放到下一个事件循环，不阻塞主业务流程
 * @param level 日志级别
 * @param message 日志消息
 */
export const uploadLogNonBlocking = (
  level: LogLevel,
  message: string
): void => {
  // 使用 setTimeout 将日志上传操作放到下一个事件循环中执行
  setTimeout(() => {
    uploadLog(level, message).catch((err) => {
      console.error("后台日志上传失败:", err);
    });
  }, 0);
};

/**
 * 上传对象日志（非阻塞方式）
 * 自动序列化对象并处理堆栈过长问题
 * @param title 日志标题
 * @param obj 需要记录的对象
 * @param level 日志级别，默认为错误级别
 * @param maxLength 序列化后的最大长度，默认3000字符
 */
export const uploadObjectLog = (
  title: string,
  obj: any,
  level: LogLevel = LogLevel.ERROR,
  maxLength: number = 3000
): void => {
  try {
    // 序列化对象
    let jsonStr = "";

    try {
      // 尝试使用JSON.stringify序列化对象
      jsonStr = JSON.stringify(
        obj,
        (key, value) => {
          // 处理循环引用和复杂对象
          if (typeof value === "object" && value !== null) {
            // 简单处理函数和特殊对象
            if (value instanceof Error) {
              return {
                name: value.name,
                message: value.message,
                stack: value.stack
                  ? value.stack.split("\n").slice(0, 5).join("\n")
                  : "No stack",
              };
            }
          }
          return value;
        },
        2
      );
    } catch (e: any) {
      // 如果序列化失败，尝试提取关键属性
      jsonStr = JSON.stringify(
        {
          serialization_error: true,
          error_message: e.message,
          object_type: typeof obj,
          extract: {
            ...(obj.errMsg ? { errMsg: obj.errMsg } : {}),
            ...(obj.errCode ? { errCode: obj.errCode } : {}),
            ...(obj.message ? { message: obj.message } : {}),
            ...(obj.code ? { code: obj.code } : {}),
            ...(obj.name ? { name: obj.name } : {}),
          },
        },
        null,
        2
      );
    }

    // 截取过长的内容
    if (jsonStr.length > maxLength) {
      jsonStr =
        jsonStr.substring(0, maxLength) +
        `... [截断，完整长度:${jsonStr.length}]`;
    }

    // 组合日志消息
    const logMessage = `${title}\n${jsonStr}`;

    // 使用非阻塞方式上传
    uploadLogNonBlocking(level, logMessage);
  } catch (error: any) {
    // 确保错误处理不影响主业务
    console.error("处理对象日志失败:", error);
    uploadLogNonBlocking(
      LogLevel.ERROR,
      `处理对象日志失败: ${error.message || "未知错误"}`
    );
  }
};

export default {
  initLogUploader,
  uploadLog,
  uploadLogNonBlocking,
  uploadObjectLog,
  LogLevel,
};
