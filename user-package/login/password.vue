<template>
  <view class="password-login-page">
    <view class="bg-decoration"></view>
    <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <text class="title">账号密码登录</text>
    </view>

    <view class="form-container">
      <image class="page-logo" src="/static/logo.png" mode="aspectFit" />

      <view class="form-item">
        <input
          class="input"
          type="text"
          v-model="username"
          placeholder="请输入手机号"
          placeholder-class="placeholder"
        />
      </view>
      <view class="form-item">
        <input
          class="input"
          :type="showPassword ? 'text' : 'password'"
          v-model="password"
          placeholder="请输入密码"
          placeholder-class="placeholder"
        />
        <text
          class="iconfont"
          :class="showPassword ? 'icon-eye' : 'icon-eye-close'"
          @click="togglePasswordVisibility"
        ></text>
      </view>

      <button class="login-btn" @click="handleLogin" :disabled="isLoading">
        {{ isLoading ? "登录中..." : "登录" }}
      </button>

      <!-- <view class="other-options">
        <text class="option-text" @click="goToMobileLogin">手机号登录</text>
        <text class="option-text" @click="goToForgetPassword">忘记密码</text>
      </view> -->
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from "@/store/user";
import { ref } from "vue";

const userStore = useUserStore();
const username = ref("");
const password = ref("");
const showPassword = ref(false);
const isLoading = ref(false);

// 切换密码显示/隐藏
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// 处理登录
const handleLogin = async () => {
  // 表单验证
  if (!username.value.trim()) {
    uni.showToast({
      title: "请输入账号",
      icon: "none",
    });
    return;
  }

  if (!password.value.trim()) {
    uni.showToast({
      title: "请输入密码",
      icon: "none",
    });
    return;
  }

  try {
    isLoading.value = true;

    await userStore.login({
      type: "password",
      data: {
        mobile: username.value.trim(),
        password: password.value.trim(),
      },
    });

    uni.showToast({
      title: "登录成功",
      icon: "success",
    });

    // 根据是否首次登录决定跳转页面
    if (userStore.firstLogin) {
      setTimeout(() => {
        uni.navigateTo({
          url: "/user-package/user-info/index",
        });
      }, 300);
    } else {
      // 返回上一页
      uni.navigateBack({
        delta: 2, // 返回两级，跳过登录选择页
      });
    }
  } catch (error) {
    console.error("账号密码登录失败:", error);
    uni.showToast({
      title: "登录失败，请检查账号密码",
      icon: "none",
    });
  } finally {
    isLoading.value = false;
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 跳转到手机号登录
const goToMobileLogin = () => {
  uni.navigateTo({
    url: "/user-package/login/mobile",
  });
};

// 跳转到忘记密码页面
const goToForgetPassword = () => {
  uni.navigateTo({
    url: "/user-package/login/forget-password",
  });
};
</script>

<style lang="scss" scoped>
// 主题色变量
$primary-color: #f5e973; // 淡黄色主题色 - 与logo一致
$primary-deep: #e6ca4a; // 稍深的黄色，用于渐变和强调
$primary-gradient: linear-gradient(135deg, #f5e973, #e6ca4a);
$secondary-color: #fffdf0; // 非常淡的黄色背景
$text-color: #444;
$light-text: #666;
$border-color: rgba(230, 202, 74, 0.3); // 边框颜色

.password-login-page {
  min-height: 100vh;
  padding: 40rpx 40rpx;
  background-color: #fff;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  overflow: hidden;

  .bg-decoration {
    position: absolute;
    top: -200rpx;
    right: -200rpx;
    width: 600rpx;
    height: 600rpx;
    border-radius: 50%;
    background: $primary-gradient;
    opacity: 0.15;
    z-index: 0;
  }

  .header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;
    z-index: 1;

    .back-btn {
      position: absolute;
      left: 0;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-arrow-left {
        font-size: 40rpx;
        color: $text-color;
      }
    }

    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: $text-color;
    }
  }

  .form-container {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20rpx;

    .page-logo {
      width: 100rpx;
      height: 100rpx;
      margin-bottom: 40rpx;
      border-radius: 20rpx;
      box-shadow: 0 6rpx 16rpx rgba(230, 202, 74, 0.25);
    }

    .form-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 100rpx;
      background-color: $secondary-color;
      border-radius: 50rpx;
      padding: 0 30rpx;
      margin-bottom: 30rpx;
      border: 2rpx solid $border-color;

      .input {
        flex: 1;
        height: 100%;
        font-size: 30rpx;
        color: $text-color;
      }

      .placeholder {
        color: #999;
      }

      .iconfont {
        font-size: 40rpx;
        color: $light-text;
        padding: 0 10rpx;
      }
    }

    .login-btn {
      width: 100%;
      height: 96rpx;
      line-height: 96rpx;
      background: $primary-gradient;
      color: $text-color;
      font-size: 34rpx;
      font-weight: 600;
      border-radius: 48rpx;
      margin-top: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 10rpx 20rpx rgba(230, 202, 74, 0.25);
      transition: all 0.3s;
      border: 2rpx solid $border-color;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 5rpx 10rpx rgba(230, 202, 74, 0.25);
      }

      &:disabled {
        opacity: 0.7;
      }
    }

    .other-options {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin-top: 20rpx;

      .option-text {
        font-size: 28rpx;
        color: $primary-deep;
        padding: 10rpx;
      }
    }
  }

  .footer {
    text-align: center;
    margin-top: auto;
    padding-bottom: 30rpx;
    position: relative;
    z-index: 1;

    .copyright {
      font-size: 24rpx;
      color: #999;
    }
  }
}
</style>
