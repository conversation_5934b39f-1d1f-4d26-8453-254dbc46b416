<template>
  <view class="mobile-login">
    <view class="form">
      <view class="form-item">
        <text class="iconfont icon-mobile"></text>
        <input
          v-model="mobile"
          type="number"
          maxlength="11"
          placeholder="请输入手机号"
          placeholder-class="placeholder"
        />
      </view>
      <view class="form-item">
        <text class="iconfont icon-safe"></text>
        <input
          v-model="code"
          type="number"
          maxlength="6"
          placeholder="请输入验证码"
          placeholder-class="placeholder"
        />
        <text
          class="code-btn"
          :class="{ disabled: counting }"
          @click="handleSendCode"
        >
          {{ counting ? `${countdown}s后重新获取` : "获取验证码" }}
        </text>
      </view>
    </view>

    <button class="submit-btn" :disabled="!isValid" @click="handleSubmit">
      登录
    </button>

    <view class="back-btn" @click="goBack"> 返回 </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { sendSmsCode, smsLogin } from "@/api/auth";
import { useUserStore } from "@/store/user";

const userStore = useUserStore();

const mobile = ref("");
const code = ref("");
const counting = ref(false);
const countdown = ref(60);

// 表单验证
const isValid = computed(() => {
  return /^1\d{10}$/.test(mobile.value) && /^\d{6}$/.test(code.value);
});

// 发送验证码
const handleSendCode = async () => {
  if (counting.value) return;
  if (!/^1\d{10}$/.test(mobile.value)) {
    uni.showToast({
      title: "请输入正确的手机号",
      icon: "none",
    });
    return;
  }

  try {
    await sendSmsCode({
      mobile: mobile.value,
      scene: "login",
    });
    counting.value = true;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
        counting.value = false;
        countdown.value = 60;
      }
    }, 1000);
  } catch (error) {
    console.error("发送验证码失败:", error);
    uni.showToast({
      title: "发送失败",
      icon: "none",
    });
  }
};

// 提交登录
const handleSubmit = async () => {
  if (!isValid.value) return;

  try {
    const res = await smsLogin({
      mobile: mobile.value,
      code: code.value,
    });
    const { accessToken, refreshToken } = res.getData();
    userStore.setToken({ accessToken, refreshToken });
    uni.navigateBack();
  } catch (error) {
    console.error("登录失败:", error);
    uni.showToast({
      title: "登录失败",
      icon: "none",
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.mobile-login {
  min-height: 100vh;
  padding: 40rpx;
  background-color: #fff;

  .form {
    margin-top: 60rpx;

    .form-item {
      display: flex;
      align-items: center;
      height: 100rpx;
      border-bottom: 1px solid #eee;
      margin-bottom: 20rpx;

      .iconfont {
        font-size: 40rpx;
        color: #999;
        margin-right: 20rpx;
      }

      input {
        flex: 1;
        height: 100%;
        font-size: 32rpx;
      }

      .placeholder {
        color: #999;
      }

      .code-btn {
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #07c160;

        &.disabled {
          color: #999;
        }
      }
    }
  }

  .submit-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background-color: #07c160;
    color: #fff;
    font-size: 32rpx;
    border-radius: 44rpx;
    margin-top: 60rpx;

    &[disabled] {
      background-color: #ccc;
    }
  }

  .back-btn {
    text-align: center;
    margin-top: 40rpx;
    color: #999;
    font-size: 28rpx;
  }
}
</style>
