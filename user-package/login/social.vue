<template>
  <view class="login-page">
    <view class="bg-decoration"></view>
    <view class="header">
      <image class="logo" src="/static/logo.png" mode="aspectFit" />
      <text class="title">白熊作业</text>
      <text class="subtitle">让学习更有趣</text>
    </view>

    <view class="login-options">
      <button
        class="weixin-btn"
        open-type="getPhoneNumber"
        @getphonenumber="handleWeixinLogin"
        :disabled="!isAgreed"
        :class="{ 'btn-disabled': !isAgreed }"
        @click="handleButtonClick"
      >
        <text class="iconfont icon-weixin"></text>
        手机号快捷登录
      </button>
      <view class="divider">
        <text class="line"></text>
        <text class="text">其他登录方式</text>
        <text class="line"></text>
      </view>
      <!-- <view class="mobile-btn" @click="goToMobileLogin">
        <text class="iconfont icon-mobile"></text>
        手机号登录
      </view> -->
      <view class="password-btn" @click="handlePasswordLogin">
        <text class="iconfont icon-lock"></text>
        账号密码登录
      </view>
    </view>

    <view class="footer">
      <view class="agreement-box">
        <checkbox-group @change="handleAgreementChange">
          <label class="agreement-label">
            <checkbox
              :checked="isAgreed"
              color="#e6ca4a"
              style="transform: scale(0.8)"
            />
            <text class="agreement-text">我已阅读并同意</text>
            <text class="agreement-link" @click.stop="openPrivacyPolicy"
              >《白熊隐私政策》</text
            >
          </label>
        </checkbox-group>
      </view>
    </view>

    <!-- #ifdef MP-WEIXIN -->
    <zero-privacy
      ref="privacyRef"
      :onNeed="false"
      :color="'#e6ca4a'"
      @agree="handlePrivacyAgree"
      @disagree="handlePrivacyDisagree"
      @needAuthorization="handleNeedAuthorization"
    ></zero-privacy>
    <!-- #endif -->
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from "@/store/user";
import { ref } from "vue";

const userStore = useUserStore();
// 使用 any 类型来避免类型错误，因为我们没有 zero-privacy 组件的类型定义
const privacyRef = ref<any>(null);
const isAgreed = ref(false);
const isPrivacyAgreed = ref(false);

// 处理协议勾选状态变化
const handleAgreementChange = (e: any) => {
  isAgreed.value = e.detail.value.length > 0;
};

// 打开隐私政策
const openPrivacyPolicy = () => {
  // 使用微信官方的隐私协议页面
  if (privacyRef.value) {
    privacyRef.value.handleOpenPrivacyContract();
  }
};

// 处理按钮点击
const handleButtonClick = () => {
  if (!isAgreed.value) {
    uni.showToast({
      title: "请先勾选隐私政策",
      icon: "none",
    });
  }
};

// 处理账号密码登录
const handlePasswordLogin = () => {
  if (!isAgreed.value) {
    uni.showToast({
      title: "请先勾选隐私政策",
      icon: "none",
    });
    return;
  }

  goToPasswordLogin();
};

// 处理隐私协议授权状态
const handleNeedAuthorization = (needAuth: boolean) => {
  isPrivacyAgreed.value = !needAuth;
  console.log("是否需要隐私授权:", needAuth);
};

// 同意隐私协议
const handlePrivacyAgree = () => {
  isPrivacyAgreed.value = true;
  console.log("用户同意隐私协议");
};

// 拒绝隐私协议
const handlePrivacyDisagree = () => {
  isPrivacyAgreed.value = false;
  console.log("用户拒绝隐私协议");
  uni.showToast({
    title: "需要同意隐私协议才能使用本应用",
    icon: "none",
  });
};

// 处理微信登录
const handleWeixinLogin = async (e: any) => {
  try {
    // 检查是否同意协议
    if (!isAgreed.value) {
      uni.showToast({
        title: "请先勾选隐私政策",
        icon: "none",
      });
      return;
    }

    const phoneCode = e.detail.code;
    if (!phoneCode) {
      uni.showModal({
        title: "授权失败",
        content: "您已拒绝获取绑定手机号登录授权，可以使用其他手机号验证登录",
        cancelText: "知道了",
        confirmText: "验证登录",
        success: (res) => {
          if (res.confirm) {
            goToMobileLogin();
          }
        },
      });
      return;
    }

    // 获取微信登录code
    const { code: loginCode } = await uni.login();

    await userStore.login({
      type: "wechat",
      data: { phoneCode, loginCode },
    });

    console.log("登录成功，store状态：", {
      hasLogin: userStore.hasLogin,
      userId: userStore.userId,
      firstLogin: userStore.firstLogin,
      expiresTime: userStore.expiresTime,
      storage: {
        accessToken: uni.getStorageSync("ACCESS_TOKEN"),
        refreshToken: uni.getStorageSync("REFRESH_TOKEN"),
      },
    });

    uni.showToast({
      title: "登录成功",
      icon: "success",
    });

    // 根据是否首次登录决定跳转页面
    if (userStore.firstLogin) {
      setTimeout(() => {
        uni.navigateTo({
          url: "/user-package/user-info/index",
        });
      }, 300);
    } else {
      // 返回上一页
      uni.navigateBack();
    }
  } catch (error) {
    console.error("微信登录失败:", error);
    uni.showToast({
      title: "登录失败",
      icon: "none",
    });
  }
};

// 跳转到手机号登录
const goToMobileLogin = () => {
  uni.navigateTo({
    url: "/user-package/login/mobile",
  });
};

// 跳转到账号密码登录
const goToPasswordLogin = () => {
  uni.navigateTo({
    url: "/user-package/login/password",
  });
};
</script>

<style lang="scss" scoped>
// 主题色变量
$primary-color: #f5e973; // 淡黄色主题色 - 与logo一致
$primary-deep: #e6ca4a; // 稍深的黄色，用于渐变和强调
$primary-gradient: linear-gradient(135deg, #f5e973, #e6ca4a);
$secondary-color: #fffdf0; // 非常淡的黄色背景
$text-color: #444;
$light-text: #666;
$border-color: rgba(230, 202, 74, 0.3); // 边框颜色

.login-page {
  min-height: 100vh;
  padding: 60rpx 40rpx;
  background-color: #fff;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;

  .bg-decoration {
    position: absolute;
    top: -200rpx;
    right: -200rpx;
    width: 600rpx;
    height: 600rpx;
    border-radius: 50%;
    background: $primary-gradient;
    opacity: 0.15; // 略微提高不透明度以增强视觉效果
    z-index: 0;
  }

  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 80rpx;
    margin-bottom: 120rpx;
    position: relative;
    z-index: 1;

    .logo {
      width: 180rpx;
      height: 180rpx;
      margin-bottom: 30rpx;
      border-radius: 30rpx;
      box-shadow: 0 10rpx 30rpx rgba(230, 202, 74, 0.25);
    }

    .title {
      font-size: 48rpx;
      font-weight: bold;
      color: $primary-deep; // 使用稍深的黄色提高可读性
      margin-bottom: 16rpx;
      letter-spacing: 2rpx;
    }

    .subtitle {
      font-size: 30rpx;
      color: $light-text;
      letter-spacing: 1rpx;
    }
  }

  .login-options {
    position: relative;
    z-index: 1;

    .weixin-btn {
      width: 100%;
      height: 96rpx;
      line-height: 96rpx;
      background: $primary-gradient;
      color: #444; // 深灰色文字与淡黄色背景形成良好对比
      font-size: 34rpx;
      font-weight: 600;
      border-radius: 48rpx;
      margin-bottom: 40rpx;
      box-shadow: 0 10rpx 20rpx rgba(230, 202, 74, 0.25);
      transition: all 0.3s;
      border: 2rpx solid $border-color; // 添加边框增强按钮立体感

      &:active {
        transform: scale(0.98);
        box-shadow: 0 5rpx 10rpx rgba(230, 202, 74, 0.25);
      }

      &.btn-disabled {
        opacity: 0.6;
        background: #f0f0f0;
        color: #999;
        box-shadow: none;
      }

      .icon-weixin {
        margin-right: 12rpx;
        font-size: 38rpx;
      }
    }

    .divider {
      display: flex;
      align-items: center;
      margin: 50rpx 0;

      .line {
        flex: 1;
        height: 1px;
        background-color: rgba(230, 202, 74, 0.2); // 使分隔线与主题色统一
      }

      .text {
        padding: 0 24rpx;
        color: $light-text;
        font-size: 26rpx;
      }
    }

    .mobile-btn,
    .password-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 96rpx;
      background-color: $secondary-color;
      color: $primary-deep; // 使用稍深的颜色提高可读性
      font-size: 34rpx;
      font-weight: 500;
      border-radius: 48rpx;
      border: 2rpx solid $border-color;
      transition: all 0.3s;
      margin-bottom: 30rpx;

      &:active {
        background-color: rgba(245, 233, 115, 0.1);
      }

      &.btn-disabled {
        opacity: 0.6;
        background: #f0f0f0;
        color: #999;
        box-shadow: none;
        border-color: #e0e0e0;
      }

      .icon-mobile,
      .icon-lock {
        margin-right: 12rpx;
        font-size: 36rpx;
      }
    }
  }

  .footer {
    text-align: center;
    margin-top: 100rpx;
    position: relative;
    z-index: 1;

    .agreement-box {
      display: flex;
      justify-content: center;

      .agreement-label {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: center;
      }

      .agreement-text {
        font-size: 26rpx;
        color: #999;
      }

      .agreement-link {
        font-size: 26rpx;
        color: $primary-deep;
      }
    }
  }
}
</style>
