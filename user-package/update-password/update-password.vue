<template>
  <view class="update-password-page">
    <view class="bg-decoration"></view>
    <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <text class="title">修改密码</text>
    </view>

    <view class="form-container">
      <view class="form-item">
        <input
          class="input"
          type="password"
          v-model="formData.oldPassword"
          placeholder="请输入旧密码"
          placeholder-class="placeholder"
          :password="!showOldPassword"
        />
        <text
          class="iconfont"
          :class="showOldPassword ? 'icon-eye' : 'icon-eye-close'"
          @click="toggleOldPasswordVisibility"
        ></text>
      </view>

      <view class="form-item">
        <input
          class="input"
          type="password"
          v-model="formData.password"
          placeholder="请输入新密码(4-16位)"
          placeholder-class="placeholder"
          :password="!showNewPassword"
        />
        <text
          class="iconfont"
          :class="showNewPassword ? 'icon-eye' : 'icon-eye-close'"
          @click="toggleNewPasswordVisibility"
        ></text>
      </view>

      <view class="form-item">
        <input
          class="input"
          type="password"
          v-model="confirmPassword"
          placeholder="请再次输入新密码"
          placeholder-class="placeholder"
          :password="!showConfirmPassword"
        />
        <text
          class="iconfont"
          :class="showConfirmPassword ? 'icon-eye' : 'icon-eye-close'"
          @click="toggleConfirmPasswordVisibility"
        ></text>
      </view>

      <view class="password-tips">
        <text>密码长度为4-16位</text>
      </view>

      <button class="submit-btn" @click="handleSubmit" :disabled="isLoading">
        {{ isLoading ? "提交中..." : "确认修改" }}
      </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { updatePassword } from "@/api/auth";
import { ref } from "vue";

const formData = ref({
  oldPassword: "",
  password: "",
});

const confirmPassword = ref("");
const isLoading = ref(false);
const showOldPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

// 切换密码显示/隐藏
const toggleOldPasswordVisibility = () => {
  showOldPassword.value = !showOldPassword.value;
};

const toggleNewPasswordVisibility = () => {
  showNewPassword.value = !showNewPassword.value;
};

const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

const handleSubmit = async () => {
  // 表单验证
  if (!formData.value.oldPassword) {
    uni.showToast({
      title: "请输入旧密码",
      icon: "none",
    });
    return;
  }

  if (!formData.value.password) {
    uni.showToast({
      title: "请输入新密码",
      icon: "none",
    });
    return;
  }

  if (
    formData.value.password.length < 4 ||
    formData.value.password.length > 16
  ) {
    uni.showToast({
      title: "密码长度为4-16位",
      icon: "none",
    });
    return;
  }

  if (formData.value.password !== confirmPassword.value) {
    uni.showToast({
      title: "两次输入的密码不一致",
      icon: "none",
    });
    return;
  }

  try {
    isLoading.value = true;

    const result = await updatePassword(formData.value);

    if (result.isSuccess()) {
      uni.showToast({
        title: "密码修改成功",
        icon: "success",
      });

      // 重置表单
      formData.value = {
        oldPassword: "",
        password: "",
      };
      confirmPassword.value = "";

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 300);
    }
  } catch (error: any) {
    uni.showToast({
      title: error.msg,
      icon: "none",
    });
    console.error("修改密码失败:", error.msg);
  } finally {
    isLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
// 主题色变量
$primary-color: #f5e973; // 淡黄色主题色 - 与logo一致
$primary-deep: #e6ca4a; // 稍深的黄色，用于渐变和强调
$primary-gradient: linear-gradient(135deg, #f5e973, #e6ca4a);
$secondary-color: #fffdf0; // 非常淡的黄色背景
$text-color: #444;
$light-text: #666;
$border-color: rgba(230, 202, 74, 0.3); // 边框颜色

.update-password-page {
  min-height: 100vh;
  padding: 40rpx 40rpx;
  background-color: #fff;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  overflow: hidden;

  .bg-decoration {
    position: absolute;
    top: -200rpx;
    right: -200rpx;
    width: 600rpx;
    height: 600rpx;
    border-radius: 50%;
    background: $primary-gradient;
    opacity: 0.15;
    z-index: 0;
  }

  .header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 60rpx;
    z-index: 1;

    .back-btn {
      position: absolute;
      left: 0;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-arrow-left {
        font-size: 40rpx;
        color: $text-color;
      }
    }

    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: $text-color;
    }
  }

  .form-container {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20rpx;

    .form-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 100rpx;
      background-color: $secondary-color;
      border-radius: 50rpx;
      padding: 0 30rpx;
      margin-bottom: 30rpx;
      border: 2rpx solid $border-color;

      .input {
        flex: 1;
        height: 100%;
        font-size: 30rpx;
        color: $text-color;
      }

      .placeholder {
        color: #999;
      }

      .iconfont {
        font-size: 40rpx;
        color: $light-text;
        padding: 0 10rpx;
      }
    }

    .password-tips {
      width: 100%;
      font-size: 24rpx;
      color: #999;
      margin-bottom: 40rpx;
      padding-left: 20rpx;
    }

    .submit-btn {
      width: 100%;
      height: 96rpx;
      line-height: 96rpx;
      background: $primary-gradient;
      color: $text-color;
      font-size: 34rpx;
      font-weight: 600;
      border-radius: 48rpx;
      margin-top: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 10rpx 20rpx rgba(230, 202, 74, 0.25);
      transition: all 0.3s;
      border: 2rpx solid $border-color;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 5rpx 10rpx rgba(230, 202, 74, 0.25);
      }

      &:disabled {
        opacity: 0.7;
      }
    }
  }
}
</style>
