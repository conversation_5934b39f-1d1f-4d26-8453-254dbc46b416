<template>
  <view class="settings-container">
    <!-- 头像区域 -->
    <view class="settings-item">
      <text class="item-label">头像</text>
      <view class="item-content">
        <button
          class="avatar-wrapper"
          open-type="chooseAvatar"
          @chooseavatar="onChooseAvatar"
        >
          <image class="avatar" :src="userInfo.avatar" mode="aspectFill" />
        </button>
        <text class="iconfont icon-arrow"></text>
      </view>
    </view>

    <!-- 用户名区域 -->
    <view class="settings-item" @click="handleModifyNickname">
      <text class="item-label">用户名</text>
      <view class="item-content">
        <text>{{ userInfo.nickname }}</text>
        <text class="iconfont icon-arrow"></text>
      </view>
    </view>

    <!-- 性别区域 -->
    <view class="settings-item" @click="handleModifySex">
      <text class="item-label">性别</text>
      <view class="item-content">
        <text>{{
          userInfo.sex === 0 ? "女" : userInfo.sex === 1 ? "男" : "未设置"
        }}</text>
        <text class="iconfont icon-arrow"></text>
      </view>
    </view>

    <!-- 手机号区域 -->
    <view class="settings-item">
      <text class="item-label">手机号</text>
      <view class="item-content">
        <text @longpress="copyMobile">{{ userInfo.mobile || "未设置" }}</text>
      </view>
    </view>

    <!-- 修改密码入口 -->
    <view class="settings-item" @click="navigateToUpdatePassword">
      <text class="item-label">修改密码</text>
      <view class="item-content">
        <text class="iconfont icon-arrow"></text>
      </view>
    </view>

    <!-- 版本号区域 -->
    <view class="settings-item">
      <text class="item-label">当前版本</text>
      <view class="item-content">
        <text>{{ version }}</text>
      </view>
    </view>

    <!-- 重启小程序按钮 -->
    <view class="restart-btn" @click="handleRestart">重启小程序(修复bug)</view>

    <!-- 退出登录按钮 -->
    <view class="logout-btn" @click="handleLogout">退出登录</view>
  </view>
</template>

<script lang="ts" setup>
import { updateAvatar, updateUserInfo } from "@/api/user";
import { PRE_VERSION } from "@/config/index";
import { useUserStore } from "@/store/user";
import { computed, ref } from "vue";

const userStore = useUserStore();
const userInfo = computed(() => userStore.info);
const version = ref(PRE_VERSION);

const onChooseAvatar = async (e: any) => {
  try {
    const tempFilePath = e.detail.avatarUrl;
    await uploadAvatar(tempFilePath);
  } catch (error) {
    console.error("选择头像过程出错:", error);
  }
};

const uploadAvatar = async (avatarPath: string) => {
  try {
    const result = await updateAvatar(avatarPath);
    if (result.isSuccess()) {
      // 更新用户信息
      userStore.fetchUserInfo();
    }
  } catch (error) {
    console.error("上传头像失败:", error);
  }
};

const handleModifyNickname = () => {
  uni.showModal({
    title: "修改昵称",
    editable: true,
    placeholderText: "请输入新昵称",
    content: userInfo.value.nickname,
    success: async (res) => {
      if (res.confirm && res.content?.trim()) {
        const nickname = res.content.trim();
        const result = await updateUserInfo({
          ...userInfo.value,
          nickname,
        });
        if (result.isSuccess()) {
          userStore.fetchUserInfo();
        }
      }
    },
  });
};

const handleModifySex = () => {
  uni.showActionSheet({
    itemList: ["女", "男"],
    success: async (res) => {
      const sex = res.tapIndex;
      const result = await updateUserInfo({
        ...userInfo.value,
        sex,
      });
      if (result.isSuccess()) {
        userStore.fetchUserInfo();
      }
    },
  });
};

// 复制手机号到剪贴板
const copyMobile = () => {
  if (userInfo.value.mobile) {
    uni.setClipboardData({
      data: userInfo.value.mobile,
      success: () => {
        uni.showToast({
          title: "手机号已复制",
          icon: "success",
        });
      },
    });
  }
};

const navigateToUpdatePassword = () => {
  uni.navigateTo({
    url: "/user-package/update-password/update-password",
  });
};

const handleViewAgreement = () => {
  uni.showToast({
    title: "功能开发中",
    icon: "none",
  });
};

// 重启小程序
const handleRestart = () => {
  // 微信小程序退出当前小程序
  // @ts-ignore
  if (
    uni.getSystemInfoSync().platform === "android" ||
    uni.getSystemInfoSync().platform === "ios"
  ) {
    // 真机环境下执行退出操作
    // @ts-ignore
    if (wx && wx.exitMiniProgram) {
      // @ts-ignore
      wx.exitMiniProgram();
    }
  } else {
    // 开发环境提示
    uni.showToast({
      title: "开发环境无法完全退出，请手动退出",
      icon: "none",
      duration: 2000,
    });
  }
};

const handleLogout = () => {
  uni.showModal({
    title: "提示",
    content: "确定要退出登录吗？",
    success: (res) => {
      if (res.confirm) {
        userStore.logout();
        uni.navigateBack();
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.settings-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 12rpx;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #fff;
  margin-bottom: 2rpx;

  .item-label {
    font-size: 28rpx;
    color: #333;
  }

  .item-content {
    display: flex;
    align-items: center;

    text {
      font-size: 28rpx;
      color: #666;
      margin-right: 16rpx;
    }
  }

  .avatar-wrapper {
    width: auto;
    height: auto;
    background: transparent;
    border: none;
    line-height: normal;
    padding: 0;
    margin: 0;

    &::after {
      border: none;
    }
  }

  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 16rpx;
  }

  .iconfont {
    font-size: 28rpx;
    color: #999;
  }
}

.restart-btn {
  margin: 48rpx 32rpx 20rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border: 1rpx solid #999;
  color: #999;
  border-radius: 44rpx;
  font-size: 32rpx;
}

.logout-btn {
  margin: 20rpx 32rpx 48rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #f3e482;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
}
</style>
