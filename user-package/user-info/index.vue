<template>
  <view class="user-info-container">
    <view class="form-group">
      <!-- 头像 -->
      <button
        class="avatar-wrapper"
        open-type="chooseAvatar"
        @chooseavatar="chooseAvatar"
      >
        <image
          class="avatar"
          :src="userInfo.avatar || '/static/tabbar/smile.png'"
          mode="aspectFill"
        />
        <view class="camera-icon">
          <image src="/static/icons/camera.png" mode="aspectFit" />
        </view>
      </button>

      <view class="divider"></view>

      <view class="input-item">
        <view class="item-content">
          <text class="label">昵称</text>
          <input
            class="input"
            type="nickname"
            placeholder="请输入昵称"
            v-model="userInfo.nickname"
            @input="onInputNickname"
            @nicknamereview="onNicknameReview"
          />
        </view>
      </view>

      <view class="input-item">
        <view class="item-content">
          <text class="label">性别</text>
          <view class="radio-group">
            <view
              class="radio"
              :class="{ active: userInfo.sex === 0 }"
              @tap="onSelectSex(0)"
            >
              <text>女生</text>
            </view>
            <view
              class="radio"
              :class="{ active: userInfo.sex === 1 }"
              @tap="onSelectSex(1)"
            >
              <text>男生</text>
            </view>
          </view>
        </view>
      </view>

      <view class="bottom-buttons">
        <button class="submit-btn" @tap="submitInfo">确定</button>
        <view class="skip-btn" @tap="skipUpdate">跳过</view>
      </view>
    </view>

    <view class="upload-progress" v-if="showUploadProgress">
      <view class="mask"></view>
      <view class="progress-content">
        <text>头像上传中...</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { updateAvatar, updateUserInfo } from "@/api/user";
import { useUserStore } from "@/store/user";

const userStore = useUserStore();

const userInfo = reactive({
  avatar: "",
  nickname: "",
  sex: 1, // 1-男，0-女
});

const showUploadProgress = ref(false);

// 微信小程序选择头像
const chooseAvatar = async (e: any) => {
  try {
    console.log("选择头像事件:", e);
    const { avatarUrl } = e.detail;
    console.log("获取到的头像URL:", avatarUrl);

    userInfo.avatar = avatarUrl;
    await uploadAvatar(avatarUrl);
  } catch (error) {
    console.error("选择头像过程出错:", error);
  }
};

// 上传头像
const uploadAvatar = async (avatarPath: string) => {
  try {
    showUploadProgress.value = true;
    const result = await updateAvatar(avatarPath);
    if (result.isSuccess()) {
      // 更新用户信息
      await userStore.fetchUserInfo();
    }
  } catch (error) {
    console.error("上传头像失败:", error);
  } finally {
    showUploadProgress.value = false;
  }
};

const onInputNickname = (e: any) => {
  userInfo.nickname = e.detail.value;
};

const onSelectSex = (sex: number) => {
  userInfo.sex = sex;
};

const submitInfo = async () => {
  try {
    if (!userInfo.nickname.trim()) {
      return uni.showToast({
        title: "请输入昵称",
        icon: "none",
      });
    }

    uni.showLoading({
      title: "保存中...",
      mask: true,
    });

    // 只更新昵称和性别
    const result = await updateUserInfo({
      nickname: userInfo.nickname.trim(),
      sex: userInfo.sex,
    });

    if (result.isSuccess()) {
      uni.hideLoading();
      uni.showToast({
        title: "保存成功",
        icon: "success",
      });

      // 更新用户信息
      await userStore.fetchUserInfo();

      setTimeout(() => {
        uni.switchTab({
          url: "/pages/mine/mine",
        });
      }, 800);
    }
  } catch (error) {
    console.error("保存失败:", error);
    uni.hideLoading();
    uni.showToast({
      title: "保存失败，请重试",
      icon: "none",
    });
  }
};

const skipUpdate = () => {
  uni.switchTab({
    url: "/pages/mine/mine",
  });
};

const onNicknameReview = (e: any) => {
  // 获取微信返回的昵称
  const nickname = e.detail.value;
  if (nickname) {
    userInfo.nickname = nickname;
  }
};
</script>

<style lang="scss" scoped>
.user-info-container {
  min-height: 100vh;
  background-color: #fff;
  padding: 0 30rpx;
  display: flex;
  flex-direction: column;
}

.form-group {
  flex: 1;
  padding: 40rpx 0;

  .avatar-wrapper {
    width: 180rpx;
    height: 180rpx;
    margin: 0 auto;
    padding: 0;
    position: relative;
    background: none;
    margin-bottom: 40rpx;
    border: none;

    &::after {
      border: none;
    }

    .avatar {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background-color: #f5f5f5;
    }

    .camera-icon {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 48rpx;
      height: 48rpx;
      background: #3478f6;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 28rpx;
        height: 28rpx;
      }
    }
  }

  .divider {
    height: 20rpx;
    background: #f8f9fa;
    margin: 0 -30rpx 40rpx;
  }

  .input-item {
    padding: 32rpx 0;
    border-bottom: 1rpx solid #eeeeee;

    .item-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .label {
        font-size: 32rpx;
        color: #333333;
      }

      .input {
        flex: 1;
        font-size: 32rpx;
        color: #333333;
        text-align: right;
        margin-left: 40rpx;
      }

      .radio-group {
        display: flex;
        gap: 20rpx;

        .radio {
          display: flex;
          align-items: center;
          padding: 0 20rpx;
          height: 56rpx;
          border-radius: 28rpx;
          font-size: 28rpx;
          color: #666666;
          position: relative;

          &::before {
            content: "";
            width: 32rpx;
            height: 32rpx;
            border: 2rpx solid #dddddd;
            border-radius: 50%;
            margin-right: 8rpx;
          }

          &.active {
            color: #3478f6;

            &::before {
              border-color: #3478f6;
              background: #3478f6;
              box-shadow: inset 0 0 0 8rpx #fff;
            }
          }
        }
      }
    }
  }
}

.bottom-buttons {
  padding: 0 0 80rpx;
  margin-top: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;

  .submit-btn {
    width: 100%;
    height: 96rpx;
    background: #3478f6;
    border-radius: 48rpx;
    color: #ffffff;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
  }

  .skip-btn {
    font-size: 28rpx;
    color: #666666;
  }
}

.upload-progress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;

  .mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(4rpx);
  }

  .progress-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: 40rpx 60rpx;
    border-radius: 20rpx;
    text-align: center;
    font-size: 28rpx;
    color: #333;
    box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.1);

    &::before {
      content: "";
      display: block;
      width: 80rpx;
      height: 80rpx;
      margin: 0 auto 20rpx;
      border: 4rpx solid #3478f6;
      border-top-color: transparent;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
