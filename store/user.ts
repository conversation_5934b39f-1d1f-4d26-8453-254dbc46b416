import {
  logout as apiLogout,
  passwordLogin,
  smsLogin,
  weixinMiniAppLogin,
} from "@/api/auth";
import {
  getUserInfo,
  updateAvatar,
  updateNickname,
  updateUserInfo,
} from "@/api/user";
import type { LoginParams, TokenInfo, UserInfo, UserState } from "@/types/auth";
import { defineStore } from "pinia";

// 存储键名常量
const STORAGE_KEYS = {
  ACCESS_TOKEN: "ACCESS_TOKEN",
  REFRESH_TOKEN: "REFRESH_TOKEN",
  USER_INFO: "USER_INFO",
} as const;

// Store 定义
export const useUserStore = defineStore("user", {
  state: (): UserState => ({
    userInfo: uni.getStorageSync(STORAGE_KEYS.USER_INFO) || {},
    userId: 0,
    accessToken: uni.getStorageSync(STORAGE_KEYS.ACCESS_TOKEN) || "",
    refreshToken: uni.getStorageSync(STORAGE_KEYS.REFRESH_TOKEN) || "",
    expiresTime: 0,
    firstLogin: false,
  }),

  getters: {
    // 基础信息获取
    token: (state): string => state.accessToken,
    refreshTokenValue: (state): string => state.refreshToken,
    info: (state): UserInfo => state.userInfo,

    // 用户状态
    hasLogin: (state): boolean => !!state.accessToken,
    isProfileComplete: (state): boolean =>
      !!state.userInfo.nickname && !!state.userInfo.avatar,

    // 用户属性
    nickname: (state): string => state.userInfo.nickname || "",
    avatar: (state): string => state.userInfo.avatar || "",
  },

  actions: {
    // ============ 内部方法 ============
    // Token 相关操作
    setToken({
      userId,
      accessToken,
      refreshToken,
      expiresTime,
      firstLogin,
    }: TokenInfo) {
      this.userId = userId;
      this.accessToken = accessToken;
      this.refreshToken = refreshToken;
      this.expiresTime = expiresTime;
      this.firstLogin = firstLogin;
      // 持久化存储
      uni.setStorageSync(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
      uni.setStorageSync(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    },

    // 用户信息相关操作
    setUserInfo(info: UserInfo) {
      this.userInfo = info;
      // 持久化存储
      uni.setStorageSync(STORAGE_KEYS.USER_INFO, info);
    },

    // 清除登录信息
    clearLoginInfo() {
      this.userInfo = {} as UserInfo;
      this.accessToken = "";
      this.refreshToken = "";
      // 清除存储
      uni.removeStorageSync(STORAGE_KEYS.ACCESS_TOKEN);
      uni.removeStorageSync(STORAGE_KEYS.REFRESH_TOKEN);
      uni.removeStorageSync(STORAGE_KEYS.USER_INFO);
    },

    // ============ 业务方法 ============
    // 登录
    async login({ type, data }: LoginParams) {
      console.log("login", type, data);
      try {
        let res;
        switch (type) {
          case "password":
            res = await passwordLogin(data);
            break;
          case "sms":
            res = await smsLogin(data);
            break;
          case "wechat":
            res = await weixinMiniAppLogin(data);
            break;
          default:
            throw new Error("不支持的登录类型");
        }

        console.log("res", res);
        const tokenInfo = res.getData();
        this.setToken(tokenInfo);
        console.log("tokenInfo", tokenInfo);
        // 登录后自动获取用户信息
        await this.fetchUserInfo();
        return res;
      } catch (error) {
        console.error("登录失败:", error);
        throw error;
      }
    },

    // 登出
    async logout() {
      try {
        await apiLogout();
        this.clearLoginInfo();
        // 跳转到登录页
        uni.navigateTo({
          url: "/user-package/login/social",
        });
      } catch (error) {
        console.error("登出失败:", error);
        throw error;
      }
    },

    // 获取用户信息
    async fetchUserInfo() {
      try {
        const res = await getUserInfo();
        const userInfo = res.getData();
        this.setUserInfo(userInfo);
        return userInfo;
      } catch (error) {
        console.error("获取用户信息失败:", error);
        // 如果远程获取失败，尝试使用本地缓存
        const localUserInfo = uni.getStorageSync(STORAGE_KEYS.USER_INFO);
        if (localUserInfo) {
          this.setUserInfo(localUserInfo);
          return localUserInfo;
        }
        throw error;
      }
    },

    // 更新用户信息
    async updateUserInfo(info: Partial<UserInfo>) {
      try {
        const res = await updateUserInfo(info);
        // 更新成功后，合并新的用户信息
        const newUserInfo = { ...this.userInfo, ...info };
        this.setUserInfo(newUserInfo);
        return res;
      } catch (error) {
        console.error("更新用户信息失败:", error);
        throw error;
      }
    },

    // 更新用户头像
    async updateAvatar(filePath: string) {
      try {
        const res = await updateAvatar(filePath);
        if (res.code === 0) {
          const avatarUrl = res.data;
          await this.updateUserInfo({ avatar: avatarUrl });
          return avatarUrl;
        }
        throw new Error(res.msg || "更新头像失败");
      } catch (error) {
        console.error("更新头像失败:", error);
        throw error;
      }
    },

    // 更新用户昵称
    async updateNickname(nickname: string) {
      try {
        const res = await updateNickname(nickname);
        if (res.code === 0) {
          await this.updateUserInfo({ nickname });
          return true;
        }
        throw new Error(res.msg || "更新昵称失败");
      } catch (error) {
        console.error("更新昵称失败:", error);
        throw error;
      }
    },
  },
});
