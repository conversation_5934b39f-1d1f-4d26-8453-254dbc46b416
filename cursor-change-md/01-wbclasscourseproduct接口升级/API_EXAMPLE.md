# API 使用示例

## 商品分页接口响应示例

### 请求

```http
GET /wbclass/course-product/page?pageNo=1&pageSize=10
```

### 响应

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "Java全栈开发训练营",
        "description": "这是一个全面的Java开发训练营...",
        "coverUrl": "https://example.com/cover.jpg",
        "originalPrice": 19900,
        "salePrice": 9900,
        "viewCount": 1000,
        "salesCount": 100,
        "status": 1,
        "sort": 1,
        "createTime": "2024-01-01T12:00:00",
        "extraSkus": {
          "skus": [
            {
              "id": 1,
              "productId": 1,
              "skuName": "基础版",
              "skuCode": "JAVA-001-BASIC",
              "originalPrice": 19900,
              "salePrice": 9900,
              "stock": 100,
              "salesCount": 50,
              "sort": 1,
              "status": 1,
              "courses": [
                {
                  "id": 1,
                  "name": "Java基础课程",
                  "coverUrl": "https://example.com/course1.jpg"
                }
              ]
            },
            {
              "id": 2,
              "productId": 1,
              "skuName": "进阶版",
              "skuCode": "JAVA-001-ADVANCED",
              "originalPrice": 29900,
              "salePrice": 19900,
              "stock": 50,
              "salesCount": 30,
              "sort": 2,
              "status": 1,
              "courses": [
                {
                  "id": 1,
                  "name": "Java基础课程",
                  "coverUrl": "https://example.com/course1.jpg"
                },
                {
                  "id": 2,
                  "name": "Java进阶课程",
                  "coverUrl": "https://example.com/course2.jpg"
                }
              ]
            }
          ],
          "minPrice": 9900,
          "maxPrice": 19900,
          "hasMultipleSkus": true
        }
      }
    ],
    "total": 1
  },
  "msg": "操作成功"
}
```

## 前端使用示例

### 显示价格

```javascript
// 从 extraSkus 属性获取价格信息
const product = response.data.list[0];
const { minPrice, maxPrice, hasMultipleSkus } = product.extraSkus;

// 显示价格
if (hasMultipleSkus && minPrice !== maxPrice) {
  // 有多个SKU且价格不同
  console.log(`价格范围: ¥${minPrice / 100} - ¥${maxPrice / 100}`);
} else {
  // 单一价格
  console.log(`价格: ¥${minPrice / 100}`);
}
```

### 获取 SKU 详情

```javascript
// 从 extraSkus.skus 获取完整的SKU信息
const skus = product.extraSkus.skus;
skus.forEach((sku) => {
  console.log(`SKU: ${sku.skuName}, 价格: ¥${sku.salePrice / 100}`);
  console.log(`包含课程: ${sku.courses.map((c) => c.name).join(", ")}`);
});
```

## 关键优势

1. **价格信息直接可用**: `extraSkus.minPrice` 和 `extraSkus.maxPrice` 提供了直接的价格信息
2. **完整的 SKU 详情**: `extraSkus.skus` 包含了所有 SKU 的详细信息，包括关联的课程
3. **便于 UI 展示**: `hasMultipleSkus` 标识可以帮助前端决定如何展示价格（单一价格 vs 价格范围）
4. **架构清晰**: 采用 `extraXXX` 命名规范，所有非主表属性统一管理
5. **代码规范**: 使用 `fillExtraXXX` 方法统一填充扩展属性，便于维护和扩展
