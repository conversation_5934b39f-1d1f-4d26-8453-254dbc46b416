# 训练营课程商品接口升级

## 概述

本次升级将原有的基于 course 的接口升级为基于 product 的接口，新增了 `wbclasscourseproduct` 文件夹和相关的 controller。

## 新增文件结构

```
yudao-module-wbclass/yudao-module-wbclass-biz/src/main/java/cn/iocoder/yudao/module/wbclass/controller/app/wbclasscourseproduct/
├── AppWbClassCourseProductController.java
└── vo/
    ├── AppWbClassCourseProductPageReqVO.java
    ├── AppWbClassCourseProductRespVO.java
    ├── AppWbClassCourseProductSkuRespVO.java
    └── AppWbClassCourseProductOrderCreateReqVO.java
```

## 新增接口

### 1. 获得课程商品分页

- **接口地址**: `GET /wbclass/course-product/page`
- **功能**: 获取课程商品分页列表，包含 SKU 信息和关联课程，**新增 extraSkus 属性包含价格信息**
- **参数**:
  - `name`: 商品名称（可选）
  - `status`: 状态（可选）
  - `pageNo`: 页码
  - `pageSize`: 每页大小
- **响应结构**:
  ```json
  {
    "id": 1024,
    "name": "Java全栈开发训练营",
    "description": "商品描述",
    "coverUrl": "封面图片URL",
    "extraSkus": {
      "skus": [...],
      "minPrice": 9900,
      "maxPrice": 19900,
      "hasMultipleSkus": true
    }
  }
  ```

### 2. 获得课程商品详情

- **接口地址**: `GET /wbclass/course-product/get`
- **功能**: 获取课程商品详情，自动增加浏览量，**包含 extraSkus 价格信息**
- **参数**:
  - `id`: 商品 ID（必填）

### 3. 创建课程商品订单

- **接口地址**: `POST /wbclass/course-product/order/create`
- **功能**: 基于商品 SKU 创建订单
- **参数**:
  - `productId`: 商品 ID（必填）
  - `skuId`: SKU ID（必填）
  - `userRemark`: 用户备注（可选）

## 技术实现

### 1. VO 类

- `AppWbClassCourseProductPageReqVO`: 分页请求参数
- `AppWbClassCourseProductRespVO`: 商品响应数据，包含**extraSkus 扩展信息**
- `AppWbClassCourseProductSkuRespVO`: SKU 响应数据，包含关联课程
- `AppWbClassCourseProductOrderCreateReqVO`: 订单创建请求参数
- `AppWbClassCourseProductExtraVO`: **新增**SKU 扩展信息 VO，包含价格范围和 SKU 详情

### 2. Convert 类扩展

- `WbClassCourseProductConvert`: 新增 App 端转换方法
- `WbClassCourseProductSkuConvert`: 新增 App 端 SKU 转换方法

### 3. Service 层扩展

- `WbClassCourseProductService`: 新增 `getAppProductPage` 方法
- `WbClassCourseProductServiceImpl`: 实现 App 端分页查询
- `WbClassCourseProductMapper`: 新增 `selectAppPage` 方法，只显示上架商品

### 4. Controller 实现

- `AppWbClassCourseProductController`: 主要的 App 端控制器
  - 商品分页查询（包含 SKU 和课程信息）
  - 商品详情查询（自动增加浏览量）
  - 订单创建（兼容原有订单系统）
  - **新增 fillExtraSkus 方法**：统一填充 SKU 扩展信息

## 与原有系统的兼容性

1. **订单系统**: 新接口创建的订单与原有订单系统完全兼容
2. **数据结构**: 复用现有的商品、SKU、课程数据表
3. **权限控制**: 继承原有的权限控制机制

## 使用建议

1. 前端可以逐步迁移到新的 product 接口
2. 新接口提供了更完整的商品信息（包含 SKU 和课程关联）
3. 订单创建接口保持了与原有系统的兼容性

## ExtraSkus 属性说明

新增的 `extraSkus` 属性包含以下信息：

- `skus`: 完整的 SKU 列表，包含价格和课程信息
- `minPrice`: 所有 SKU 中的最低价格（分）
- `maxPrice`: 所有 SKU 中的最高价格（分）
- `hasMultipleSkus`: 是否有多个 SKU 选项

这样前端可以直接从 `extraSkus.minPrice` 和 `extraSkus.maxPrice` 获取价格信息进行显示。

## 设计原则

1. **Extra 前缀规范**: 所有非主表属性都以 `extra` 开头，如 `extraSkus`
2. **FillExtra 方法规范**: 使用 `fillExtraXXX` 方法来统一填充扩展属性
3. **数据分离**: 主表数据和扩展数据分离，便于维护和扩展

## 注意事项

1. App 端分页查询只显示状态为"上架"(status=1)的商品
2. 商品详情查询会自动增加浏览量
3. 订单创建需要用户登录认证
4. **重要**: 新增的 `extraSkus` 属性包含了价格信息，前端可以直接使用来显示商品价格
5. **架构优化**: 采用 `fillExtraXXX` 方法统一管理扩展属性，代码更清晰易维护
