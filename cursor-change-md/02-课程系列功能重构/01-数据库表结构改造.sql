-- 课程系列功能 - 数据库表结构改造
-- 为课程表增加系列相关字段，实现课程系列分组功能

-- 1. 为课程表增加系列相关字段
ALTER TABLE `edusys_wbclass_course` 
ADD COLUMN `series_name` varchar(255) DEFAULT NULL COMMENT '系列名称（如：RE 阅读营）',
ADD COLUMN `series_order` int DEFAULT NULL COMMENT '系列内排序（用于确定优先级，数字越小优先级越高）',
ADD COLUMN `volume_name` varchar(100) DEFAULT NULL COMMENT '册名称（如：1-3册）';

-- 2. 添加索引以提高查询效率
ALTER TABLE `edusys_wbclass_course` 
ADD INDEX `idx_series_name` (`series_name`),
ADD INDEX `idx_series_order` (`series_name`, `series_order`);

-- 3. 更新现有课程数据示例（根据实际数据调整）
-- 假设已有的RE阅读营课程数据
UPDATE `edusys_wbclass_course` 
SET `series_name` = 'RE 阅读营', `series_order` = 1, `volume_name` = '1-3册'
WHERE `name` LIKE '%RE 阅读营%' AND `name` LIKE '%1-3%';

UPDATE `edusys_wbclass_course` 
SET `series_name` = 'RE 阅读营', `series_order` = 2, `volume_name` = '4-6册'
WHERE `name` LIKE '%RE 阅读营%' AND `name` LIKE '%4-6%';

UPDATE `edusys_wbclass_course` 
SET `series_name` = 'RE 阅读营', `series_order` = 3, `volume_name` = '7-9册'
WHERE `name` LIKE '%RE 阅读营%' AND `name` LIKE '%7-9%';

-- 4. 验证数据更新结果
SELECT 
    id,
    name,
    series_name,
    series_order,
    volume_name
FROM `edusys_wbclass_course`
WHERE `series_name` IS NOT NULL
ORDER BY `series_name`, `series_order`;
