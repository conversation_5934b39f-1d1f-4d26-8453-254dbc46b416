-- 课程系列功能测试数据
-- 为现有课程添加系列信息，用于测试课程系列功能

-- 1. 先查看现有课程数据
SELECT id, name, series_name, series_order, volume_name 
FROM edusys_wbclass_course 
ORDER BY id;

-- 2. 添加RE阅读营系列课程数据
-- 假设已有课程，更新其系列信息
UPDATE edusys_wbclass_course 
SET series_name = 'RE 阅读营', series_order = 1, volume_name = '1-3册'
WHERE name LIKE '%RE%' AND (name LIKE '%1-3%' OR name LIKE '%基础%' OR name LIKE '%入门%');

UPDATE edusys_wbclass_course 
SET series_name = 'RE 阅读营', series_order = 2, volume_name = '4-6册'
WHERE name LIKE '%RE%' AND (name LIKE '%4-6%' OR name LIKE '%进阶%' OR name LIKE '%中级%');

UPDATE edusys_wbclass_course 
SET series_name = 'RE 阅读营', series_order = 3, volume_name = '7-9册'
WHERE name LIKE '%RE%' AND (name LIKE '%7-9%' OR name LIKE '%高级%' OR name LIKE '%提高%');

-- 3. 如果没有现有课程，创建测试课程数据
INSERT INTO edusys_wbclass_course (
    name, 
    cover_url, 
    description, 
    short_description, 
    sort, 
    status,
    series_name,
    series_order,
    volume_name,
    creator,
    create_time,
    updater,
    update_time,
    deleted,
    tenant_id
) VALUES 
-- RE 阅读营系列
('RE 阅读营 1-3册', 'https://example.com/re-1-3.jpg', 'RE阅读营基础课程，适合初学者', '基础阅读理解训练', 1, 1, 'RE 阅读营', 1, '1-3册', 'admin', NOW(), 'admin', NOW(), 0, 1),
('RE 阅读营 4-6册', 'https://example.com/re-4-6.jpg', 'RE阅读营进阶课程，提升阅读技巧', '进阶阅读理解训练', 2, 1, 'RE 阅读营', 2, '4-6册', 'admin', NOW(), 'admin', NOW(), 0, 1),
('RE 阅读营 7-9册', 'https://example.com/re-7-9.jpg', 'RE阅读营高级课程，掌握高级阅读技巧', '高级阅读理解训练', 3, 1, 'RE 阅读营', 3, '7-9册', 'admin', NOW(), 'admin', NOW(), 0, 1),

-- 1000词单词营系列
('1000词单词营 Level1', 'https://example.com/1000-level1.jpg', '1000个核心单词学习，Level1基础词汇', '基础词汇学习', 4, 1, '1000词单词营', 1, 'Level1', 'admin', NOW(), 'admin', NOW(), 0, 1),
('1000词单词营 Level2', 'https://example.com/1000-level2.jpg', '1000个核心单词学习，Level2进阶词汇', '进阶词汇学习', 5, 1, '1000词单词营', 2, 'Level2', 'admin', NOW(), 'admin', NOW(), 0, 1),
('1000词单词营 Level3', 'https://example.com/1000-level3.jpg', '1000个核心单词学习，Level3高级词汇', '高级词汇学习', 6, 1, '1000词单词营', 3, 'Level3', 'admin', NOW(), 'admin', NOW(), 0, 1),

-- Timed reading阅读营系列
('Timed reading阅读营 初级', 'https://example.com/timed-basic.jpg', '限时阅读训练，提升阅读速度和理解能力', '初级限时阅读', 7, 1, 'Timed reading阅读营', 1, '初级', 'admin', NOW(), 'admin', NOW(), 0, 1),
('Timed reading阅读营 中级', 'https://example.com/timed-medium.jpg', '限时阅读训练，中级难度挑战', '中级限时阅读', 8, 1, 'Timed reading阅读营', 2, '中级', 'admin', NOW(), 'admin', NOW(), 0, 1),
('Timed reading阅读营 高级', 'https://example.com/timed-advanced.jpg', '限时阅读训练，高级难度挑战', '高级限时阅读', 9, 1, 'Timed reading阅读营', 3, '高级', 'admin', NOW(), 'admin', NOW(), 0, 1),

-- 单独的课程（不属于任何系列）
('英语语法专项训练', 'https://example.com/grammar.jpg', '英语语法专项训练课程', '语法基础强化', 10, 1, NULL, NULL, NULL, 'admin', NOW(), 'admin', NOW(), 0, 1),
('英语口语实战', 'https://example.com/speaking.jpg', '英语口语实战训练课程', '口语能力提升', 11, 1, NULL, NULL, NULL, 'admin', NOW(), 'admin', NOW(), 0, 1);

-- 4. 为测试用户添加课程关联（假设用户ID为1）
-- 注意：这里需要根据实际的用户ID进行调整
INSERT INTO edusys_wbclass_course_user_relation (
    user_id,
    course_id,
    course_name,
    acquire_type,
    acquire_time,
    status,
    creator,
    create_time,
    updater,
    update_time,
    deleted,
    tenant_id
) 
SELECT 
    1 as user_id,
    id as course_id,
    name as course_name,
    1 as acquire_type, -- 购买获得
    NOW() as acquire_time,
    1 as status, -- 有效
    'admin' as creator,
    NOW() as create_time,
    'admin' as updater,
    NOW() as update_time,
    0 as deleted,
    1 as tenant_id
FROM edusys_wbclass_course 
WHERE series_name IN ('RE 阅读营', '1000词单词营')
AND NOT EXISTS (
    SELECT 1 FROM edusys_wbclass_course_user_relation 
    WHERE user_id = 1 AND course_id = edusys_wbclass_course.id
);

-- 5. 验证数据
-- 查看课程系列数据
SELECT 
    series_name,
    series_order,
    volume_name,
    name,
    status
FROM edusys_wbclass_course 
WHERE series_name IS NOT NULL
ORDER BY series_name, series_order;

-- 查看用户拥有的课程
SELECT 
    c.series_name,
    c.series_order,
    c.volume_name,
    c.name,
    cur.acquire_time
FROM edusys_wbclass_course_user_relation cur
JOIN edusys_wbclass_course c ON cur.course_id = c.id
WHERE cur.user_id = 1 AND cur.status = 1
ORDER BY c.series_name, c.series_order;
