# 课程系列功能重构说明文档

## 功能概述

本次重构实现了课程系列分组功能，让用户在前端页面的tab中看到系列名称而不是具体的课程名称，点击系列tab后可以查看该系列的默认课程，并支持在系列内切换不同的册。

## 数据库改造

### 1. 课程表字段新增

在 `edusys_wbclass_course` 表中新增了以下字段：

- `series_name` varchar(255) - 系列名称（如：RE 阅读营）
- `series_order` int - 系列内排序（数字越小优先级越高）
- `volume_name` varchar(100) - 册名称（如：1-3册）

### 2. 索引优化

添加了以下索引以提高查询效率：
- `idx_series_name` - 系列名称索引
- `idx_series_order` - 系列名称和排序的复合索引

## 后端改造

### 1. 实体类更新

- 更新了 `WbClassCourseDO` 实体类，添加系列相关字段
- 更新了 `WbClassCourseBaseVO` 基础VO类
- 更新了 `AppWbClassCourseRespVO` 响应VO类

### 2. 新增VO类

- `AppWbClassCourseSeriesRespVO` - 课程系列响应VO
- `AppWbClassUserCourseSeriesRespVO` - 用户课程系列响应VO

### 3. 业务逻辑增强

- 在 `WbClassCourseUserRelationService` 中新增 `getCoursesByUserIdGroupBySeries` 方法
- 创建了 `WbClassCourseSeriesConvert` 转换器处理系列数据转换

### 4. API接口新增

- 新增 `/wbclass/course/my-course-series` 接口，返回按系列分组的课程数据

## 前端改造

### 1. 移动端（uni-app）

#### API层
- 在 `api/homework.ts` 中新增了课程系列相关的类型定义和API方法
- 新增 `getMyCoursesSeries()` 方法调用后端接口

#### 页面改造
- 重构了 `pages/study/study.vue` 页面
- 实现了课程系列tab显示
- 支持系列内课程切换功能
- 添加了课程详情展示

#### 功能特性
- Tab显示系列名称而不是具体课程名称
- 点击系列tab显示该系列的默认课程（优先级最高的）
- 支持在系列内切换不同册的课程
- 保持原有的"我的作业"功能不变

### 2. 后台管理界面（Vue）

#### 课程管理页面增强
- 在课程列表中新增"系列名称"和"册名称"显示列
- 在课程创建/编辑表单中新增系列相关字段
- 在课程详情查看中新增系列信息显示
- 在搜索表单中新增系列名称搜索功能

#### 表单字段
- 系列名称：支持输入系列名称，相同系列名称的课程会被分组
- 册名称：支持输入册名称，用于区分系列内的不同册
- 系列内排序：数字越小在系列中排序越靠前

## 使用说明

### 1. 创建课程系列

1. 在后台管理界面创建或编辑课程
2. 填写相同的"系列名称"（如：RE 阅读营）
3. 填写不同的"册名称"（如：1-3册、4-6册）
4. 设置"系列内排序"（1、2、3...）

### 2. 前端展示效果

1. 用户在学习页面看到的tab是系列名称
2. 点击系列tab后显示该系列优先级最高的课程
3. 可以通过切换按钮选择系列内的其他册
4. 单独的课程（没有系列）仍然单独显示

### 3. 数据兼容性

- 对于没有设置系列信息的课程，系列相关字段为NULL
- 这些课程会作为"单独课程"在前端显示
- 不影响现有的课程和用户关联数据

## 测试数据

执行 `cursor-change-md/02-课程系列功能重构/02-测试数据.sql` 文件可以创建测试数据，包括：

- RE 阅读营系列（1-3册、4-6册、7-9册）
- 1000词单词营系列（Level1、Level2、Level3）
- Timed reading阅读营系列（初级、中级、高级）
- 单独的课程（英语语法专项训练、英语口语实战）

## 注意事项

1. 系列名称相同的课程会被分组显示
2. 系列内排序决定了默认显示的课程和切换顺序
3. 册名称用于在切换时显示给用户
4. 后台管理界面支持按系列名称搜索课程
5. 所有改动都向后兼容，不影响现有功能

## 技术要点

1. 使用了Java 8兼容的API，避免使用新版本特性
2. 前端使用了响应式设计，支持移动端显示
3. 后端使用了MapStruct进行对象转换
4. 数据库查询进行了索引优化
5. API设计遵循RESTful规范

## 部署步骤

1. 执行数据库升级脚本 `01-数据库表结构改造.sql`
2. 部署后端代码
3. 部署前端代码（移动端和管理端）
4. 执行测试数据脚本 `02-测试数据.sql`（可选）
5. 验证功能是否正常工作
