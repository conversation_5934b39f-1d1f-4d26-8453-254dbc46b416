# 课程系列功能测试指南

## 测试前准备

### 1. 数据库准备
执行以下SQL脚本：
```bash
# 1. 执行数据库结构升级
mysql -u root -p your_database < cursor-change-md/02-课程系列功能重构/01-数据库表结构改造.sql

# 2. 执行测试数据插入
mysql -u root -p your_database < cursor-change-md/02-课程系列功能重构/02-测试数据.sql
```

### 2. 后端部署
1. 重新编译并启动后端服务
2. 确保新的API接口 `/app-api/wbclass/course/my-course-series` 可以正常访问

### 3. 前端部署
1. 重新编译并部署uni-app前端
2. 确保新的API调用路径正确

## 测试步骤

### 1. 后台管理界面测试

#### 1.1 课程管理页面
1. 登录后台管理系统
2. 进入 "练习营管理" -> "课程管理"
3. 验证列表页面：
   - 应该能看到"系列名称"和"册名称"列
   - 对于有系列信息的课程，应该显示相应的系列名称和册名称
   - 对于没有系列信息的课程，应该显示"-"

#### 1.2 课程创建/编辑
1. 点击"新增课程"按钮
2. 验证表单中有以下新字段：
   - 系列名称（可选）
   - 册名称（可选）
   - 系列内排序（可选）
3. 创建一个测试课程：
   - 系列名称：测试系列
   - 册名称：测试册1
   - 系列内排序：1
4. 保存并验证数据是否正确保存

#### 1.3 搜索功能
1. 在搜索框中输入系列名称进行搜索
2. 验证能够正确筛选出对应系列的课程

### 2. 移动端界面测试

#### 2.1 学习页面基本功能
1. 打开uni-app应用
2. 进入"学习"页面
3. 验证tab显示：
   - 应该看到"我的作业"tab
   - 应该看到系列名称的tab（如"RE 阅读营"、"1000词单词营"等）
   - 不应该看到具体的课程名称（如"RE 阅读营 1-3册"）

#### 2.2 课程系列切换功能
1. 点击某个系列tab（如"RE 阅读营"）
2. 验证显示内容：
   - 应该显示课程切换按钮（如"1-3册"、"4-6册"、"7-9册"）
   - 默认选中优先级最高的课程（series_order最小的）
   - 显示当前选中课程的详细信息

#### 2.3 系列内课程切换
1. 在系列页面中，点击不同的册按钮
2. 验证：
   - 按钮状态正确切换（选中状态高亮）
   - 课程详情正确更新
   - 显示对应课程的封面、名称、描述等信息

#### 2.4 单独课程显示
1. 点击不属于任何系列的课程tab
2. 验证：
   - 直接显示课程详情
   - 不显示切换按钮

#### 2.5 作业功能保持不变
1. 点击"我的作业"tab
2. 验证：
   - 作业列表正常显示
   - 作业提交功能正常
   - 原有功能不受影响

## API测试

### 1. 直接API测试
使用Postman或类似工具测试：

```bash
# 获取用户课程系列数据
GET /app-api/wbclass/course/my-course-series
Authorization: Bearer {your_token}
```

期望返回格式：
```json
{
  "code": 0,
  "data": {
    "courseSeries": [
      {
        "seriesName": "RE 阅读营",
        "defaultCourse": {
          "id": 1,
          "name": "RE 阅读营 1-3册",
          "volumeName": "1-3册",
          "seriesOrder": 1
        },
        "allCourses": [
          {
            "id": 1,
            "name": "RE 阅读营 1-3册",
            "volumeName": "1-3册",
            "seriesOrder": 1
          },
          {
            "id": 2,
            "name": "RE 阅读营 4-6册",
            "volumeName": "4-6册",
            "seriesOrder": 2
          }
        ]
      }
    ],
    "individualCourses": [
      {
        "id": 10,
        "name": "英语语法专项训练"
      }
    ]
  },
  "msg": "操作成功"
}
```

## 常见问题排查

### 1. 前端显示仍然是具体课程名称
- 检查API调用是否使用了新的 `getMyCoursesSeries()` 方法
- 检查API路径是否正确：`/app-api/wbclass/course/my-course-series`
- 检查后端接口是否正常返回数据

### 2. 后端接口返回空数据
- 检查用户是否有课程关联数据
- 检查数据库中课程是否有系列信息
- 检查Service层的分组逻辑是否正确

### 3. 系列内课程切换不生效
- 检查前端状态管理是否正确
- 检查课程ID匹配逻辑
- 检查Vue响应式数据更新

### 4. 数据库查询性能问题
- 确认已添加必要的索引
- 检查查询语句是否使用了索引
- 监控查询执行时间

## 验收标准

### 功能验收
- [ ] 后台管理界面能正确显示和编辑系列信息
- [ ] 移动端tab显示系列名称而不是具体课程名称
- [ ] 点击系列tab能正确显示默认课程
- [ ] 系列内课程切换功能正常
- [ ] 单独课程正常显示
- [ ] 原有作业功能不受影响

### 性能验收
- [ ] 页面加载时间不超过3秒
- [ ] 课程切换响应时间不超过1秒
- [ ] 数据库查询时间在可接受范围内

### 兼容性验收
- [ ] 对于没有系列信息的课程能正常显示
- [ ] 现有用户数据不受影响
- [ ] 向后兼容原有API接口

## 回滚方案

如果发现严重问题需要回滚：

1. **数据库回滚**：
   ```sql
   ALTER TABLE edusys_wbclass_course 
   DROP COLUMN series_name,
   DROP COLUMN series_order,
   DROP COLUMN volume_name;
   ```

2. **代码回滚**：
   - 恢复原有的前端代码
   - 移除新增的后端接口和逻辑

3. **验证回滚**：
   - 确认原有功能正常
   - 确认数据完整性
