package cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 用户 App - 训练营课程商品订单创建 Request VO
 *
 * <AUTHOR>
 */
@ApiModel("用户 App - 训练营课程商品订单创建 Request VO")
@Data
public class AppWbClassCourseProductOrderCreateReqVO {

    @ApiModelProperty(value = "商品ID", required = true, example = "1024")
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @ApiModelProperty(value = "SKU ID", required = true, example = "2048")
    @NotNull(message = "SKU ID不能为空")
    private Long skuId;

    @ApiModelProperty(value = "用户备注", example = "请尽快安排课程")
    private String userRemark;

}
