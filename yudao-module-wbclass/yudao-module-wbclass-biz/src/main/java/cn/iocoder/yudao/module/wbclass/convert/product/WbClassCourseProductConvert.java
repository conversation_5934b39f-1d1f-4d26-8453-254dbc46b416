package cn.iocoder.yudao.module.wbclass.convert.product;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductRespVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductUpdateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo.AppWbClassCourseProductRespVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 训练营课程商品 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassCourseProductConvert {

    WbClassCourseProductConvert INSTANCE = Mappers.getMapper(WbClassCourseProductConvert.class);

    WbClassCourseProductDO convert(WbClassCourseProductCreateReqVO bean);

    WbClassCourseProductDO convert(WbClassCourseProductUpdateReqVO bean);

    WbClassCourseProductRespVO convert(WbClassCourseProductDO bean);

    List<WbClassCourseProductRespVO> convertList(List<WbClassCourseProductDO> list);

    PageResult<WbClassCourseProductRespVO> convertPage(PageResult<WbClassCourseProductDO> page);

    // App端转换
    AppWbClassCourseProductRespVO convertApp(WbClassCourseProductDO bean);

    List<AppWbClassCourseProductRespVO> convertAppList(List<WbClassCourseProductDO> list);

    PageResult<AppWbClassCourseProductRespVO> convertAppPage(PageResult<WbClassCourseProductDO> page);

}