package cn.iocoder.yudao.module.wbclass.service.product;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductPageReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductUpdateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo.AppWbClassCourseProductPageReqVO;
import cn.iocoder.yudao.module.wbclass.convert.product.WbClassCourseProductConvert;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductDO;
import cn.iocoder.yudao.module.wbclass.dal.mysql.product.WbClassCourseProductMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.wbclass.enums.ErrorCodeConstants.*;

/**
 * 训练营课程商品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WbClassCourseProductServiceImpl implements WbClassCourseProductService {

    @Resource
    private WbClassCourseProductMapper productMapper;

    @Resource
    private WbClassCourseProductSkuService skuService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createProduct(WbClassCourseProductCreateReqVO createReqVO) {
        // 校验SKU列表不能为空
        if (createReqVO.getSkus() == null || createReqVO.getSkus().isEmpty()) {
            throw exception(COURSE_SKU_REQUIRED);
        }
        
        // 转换对象
        WbClassCourseProductDO product = WbClassCourseProductConvert.INSTANCE.convert(createReqVO);
        // 设置默认值
        product.setViewCount(0);
        product.setSalesCount(0);
        // 插入
        productMapper.insert(product);
        
        // 保存SKU列表
        skuService.saveSkuList(product.getId(), createReqVO.getSkus());
        
        // 返回
        return product.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProduct(WbClassCourseProductUpdateReqVO updateReqVO) {
        // 校验存在
        validateProductExists(updateReqVO.getId());
        
        // 校验SKU列表不能为空
        if (updateReqVO.getSkus() == null || updateReqVO.getSkus().isEmpty()) {
            throw exception(COURSE_SKU_REQUIRED);
        }
        
        // 更新
        WbClassCourseProductDO updateObj = WbClassCourseProductConvert.INSTANCE.convert(updateReqVO);
        productMapper.updateById(updateObj);
        
        // 更新SKU列表
        skuService.saveSkuList(updateReqVO.getId(), updateReqVO.getSkus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProduct(Long id) {
        // 校验存在
        validateProductExists(id);
        // 删除SKU
        skuService.deleteSkusByProductId(id);
        // 删除商品
        productMapper.deleteById(id);
    }

    private void validateProductExists(Long id) {
        if (productMapper.selectById(id) == null) {
            throw exception(COURSE_PRODUCT_NOT_EXISTS);
        }
    }

    @Override
    public WbClassCourseProductDO getProduct(Long id) {
        return productMapper.selectById(id);
    }

    @Override
    public List<WbClassCourseProductDO> getProductList(Collection<Long> ids) {
        return productMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<WbClassCourseProductDO> getProductPage(WbClassCourseProductPageReqVO pageReqVO) {
        return productMapper.selectPage(pageReqVO);
    }

    @Override
    public void incrementViewCount(Long id) {
        WbClassCourseProductDO product = getProduct(id);
        if (product != null) {
            product.setViewCount(product.getViewCount() + 1);
            productMapper.updateById(product);
        }
    }

    @Override
    public void incrementSalesCount(Long id) {
        WbClassCourseProductDO product = getProduct(id);
        if (product != null) {
            product.setSalesCount(product.getSalesCount() + 1);
            productMapper.updateById(product);
        }
    }

    @Override
    public PageResult<WbClassCourseProductDO> getAppProductPage(AppWbClassCourseProductPageReqVO pageReqVO) {
        return productMapper.selectAppPage(pageReqVO);
    }

}