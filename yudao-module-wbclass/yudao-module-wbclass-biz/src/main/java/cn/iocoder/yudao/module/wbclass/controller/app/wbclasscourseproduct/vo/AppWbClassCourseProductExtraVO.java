package cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户 App - 训练营课程商品SKU扩展信息 VO
 *
 * <AUTHOR>
 */
@ApiModel("用户 App - 训练营课程商品SKU扩展信息 VO")
@Data
public class AppWbClassCourseProductExtraVO {

    @ApiModelProperty(value = "SKU列表")
    private List<AppWbClassCourseProductSkuRespVO> skus;

    @ApiModelProperty(value = "最低价格（分）", example = "9900")
    private Integer minPrice;

    @ApiModelProperty(value = "最高价格（分）", example = "19900")
    private Integer maxPrice;

    @ApiModelProperty(value = "是否有多个SKU", example = "true")
    private Boolean hasMultipleSkus;

}
