package cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 用户 App - 训练营课程商品分页 Request VO
 *
 * <AUTHOR>
 */
@ApiModel("用户 App - 训练营课程商品分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppWbClassCourseProductPageReqVO extends PageParam {

    @ApiModelProperty(value = "商品名称", example = "Java全栈开发训练营")
    private String name;

    @ApiModelProperty(value = "状态：1-上架，2-下架", example = "1")
    private Integer status;

}
