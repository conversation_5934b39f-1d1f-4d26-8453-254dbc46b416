package cn.iocoder.yudao.module.wbclass.service.course;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseUserRelationDO;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassCourseMapper;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassCourseUserRelationMapper;
import cn.iocoder.yudao.module.wbclass.enums.CourseAcquireTypeEnum;
import cn.iocoder.yudao.module.wbclass.enums.UserCourseStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程用户关联 Service 实现类
 * 
 * 提供课程与用户关联关系的业务逻辑，直接管理用户拥有的课程
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class WbClassCourseUserRelationServiceImpl implements WbClassCourseUserRelationService {

    @Resource
    private WbClassCourseUserRelationMapper relationMapper;
    @Resource
    private WbClassCourseMapper courseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCourseByPurchase(Long userId, Long courseId, String courseName, Long orderId, 
                                  Long skuId, Long productId, LocalDateTime expireTime, String remark) {
        addCourse(userId, courseId, courseName, CourseAcquireTypeEnum.PURCHASE, 
                 orderId, skuId, productId, expireTime, remark);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCourseByGift(Long userId, Long courseId, String courseName, LocalDateTime expireTime, String remark) {
        addCourse(userId, courseId, courseName, CourseAcquireTypeEnum.GIFT, 
                 null, null, null, expireTime, remark);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCourseByActivity(Long userId, Long courseId, String courseName, LocalDateTime expireTime, String remark) {
        addCourse(userId, courseId, courseName, CourseAcquireTypeEnum.ACTIVITY, 
                 null, null, null, expireTime, remark);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCourseByAdminAssign(Long userId, Long courseId, String courseName, LocalDateTime expireTime, String remark) {
        addCourse(userId, courseId, courseName, CourseAcquireTypeEnum.ADMIN_ASSIGN, 
                 null, null, null, expireTime, remark);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCoursesByPurchase(Long userId, List<WbClassCourseDO> courses, Long orderId, 
                                   Long skuId, Long productId, LocalDateTime expireTime) {
        if (CollUtil.isEmpty(courses)) {
            return;
        }

        for (WbClassCourseDO course : courses) {
            addCourseByPurchase(userId, course.getId(), course.getName(), orderId, 
                              skuId, productId, expireTime, null);
        }
    }

    /**
     * 通用的添加课程方法
     */
    private void addCourse(Long userId, Long courseId, String courseName, CourseAcquireTypeEnum acquireType,
                          Long orderId, Long skuId, Long productId, LocalDateTime expireTime, String remark) {
        // 检查是否已存在有效的关联关系
        if (relationMapper.existsValidByUserIdAndCourseId(userId, courseId)) {
            log.info("课程用户关联关系已存在，跳过创建: userId={}, courseId={}", userId, courseId);
            return;
        }

        WbClassCourseUserRelationDO relation = WbClassCourseUserRelationDO.builder()
                .userId(userId)
                .courseId(courseId)
                .courseName(courseName)
                .acquireType(acquireType.getType())
                .acquireTime(LocalDateTime.now())
                .orderId(orderId)
                .skuId(skuId)
                .productId(productId)
                .expireTime(expireTime)
                .status(UserCourseStatusEnum.VALID.getStatus())
                .remark(remark)
                .build();

        relationMapper.insert(relation);
        log.info("创建课程用户关联关系成功: userId={}, courseId={}, acquireType={}", 
                userId, courseId, acquireType.getName());
    }

    @Override
    public List<WbClassCourseDO> getCoursesByUserId(Long userId) {
        List<WbClassCourseUserRelationDO> relations = relationMapper.selectValidListByUserId(userId);
        if (CollUtil.isEmpty(relations)) {
            return new ArrayList<>();
        }

        List<Long> courseIds = relations.stream()
                .map(WbClassCourseUserRelationDO::getCourseId)
                .distinct()
                .collect(Collectors.toList());

        return courseMapper.selectBatchIds(courseIds);
    }

    @Override
    public List<WbClassCourseDO> getCoursesByUserIds(Collection<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        List<WbClassCourseUserRelationDO> relations = relationMapper.selectValidListByUserIds(userIds);
        if (CollUtil.isEmpty(relations)) {
            return new ArrayList<>();
        }

        List<Long> courseIds = relations.stream()
                .map(WbClassCourseUserRelationDO::getCourseId)
                .distinct()
                .collect(Collectors.toList());

        return courseMapper.selectBatchIds(courseIds);
    }

    @Override
    public List<WbClassCourseUserRelationDO> getUsersByCourseId(Long courseId) {
        return relationMapper.selectValidListByCourseId(courseId);
    }

    @Override
    public boolean hasUserCourse(Long userId, Long courseId) {
        return relationMapper.existsValidByUserIdAndCourseId(userId, courseId);
    }

    @Override
    public List<WbClassCourseUserRelationDO> getRelationsByUserId(Long userId) {
        return relationMapper.selectValidListByUserId(userId);
    }

    @Override
    public List<WbClassCourseUserRelationDO> getRelationsByOrderId(Long orderId) {
        return relationMapper.selectListByOrderId(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelCoursesByOrderId(Long orderId) {
        relationMapper.cancelByOrderId(orderId);
        log.info("取消订单的所有课程关联关系: orderId={}", orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExpiredCourses() {
        relationMapper.updateExpiredCourses();
        log.info("更新过期课程状态完成");
    }

    @Override
    public Map<String, List<WbClassCourseDO>> getCoursesByUserIdGroupBySeries(Long userId) {
        List<WbClassCourseDO> courses = getCoursesByUserId(userId);
        if (CollUtil.isEmpty(courses)) {
            return new HashMap<>();
        }

        // 按系列名称分组，没有系列的课程放在特殊的key下
        Map<String, List<WbClassCourseDO>> groupedCourses = courses.stream()
                .collect(Collectors.groupingBy(course ->
                    StrUtil.isNotBlank(course.getSeriesName()) ? course.getSeriesName() : "individual_courses"
                ));

        // 对每个系列内的课程按seriesOrder排序
        groupedCourses.forEach((seriesName, courseList) -> {
            if (!"individual_courses".equals(seriesName)) {
                courseList.sort((c1, c2) -> {
                    Integer order1 = c1.getSeriesOrder() != null ? c1.getSeriesOrder() : Integer.MAX_VALUE;
                    Integer order2 = c2.getSeriesOrder() != null ? c2.getSeriesOrder() : Integer.MAX_VALUE;
                    return order1.compareTo(order2);
                });
            }
        });

        return groupedCourses;
    }

}
