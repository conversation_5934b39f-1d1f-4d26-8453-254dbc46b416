package cn.iocoder.yudao.module.wbclass.dal.dataobject.course;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.wbclass.enums.CourseStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 练习营课程产品 DO
 *
 * <AUTHOR>
 */
@TableName("edusys_wbclass_course")
@KeySequence("edusys_wbclass_course_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WbClassCourseDO extends BaseDO {

    /**
     * 课程产品ID
     */
    @TableId
    private Long id;

    /**
     * 课程名称
     */
    private String name;

    /**
     * 课程封面图片
     */
    private String coverUrl;

    /**
     * 课程详情描述
     */
    private String description;

    /**
     * 课程简介
     */
    private String shortDescription;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 状态
     *
     * 枚举 {@link CourseStatusEnum}
     */
    private Integer status;

    /**
     * 系列名称（如：RE 阅读营）
     */
    private String seriesName;

    /**
     * 系列内排序（用于确定优先级，数字越小优先级越高）
     */
    private Integer seriesOrder;

    /**
     * 册名称（如：1-3册）
     */
    private String volumeName;

}