package cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo;

import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户 App - 训练营课程商品SKU Response VO
 *
 * <AUTHOR>
 */
@ApiModel("用户 App - 训练营课程商品SKU Response VO")
@Data
public class AppWbClassCourseProductSkuRespVO {

    @ApiModelProperty(value = "SKU ID", example = "1024")
    private Long id;

    @ApiModelProperty(value = "商品ID", example = "1")
    private Long productId;

    @ApiModelProperty(value = "SKU名称", example = "基础版")
    private String skuName;

    @ApiModelProperty(value = "SKU编码", example = "JAVA-001-BASIC")
    private String skuCode;

    @ApiModelProperty(value = "SKU原价（分）", example = "19900")
    private Integer originalPrice;

    @ApiModelProperty(value = "SKU售价（分）", example = "9900")
    private Integer salePrice;

    @ApiModelProperty(value = "库存", example = "100")
    private Integer stock;

    @ApiModelProperty(value = "销售数量", example = "50")
    private Integer salesCount;

    @ApiModelProperty(value = "排序", example = "1")
    private Integer sort;

    @ApiModelProperty(value = "状态：1-上架，2-下架", example = "1")
    private Integer status;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "关联的课程列表")
    private List<WbClassCourseDO> courses;

}
