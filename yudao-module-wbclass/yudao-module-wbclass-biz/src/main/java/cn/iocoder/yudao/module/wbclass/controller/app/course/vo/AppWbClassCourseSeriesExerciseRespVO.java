package cn.iocoder.yudao.module.wbclass.controller.app.course.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程系列练习数据响应 VO
 * 用于返回特定系列的练习数据
 *
 * <AUTHOR>
 */
@ApiModel("课程系列练习数据响应 VO")
@Data
public class AppWbClassCourseSeriesExerciseRespVO {

    @ApiModelProperty(value = "系列ID", required = true, example = "1")
    private Long seriesId;

    @ApiModelProperty(value = "系列名称", required = true, example = "RE 阅读营")
    private String seriesName;

    @ApiModelProperty(value = "今日任务", required = true)
    private TodayTask todayTask;

    @ApiModelProperty(value = "学习目录", required = true)
    private List<LessonInfo> lessons;

    @ApiModelProperty(value = "学习进度统计")
    private ProgressStats progressStats;

    @ApiModel("今日任务")
    @Data
    public static class TodayTask {
        @ApiModelProperty(value = "课程ID", example = "1")
        private Long courseId;

        @ApiModelProperty(value = "课程名称", example = "1000词单词营")
        private String courseName;

        @ApiModelProperty(value = "当前册名称", example = "Level1")
        private String volumeName;

        @ApiModelProperty(value = "当前课节", example = "Lesson 2")
        private String currentLesson;

        @ApiModelProperty(value = "课节标题", example = "Unit1 B篇-The Amalfi Coast")
        private String lessonTitle;

        @ApiModelProperty(value = "学习进度", example = "已学1节/共28节")
        private String progress;
    }

    @ApiModel("课节信息")
    @Data
    public static class LessonInfo {
        @ApiModelProperty(value = "课节ID", example = "1")
        private Long lessonId;

        @ApiModelProperty(value = "课节名称", example = "Lesson 1")
        private String lessonName;

        @ApiModelProperty(value = "课节标题", example = "Unit1 A篇-The Amalfi Coast")
        private String lessonTitle;

        @ApiModelProperty(value = "完成状态", example = "true")
        private Boolean completed;

        @ApiModelProperty(value = "课节类型", example = "reading")
        private String lessonType; // reading: 阅读, vocabulary: 词汇, exercise: 练习

        @ApiModelProperty(value = "排序", example = "1")
        private Integer sort;

        @ApiModelProperty(value = "课程ID", example = "1")
        private Long courseId;

        @ApiModelProperty(value = "册名称", example = "Level1")
        private String volumeName;
    }

    @ApiModel("进度统计")
    @Data
    public static class ProgressStats {
        @ApiModelProperty(value = "总课节数", example = "28")
        private Integer totalLessons;

        @ApiModelProperty(value = "已完成课节数", example = "1")
        private Integer completedLessons;

        @ApiModelProperty(value = "完成百分比", example = "3.6")
        private Double completionPercentage;

        @ApiModelProperty(value = "连续学习天数", example = "5")
        private Integer consecutiveDays;

        @ApiModelProperty(value = "本周学习天数", example = "3")
        private Integer weeklyStudyDays;
    }

}
