package cn.iocoder.yudao.module.wbclass.convert.product;

import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuRespVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuUpdateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuVO;
import cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo.AppWbClassCourseProductSkuRespVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductSkuDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 课程商品SKU Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassCourseProductSkuConvert {

    WbClassCourseProductSkuConvert INSTANCE = Mappers.getMapper(WbClassCourseProductSkuConvert.class);

    WbClassCourseProductSkuDO convert(WbClassCourseProductSkuVO bean);

    WbClassCourseProductSkuVO convert(WbClassCourseProductSkuDO bean);

    List<WbClassCourseProductSkuVO> convertList(List<WbClassCourseProductSkuDO> list);

    WbClassCourseProductSkuDO convert(WbClassCourseProductSkuCreateReqVO bean);

    WbClassCourseProductSkuDO convert(WbClassCourseProductSkuUpdateReqVO bean);

    WbClassCourseProductSkuRespVO convertToRespVO(WbClassCourseProductSkuDO bean);

    List<WbClassCourseProductSkuRespVO> convertToRespVOList(List<WbClassCourseProductSkuDO> list);

    // App端转换
    AppWbClassCourseProductSkuRespVO convertToAppRespVO(WbClassCourseProductSkuDO bean);

    List<AppWbClassCourseProductSkuRespVO> convertToAppRespVOList(List<WbClassCourseProductSkuDO> list);

}