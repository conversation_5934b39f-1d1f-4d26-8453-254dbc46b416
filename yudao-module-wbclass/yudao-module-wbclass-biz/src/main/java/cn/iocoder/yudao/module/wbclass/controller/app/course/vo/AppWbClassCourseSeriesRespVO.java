package cn.iocoder.yudao.module.wbclass.controller.app.course.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("用户 App - 课程系列 Response VO")
@Data
public class AppWbClassCourseSeriesRespVO {

    @ApiModelProperty(value = "系列名称", example = "RE 阅读营")
    private String seriesName;

    @ApiModelProperty(value = "默认课程（优先级最高的课程）")
    private AppWbClassCourseRespVO defaultCourse;

    @ApiModelProperty(value = "系列内所有课程列表")
    private List<AppWbClassCourseRespVO> allCourses;

}
