package cn.iocoder.yudao.module.wbclass.controller.app.course.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 课程分类响应 VO
 * 用于返回课程分类列表（我的作业 + 各个课程系列）
 *
 * <AUTHOR>
 */
@ApiModel("课程分类响应 VO")
@Data
public class AppWbClassCourseCategoryRespVO {

    @ApiModelProperty(value = "分类ID", required = true, example = "0")
    private Long id;

    @ApiModelProperty(value = "分类名称", required = true, example = "我的作业")
    private String name;

    @ApiModelProperty(value = "分类类型", required = true, example = "homework")
    private String type; // homework: 作业, series: 课程系列, individual: 单独课程

    @ApiModelProperty(value = "系列名称（仅当type=series时有值）", example = "RE 阅读营")
    private String seriesName;

    @ApiModelProperty(value = "排序", example = "1")
    private Integer sort;

}
