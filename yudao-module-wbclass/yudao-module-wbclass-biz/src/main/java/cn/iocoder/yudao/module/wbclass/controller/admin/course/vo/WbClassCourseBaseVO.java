package cn.iocoder.yudao.module.wbclass.controller.admin.course.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.Valid;
import java.util.List;

/**
 * 练习营课程产品 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class WbClassCourseBaseVO {

    @ApiModelProperty(value = "课程名称", required = true, example = "Java全栈开发练习营")
    @NotBlank(message = "课程名称不能为空")
    @Length(max = 255, message = "课程名称长度不能超过255个字符")
    private String name;

    @ApiModelProperty(value = "课程封面图片", example = "https://example.com/cover.jpg")
    @Length(max = 500, message = "课程封面图片URL长度不能超过500个字符")
    private String coverUrl;

    @ApiModelProperty(value = "课程详情描述", example = "这是一个全面的Java开发课程...")
    private String description;

    @ApiModelProperty(value = "课程简介", example = "适合零基础学员的Java课程")
    @Length(max = 500, message = "课程简介长度不能超过500个字符")
    private String shortDescription;

    @ApiModelProperty(value = "排序字段", example = "10")
    private Integer sort;

    @ApiModelProperty(value = "状态：1-上架，2-下架", required = true, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "系列名称", example = "RE 阅读营")
    @Length(max = 255, message = "系列名称长度不能超过255个字符")
    private String seriesName;

    @ApiModelProperty(value = "系列内排序", example = "1")
    private Integer seriesOrder;

    @ApiModelProperty(value = "册名称", example = "1-3册")
    @Length(max = 100, message = "册名称长度不能超过100个字符")
    private String volumeName;

}