package cn.iocoder.yudao.module.wbclass.controller.app.course.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("用户 App - 用户拥有的课程系列 Response VO")
@Data
public class AppWbClassUserCourseSeriesRespVO {

    @ApiModelProperty(value = "课程系列列表")
    private List<AppWbClassCourseSeriesRespVO> courseSeries;

    @ApiModelProperty(value = "单独的课程列表（不属于任何系列的课程）")
    private List<AppWbClassCourseRespVO> individualCourses;

}
