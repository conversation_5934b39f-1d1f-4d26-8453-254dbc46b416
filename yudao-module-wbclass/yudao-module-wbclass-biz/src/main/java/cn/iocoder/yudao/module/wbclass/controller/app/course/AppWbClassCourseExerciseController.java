package cn.iocoder.yudao.module.wbclass.controller.app.course;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseSeriesExerciseRespVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassCourseUserRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Api(tags = "用户App - 课程练习数据")
@RestController
@RequestMapping("/wbclass/course/exercise")
@Validated
public class AppWbClassCourseExerciseController {

    @Resource
    private WbClassCourseUserRelationService courseUserRelationService;

    @GetMapping("/series/{seriesId}")
    @ApiOperation("获得课程系列练习数据")
    @ApiImplicitParam(name = "seriesId", value = "系列ID", required = true, example = "1", dataTypeClass = Long.class)
    @PreAuthenticated
    public CommonResult<AppWbClassCourseSeriesExerciseRespVO> getSeriesExercise(@PathVariable("seriesId") Long seriesId) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 获取按系列分组的课程数据
        Map<String, List<WbClassCourseDO>> groupedCourses = courseUserRelationService.getCoursesByUserIdGroupBySeries(userId);
        
        // 根据seriesId找到对应的系列
        String targetSeriesName = null;
        List<WbClassCourseDO> seriesCourses = null;
        
        int currentIndex = 1;
        for (Map.Entry<String, List<WbClassCourseDO>> entry : groupedCourses.entrySet()) {
            String seriesName = entry.getKey();
            if (!"individual_courses".equals(seriesName)) {
                if (currentIndex == seriesId.intValue()) {
                    targetSeriesName = seriesName;
                    seriesCourses = entry.getValue();
                    break;
                }
                currentIndex++;
            }
        }
        
        if (targetSeriesName == null || seriesCourses == null) {
            return success(null);
        }
        
        // 构建练习数据响应（这里是模拟数据，实际应该从数据库获取）
        AppWbClassCourseSeriesExerciseRespVO result = buildExerciseData(seriesId, targetSeriesName, seriesCourses);
        
        return success(result);
    }

    @GetMapping("/individual/{courseId}")
    @ApiOperation("获得单独课程练习数据")
    @ApiImplicitParam(name = "courseId", value = "课程ID", required = true, example = "1", dataTypeClass = Long.class)
    @PreAuthenticated
    public CommonResult<AppWbClassCourseSeriesExerciseRespVO> getIndividualCourseExercise(@PathVariable("courseId") Long courseId) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 获取用户的课程
        List<WbClassCourseDO> userCourses = courseUserRelationService.getCoursesByUserId(userId);
        
        // 找到指定的课程
        WbClassCourseDO targetCourse = userCourses.stream()
            .filter(course -> course.getId().equals(courseId))
            .findFirst()
            .orElse(null);
        
        if (targetCourse == null) {
            return success(null);
        }
        
        // 构建单独课程的练习数据响应
        AppWbClassCourseSeriesExerciseRespVO result = buildIndividualCourseExerciseData(targetCourse);
        
        return success(result);
    }

    /**
     * 构建系列练习数据（模拟数据，实际应该从数据库获取）
     */
    private AppWbClassCourseSeriesExerciseRespVO buildExerciseData(Long seriesId, String seriesName, List<WbClassCourseDO> courses) {
        AppWbClassCourseSeriesExerciseRespVO result = new AppWbClassCourseSeriesExerciseRespVO();
        result.setSeriesId(seriesId);
        result.setSeriesName(seriesName);
        
        // 构建今日任务（模拟数据）
        if (!courses.isEmpty()) {
            WbClassCourseDO firstCourse = courses.get(0);
            AppWbClassCourseSeriesExerciseRespVO.TodayTask todayTask = new AppWbClassCourseSeriesExerciseRespVO.TodayTask();
            todayTask.setCourseId(firstCourse.getId());
            todayTask.setCourseName(seriesName);
            todayTask.setVolumeName(firstCourse.getVolumeName());
            todayTask.setCurrentLesson("Lesson 2");
            todayTask.setLessonTitle("Unit1 B篇-The Amalfi Coast");
            todayTask.setProgress("已学1节/共28节");
            result.setTodayTask(todayTask);
        }
        
        // 构建学习目录（模拟数据）
        // 实际应该从课节表中获取
        // TODO: 实现真实的课节数据获取逻辑
        
        // 构建进度统计（模拟数据）
        AppWbClassCourseSeriesExerciseRespVO.ProgressStats progressStats = new AppWbClassCourseSeriesExerciseRespVO.ProgressStats();
        progressStats.setTotalLessons(28);
        progressStats.setCompletedLessons(1);
        progressStats.setCompletionPercentage(3.6);
        progressStats.setConsecutiveDays(5);
        progressStats.setWeeklyStudyDays(3);
        result.setProgressStats(progressStats);
        
        return result;
    }

    /**
     * 构建单独课程练习数据（模拟数据）
     */
    private AppWbClassCourseSeriesExerciseRespVO buildIndividualCourseExerciseData(WbClassCourseDO course) {
        AppWbClassCourseSeriesExerciseRespVO result = new AppWbClassCourseSeriesExerciseRespVO();
        result.setSeriesId(1000L + course.getId()); // 使用1000+courseId作为ID
        result.setSeriesName(course.getName());
        
        // 构建今日任务
        AppWbClassCourseSeriesExerciseRespVO.TodayTask todayTask = new AppWbClassCourseSeriesExerciseRespVO.TodayTask();
        todayTask.setCourseId(course.getId());
        todayTask.setCourseName(course.getName());
        todayTask.setCurrentLesson("Lesson 1");
        todayTask.setLessonTitle("开始学习");
        todayTask.setProgress("已学0节/共10节");
        result.setTodayTask(todayTask);
        
        // 构建进度统计
        AppWbClassCourseSeriesExerciseRespVO.ProgressStats progressStats = new AppWbClassCourseSeriesExerciseRespVO.ProgressStats();
        progressStats.setTotalLessons(10);
        progressStats.setCompletedLessons(0);
        progressStats.setCompletionPercentage(0.0);
        progressStats.setConsecutiveDays(0);
        progressStats.setWeeklyStudyDays(0);
        result.setProgressStats(progressStats);
        
        return result;
    }

}
