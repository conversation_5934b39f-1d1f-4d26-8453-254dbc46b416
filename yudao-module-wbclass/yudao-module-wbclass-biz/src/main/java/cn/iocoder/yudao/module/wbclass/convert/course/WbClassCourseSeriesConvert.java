package cn.iocoder.yudao.module.wbclass.convert.course;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseRespVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseSeriesRespVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassUserCourseSeriesRespVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 课程系列 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassCourseSeriesConvert {

    WbClassCourseSeriesConvert INSTANCE = Mappers.getMapper(WbClassCourseSeriesConvert.class);

    /**
     * 将分组的课程数据转换为用户课程系列响应VO
     *
     * @param groupedCourses 按系列分组的课程数据
     * @return 用户课程系列响应VO
     */
    default AppWbClassUserCourseSeriesRespVO convertToUserCourseSeries(Map<String, List<WbClassCourseDO>> groupedCourses) {
        if (CollUtil.isEmpty(groupedCourses)) {
            return new AppWbClassUserCourseSeriesRespVO();
        }

        AppWbClassUserCourseSeriesRespVO result = new AppWbClassUserCourseSeriesRespVO();
        List<AppWbClassCourseSeriesRespVO> courseSeries = new ArrayList<>();
        List<AppWbClassCourseRespVO> individualCourses = new ArrayList<>();

        groupedCourses.forEach((seriesName, courses) -> {
            if ("individual_courses".equals(seriesName)) {
                // 处理单独的课程（不属于任何系列）
                individualCourses.addAll(WbClassCourseConvert.INSTANCE.convertAppList(courses));
            } else {
                // 处理系列课程
                AppWbClassCourseSeriesRespVO seriesVO = new AppWbClassCourseSeriesRespVO();
                seriesVO.setSeriesName(seriesName);
                
                List<AppWbClassCourseRespVO> courseVOs = WbClassCourseConvert.INSTANCE.convertAppList(courses);
                seriesVO.setAllCourses(courseVOs);
                
                // 设置默认课程（第一个，因为已经按seriesOrder排序）
                if (CollUtil.isNotEmpty(courseVOs)) {
                    seriesVO.setDefaultCourse(courseVOs.get(0));
                }
                
                courseSeries.add(seriesVO);
            }
        });

        result.setCourseSeries(courseSeries);
        result.setIndividualCourses(individualCourses);
        
        return result;
    }

}
