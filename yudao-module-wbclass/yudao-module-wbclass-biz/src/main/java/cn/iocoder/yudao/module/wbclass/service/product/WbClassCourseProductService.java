package cn.iocoder.yudao.module.wbclass.service.product;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductPageReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductUpdateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo.AppWbClassCourseProductPageReqVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 训练营课程商品 Service 接口
 *
 * <AUTHOR>
 */
public interface WbClassCourseProductService {

    /**
     * 创建课程商品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProduct(@Valid WbClassCourseProductCreateReqVO createReqVO);

    /**
     * 更新课程商品
     *
     * @param updateReqVO 更新信息
     */
    void updateProduct(@Valid WbClassCourseProductUpdateReqVO updateReqVO);

    /**
     * 删除课程商品
     *
     * @param id 编号
     */
    void deleteProduct(Long id);

    /**
     * 获得课程商品
     *
     * @param id 编号
     * @return 课程商品
     */
    WbClassCourseProductDO getProduct(Long id);

    /**
     * 获得课程商品列表
     *
     * @param ids 编号
     * @return 课程商品列表
     */
    List<WbClassCourseProductDO> getProductList(Collection<Long> ids);

    /**
     * 获得课程商品分页
     *
     * @param pageReqVO 分页查询
     * @return 课程商品分页
     */
    PageResult<WbClassCourseProductDO> getProductPage(WbClassCourseProductPageReqVO pageReqVO);

    /**
     * 增加商品浏览量
     *
     * @param id 商品ID
     */
    void incrementViewCount(Long id);

    /**
     * 增加商品销量
     *
     * @param id 商品ID
     */
    void incrementSalesCount(Long id);

    /**
     * 获得App端课程商品分页
     *
     * @param pageReqVO 分页查询
     * @return 课程商品分页
     */
    PageResult<WbClassCourseProductDO> getAppProductPage(AppWbClassCourseProductPageReqVO pageReqVO);

}