package cn.iocoder.yudao.module.wbclass.dal.mysql.product;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductPageReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo.AppWbClassCourseProductPageReqVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 训练营课程商品 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassCourseProductMapper extends BaseMapperX<WbClassCourseProductDO> {

    default PageResult<WbClassCourseProductDO> selectPage(WbClassCourseProductPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WbClassCourseProductDO>()
                .likeIfPresent(WbClassCourseProductDO::getName, reqVO.getName())
                .eqIfPresent(WbClassCourseProductDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(WbClassCourseProductDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WbClassCourseProductDO::getSort)
                .orderByDesc(WbClassCourseProductDO::getId));
    }

    default PageResult<WbClassCourseProductDO> selectAppPage(AppWbClassCourseProductPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WbClassCourseProductDO>()
                .likeIfPresent(WbClassCourseProductDO::getName, reqVO.getName())
                .eqIfPresent(WbClassCourseProductDO::getStatus, reqVO.getStatus())
                .eq(WbClassCourseProductDO::getStatus, 1) // App端只显示上架的商品
                .orderByDesc(WbClassCourseProductDO::getSort)
                .orderByDesc(WbClassCourseProductDO::getId));
    }

}