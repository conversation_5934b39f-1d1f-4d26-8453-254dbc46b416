<template>
  <view class="homework-detail-daily">
    <!-- 添加 loading 状态 -->
    <view v-if="state.loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 当不是 loading 状态时显示内容 -->
    <template v-else>
      <!-- 顶部信息区域 -->
      <view class="header">
        <view class="title-row">
          <view class="title">{{ state.homeworkInfo.title }}</view>
        </view>
        <view class="class-info">
          <image class="icon" src="/static/icons/class.png" />
          <text>班级：{{ state.homeworkInfo.className }}</text>
        </view>
        <view class="study-info">
          <image class="icon" src="/static/icons/study.png" />
          <text>学习：{{ state.homeworkInfo.studyContent }}</text>
        </view>

        <!-- 添加媒体预览组件 -->
        <media-preview
          v-if="state.homeworkInfo.contentImages?.length"
          :mediaList="state.homeworkInfo.contentImages"
          :itemWidth="160"
          :itemHeight="160"
          :gap="16"
          :customPreview="hasVoiceFeedback"
          @custom-preview="handleCustomPreview"
        />

        <!-- 使用文件列表组件替换原来的summary-list -->
        <file-list :files="state.homeworkInfo.fileUrls || []" />
      </view>

      <!-- 作业内容区域 -->
      <view class="content">
        <view class="tab-header">
          <view class="tab-item-wrapper">
            <text
              :class="['tab-item', { active: state.activeTab === 'list' }]"
              @click="switchTab('list')"
              >作业清单</text
            >
            <image
              v-if="state.activeTab === 'list'"
              class="tab-line"
              src="/static/icons/detail-line.png"
            />
          </view>
          <view class="tab-item-wrapper">
            <text
              :class="['tab-item', { active: state.activeTab === 'submit' }]"
              @click="switchTab('submit')"
              >我的提交</text
            >
            <image
              v-if="state.activeTab === 'submit'"
              class="tab-line"
              src="/static/icons/detail-line.png"
            />
          </view>
        </view>

        <!-- 添加筛选按钮标签 -->
        <view class="filter-tag-container" v-if="state.activeTab === 'list'">
          <view class="filter-tag" @click="showFilterPopup">
            <text>{{ getFilterText }}</text>
            <text class="iconfont icon-arrow-down"></text>
          </view>
        </view>

        <!-- 作业清单内容 -->
        <view class="homework-list" v-if="state.activeTab === 'list'">
          <view
            class="day-section"
            v-for="(day, dayIndex) in filteredHomeworkDays"
            :key="dayIndex"
          >
            <view class="day-header">
              <text class="day-title">Day{{ day.dayNumber }}</text>
              <image class="day-icon" src="/static/icons/icon-bitong.svg" />
            </view>
            <view class="task-list">
              <view
                class="task-item"
                v-for="(task, taskIndex) in day.tasks"
                :key="taskIndex"
              >
                <view class="task-left">
                  <view :class="['task-type', task.type]">{{
                    task.typeText
                  }}</view>
                  <text class="task-desc">{{ task.content }}</text>
                </view>
                <view
                  :class="['task-btn', { 'btn-submitted': task.submitted }]"
                  @click="handleSubmitHomework(task)"
                >
                  {{ getButtonText(task) }}
                </view>
              </view>
            </view>
          </view>

          <!-- 添加空状态显示 -->
          <view class="empty-homework" v-if="filteredHomeworkDays.length === 0">
            <image class="empty-icon" src="/static/icons/empty-box.svg" />
            <text class="empty-text"
              >暂无{{
                state.filterType === "online" ? "在线练习" : "书面作业"
              }}</text
            >
          </view>
        </view>

        <!-- 我的提交内容 -->
        <view class="submission-list" v-if="state.activeTab === 'submit'">
          <!-- 有提交内容时显示 -->
          <template v-if="filteredDailySubmissions.length > 0">
            <!-- 每日提交内容 -->
            <view
              class="day-section"
              v-for="(submission, index) in filteredDailySubmissions"
              :key="submission.id"
            >
              <view class="day-header">
                <text class="day-title">Day{{ submission.dayNumber }}</text>
                <image class="day-icon" src="/static/icons/icon-bitong.svg" />
              </view>

              <!-- 提交内容 -->
              <view class="submission-content">
                <!-- 文本内容 -->
                <view class="text-content" v-if="submission.contentText">
                  {{ submission.contentText }}
                </view>

                <!-- 图片内容 -->
                <media-preview
                  v-if="submission.contentImages?.length"
                  :mediaList="
                    getGradedImages(
                      submission.contentImages,
                      submission.gradingImages
                    )
                  "
                  :itemWidth="160"
                  :itemHeight="160"
                  :gap="16"
                  :customPreview="hasVoiceFeedback"
                  @custom-preview="handleCustomPreview"
                />

                <!-- 音频内容 -->
                <view
                  class="audio-list"
                  v-if="submission.recordFileUrls?.length"
                >
                  <view
                    class="audio-item"
                    v-for="(audio, audioIndex) in submission.recordFileUrls"
                    :key="audioIndex"
                  >
                    <audio-player
                      :src="audio.url"
                      :title="audio.name"
                      :duration="audio.duration || 0"
                      :show-delete-btn="false"
                    />
                  </view>
                </view>

                <!-- 视频内容 -->
                <media-preview
                  v-if="submission.fileUrls?.length"
                  :mediaList="submission.fileUrls"
                  :itemWidth="160"
                  :itemHeight="160"
                  :gap="16"
                  :customPreview="hasVoiceFeedback"
                  @custom-preview="handleCustomPreview"
                />

                <!-- 添加老师评语部分 -->
                <view
                  class="submission-feedback"
                  v-if="submission.feedbacks && submission.feedbacks.length > 0"
                >
                  <view class="feedback-title">
                    <image class="icon" src="/static/icons/icon-teacher.svg" />
                    <text>老师评语</text>
                  </view>
                  <view class="feedback-list">
                    <view
                      class="feedback-item"
                      v-for="feedback in submission.feedbacks"
                      :key="feedback.id"
                    >
                      <view class="feedback-header">
                        <view class="teacher-info">
                          <text class="name">{{
                            feedback.extUserInfo?.nickname
                          }}</text>
                          <text class="time">{{
                            formatTime(feedback.createTime)
                          }}</text>
                        </view>
                      </view>
                      <view class="feedback-content">
                        <text v-if="feedback.type === 1">{{
                          formatFeedbackContent(feedback.content)
                        }}</text>
                        <!-- 语音评语 -->
                        <audio-player
                          v-if="feedback.type === 2"
                          :src="parseVoiceFeedback(feedback.content).url"
                          :duration="
                            parseVoiceFeedback(feedback.content).duration
                          "
                          :show-delete-btn="false"
                        />
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </template>

          <!-- 没有提交内容时显示 -->
          <view v-else class="empty-submission">
            <image class="empty-icon" src="/static/icons/empty-box.svg" />
            <text class="empty-text">暂无提交内容</text>
          </view>

          <!-- 老师评语模块 - 移到提交列表最底部，只显示不匹配任何day标签的评论 -->
          <view
            class="teacher-feedback general-feedback"
            v-if="filteredGeneralFeedbacks.length"
          >
            <view class="feedback-title">
              <image class="icon" src="/static/icons/icon-teacher.svg" />
              <text>老师总评</text>
            </view>
            <view class="feedback-list">
              <view
                class="feedback-item"
                v-for="feedback in filteredGeneralFeedbacks"
                :key="feedback.id"
              >
                <view class="feedback-header">
                  <view class="teacher-info">
                    <text class="name">{{
                      feedback.extUserInfo?.nickname
                    }}</text>
                    <text class="time">{{
                      formatTime(feedback.createTime)
                    }}</text>
                  </view>
                </view>
                <view class="feedback-content">
                  <text v-if="feedback.type === 1">{{
                    formatFeedbackContent(feedback.content)
                  }}</text>
                  <!-- 语音评语 -->
                  <audio-player
                    v-if="feedback.type === 2"
                    :src="parseVoiceFeedback(feedback.content).url"
                    :duration="parseVoiceFeedback(feedback.content).duration"
                    :show-delete-btn="false"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 添加作业提交组件 -->
      <homework-submit
        ref="homeworkSubmitRef"
        :exerciseId="currentExerciseId"
        :homeworkId="state.homeworkInfo.homeworkId"
        :mediaTypes="currentMediaTypes"
        @success="handleSubmitSuccess"
        @close="handleSubmitClose"
      />

      <!-- 添加祝贺弹窗 -->
      <uni-popup
        ref="congratsPopup"
        type="center"
        :animation="true"
        :maskClick="false"
      >
        <view class="congrats-popup animate__animated">
          <text
            class="iconfont icon-congratulations congrats-icon animate__animated animate__bounceIn"
          ></text>
          <view class="congrats-title animate__animated animate__fadeInUp"
            >恭喜完成练习！</view
          >
          <view
            class="congrats-content animate__animated animate__fadeInUp animate__delay_300ms"
            v-if="congratsInfo.exerciseType !== 'no-need-commit'"
          >
            <text>练习单词：{{ congratsInfo.totalWords }}个</text>
            <text v-if="congratsInfo.exerciseType === 'word-recognition-review'"
              >满星单词：{{ congratsInfo.correctWords }}个</text
            >
            <text v-else-if="congratsInfo.exerciseType === 'word-play'"
              >正确单词：{{ congratsInfo.correctWords }}个</text
            >
            <text v-else>正确单词：{{ congratsInfo.correctWords }}个</text>
            <text v-if="congratsInfo.exerciseType === 'word-play'"
              >错误单词：{{ congratsInfo.incorrectWords }}个</text
            >
          </view>
          <button
            class="congrats-btn animate__animated animate__fadeInUp animate__delay_500ms"
            @tap="handleCongratsClose"
          >
            我知道了
          </button>
        </view>
      </uni-popup>

      <!-- 添加筛选弹窗 -->
      <uni-popup
        ref="filterPopup"
        type="bottom"
        :animation="true"
        :maskClick="true"
      >
        <view class="filter-popup">
          <view class="filter-header">
            <text class="filter-title">筛选作业类型</text>
            <text class="close-btn" @click="closeFilterPopup">×</text>
          </view>
          <view class="filter-options">
            <view
              class="filter-option option-all"
              :class="{ active: state.filterType === 'all' }"
              @click="setFilterType('all')"
            >
              <text>全部作业</text>
            </view>
            <view
              class="filter-option option-online"
              :class="{ active: state.filterType === 'online' }"
              @click="setFilterType('online')"
            >
              <text>在线练习</text>
            </view>
            <view
              class="filter-option option-paper"
              :class="{ active: state.filterType === 'paper' }"
              @click="setFilterType('paper')"
            >
              <text>书面作业</text>
            </view>
          </view>
        </view>
      </uni-popup>

      <!-- 添加自定义图片预览组件 -->
      <image-preview
        v-if="imagePreviewVisible && imagePreviewUrls.length > 0"
        :visible="imagePreviewVisible"
        :urls="imagePreviewUrls"
        :current="imagePreviewCurrent"
        @close="handleImagePreviewClose"
      />
    </template>
  </view>
</template>

<script lang="ts" setup>
import { getHomeworkDetail } from "@/api/homework";
import AudioPlayer from "@/components/audio-player/index.vue";
import FileList from "@/components/file-list/index.vue";
import HomeworkSubmit from "@/components/homework-submit/index.vue";
import ImagePreview from "@/components/media-preview/image-preview.vue";
import MediaPreview from "@/components/media-preview/index.vue";
import { UploadFileInfo } from "@/types/file";
import type Homework from "@/types/homework";
import { HomeworkExercise } from "@/types/homework-exercise";
import { formatTime } from "@/utils/date";

import { createOrUpdateHomeworkSubmissionDetail } from "@/api/homework-submission";
// @ts-ignore
import { onLoad, onShareAppMessage, onShareTimeline } from "@dcloudio/uni-app";
import { computed, nextTick, onMounted, onUnmounted, reactive, ref } from "vue";

interface State {
  homeworkInfo: {
    title: string;
    className: string;
    studyContent: string;
    status: "completed" | "uncompleted" | "overdue";
    homeworkId: number;
    contentImages?: UploadFileInfo[];
    fileUrls?: UploadFileInfo[];
    extMySubmitAssignees?: Homework.HomeworkDetail["extMySubmitAssignees"];
    extExercises?: HomeworkExercise[];
  };
  activeTab: "list" | "submit";
  homeworkDays: Array<{
    dayNumber: number;
    tasks: Array<{
      type: string;
      typeText: string;
      content: string;
      submitted: boolean;
      exerciseId: number;
      submitType: string;
      exerciseCategory: number;
      exercise: HomeworkExercise;
    }>;
  }>;
  loading: boolean;
  filterType: "all" | "online" | "paper";
}

const state = reactive<State>({
  homeworkInfo: {
    title: "",
    className: "",
    studyContent: "",
    status: "uncompleted",
    homeworkId: 0,
  },
  activeTab: "list",
  homeworkDays: [],
  loading: true,
  filterType: "all",
});

const homeworkSubmitRef = ref();
const currentExerciseId = ref<number>(0);
const currentMediaTypes = ref<Array<"image" | "video" | "audio">>([]);

const switchTab = (tab: "list" | "submit") => {
  state.activeTab = tab;
};

const getMediaTypesBySubmitType = (
  submitType: string
): Array<"image" | "video" | "audio"> => {
  switch (submitType) {
    case "3":
      return ["audio", "video"];
    case "2":
      return ["image"];
    case "1":
    default:
      return ["image", "video", "audio"];
  }
};

// 获取作业详情
const fetchHomeworkDetail = async () => {
  try {
    const res = await getHomeworkDetail(state.homeworkInfo.homeworkId);
    const data = res.getData();

    // 处理作业信息
    state.homeworkInfo = {
      title: data.title,
      className: data.extClassInfo?.name || "",
      studyContent: data.lessonUnitName || "",
      status: "uncompleted",
      homeworkId: data.id,
      contentImages: data.contentImages,
      fileUrls: data.fileUrls,
      extMySubmitAssignees: data.extMySubmitAssignees,
      extExercises: data.extExercises,
    };

    // 处理作业任务列表
    const days = new Map();
    data.extExercises?.forEach((exercise: HomeworkExercise) => {
      if (!days.has(exercise.dayNumber)) {
        days.set(exercise.dayNumber, {
          dayNumber: exercise.dayNumber,
          tasks: [],
        });
      }

      days.get(exercise.dayNumber).tasks.push({
        type: exercise.exerciseType,
        typeText: getExerciseTypeText(exercise.exerciseType),
        content: exercise.content,
        submitted: determineSubmissionStatus(exercise),
        exerciseId: exercise.id,
        submitType: exercise.submitType,
        exerciseCategory: exercise.exerciseCategory,
        exercise: exercise,
      });
    });

    state.homeworkDays = Array.from(days.values());

    // 对日期进行排序，确保按照dayNumber的数值大小排序
    state.homeworkDays.sort((a, b) => a.dayNumber - b.dayNumber);
  } catch (error) {
    console.error("获取作业详情失败:", error);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
    });
  } finally {
    state.loading = false;
  }
};

const getExerciseTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    vocabulary: "词汇",
    speaking: "口语",
    listening: "听力",
    reading: "阅读",
    writing: "写作",
    comprehensive: "综合",
    notes: "笔记",
    preview: "预习",
    review: "复习",
  };
  return typeMap[type] || type;
};

// 添加祝贺信息的响应式数据
const congratsInfo = ref({
  score: 0,
  totalWords: 0,
  correctWords: 0,
  incorrectWords: 0,
  exerciseType: "",
});

const congratsPopup = ref();

// 添加计算属性来过滤有内容的提交
const filteredDailySubmissions = computed(() => {
  // 先获取所有带内容的提交
  const submissions =
    state.homeworkInfo.extMySubmitAssignees?.extDailySubmission
      ?.filter((submission) => {
        return (
          submission.contentText ||
          (submission.contentImages && submission.contentImages.length > 0) ||
          (submission.recordFileUrls && submission.recordFileUrls.length > 0) ||
          (submission.fileUrls && submission.fileUrls.length > 0)
        );
      })
      .sort((a, b) => a.dayNumber - b.dayNumber) || [];

  // 遍历所有提交，为每个提交添加对应的评论
  return submissions.map((submission) => {
    // 为每个submission添加匹配的评论
    const feedbackTag = `day${submission.dayNumber}`;
    const matchedFeedbacks =
      state.homeworkInfo.extMySubmitAssignees?.extFeedbackList?.filter(
        (feedback) => feedback.tag === feedbackTag
      ) || [];

    return {
      ...submission,
      feedbacks: matchedFeedbacks,
    };
  });
});

// 处理祝贺弹窗关闭
const handleCongratsClose = () => {
  congratsPopup.value?.close();
};

// 处理练习完成事件
const handleExerciseCompleted = (data: any) => {
  congratsInfo.value = data;

  // 延迟300ms打开弹窗
  setTimeout(() => {
    congratsPopup.value?.open();
  }, 500);
};

// 添加事件监听
onMounted(() => {
  uni.$on("wordExerciseComplete", handleWordExerciseComplete);
  uni.$on("exerciseCompleted", handleExerciseCompleted);
  uni.$on("resultPageComplete", handleResultPageComplete);
});

// 移除事件监听
onUnmounted(() => {
  uni.$off("wordExerciseComplete", handleWordExerciseComplete);
  uni.$off("exerciseCompleted", handleExerciseCompleted);
  uni.$off("resultPageComplete", handleResultPageComplete);
});

// 处理单词练习完成事件
const handleWordExerciseComplete = () => {
  fetchHomeworkDetail();
};

// 处理结果页完成事件
const handleResultPageComplete = () => {
  // 从结果页返回，不需要做任何处理
  console.log("从结果页返回");
};

// 处理作业提交
const handleSubmitHomework = (task: any) => {
  currentExerciseId.value = task.exerciseId;
  currentMediaTypes.value = getMediaTypesBySubmitType(task.submitType);
  console.log("exerciseCategory", task.exerciseCategory);

  // 增加对 NO_NEED_COMMIT 类型的处理
  if (task.submitType === "5") {
    if (task.submitted) {
      // 已完成状态，显示提示
      uni.showToast({
        title: `作业已完成`,
        icon: "none",
        duration: 2000,
      });
      return;
    }

    // 未完成状态，调用接口完成作业
    handleNoNeedCommitTask(task);
    return;
  }

  if (task.exerciseCategory === 1) {
    if (task.exercise?.wordExerciseModel === "DICTATION") {
      // 判断是否已完成，如果已完成则跳转到结果页，否则跳转到练习页
      if (task.submitted) {
        uni.navigateTo({
          url: `/sub-exercise/word-dictation/word-dictation-result?id=${task.exerciseId}`,
        });
      } else {
        uni.navigateTo({
          url: `/sub-exercise/word-dictation/word-dictation?id=${task.exerciseId}&homeworkId=${state.homeworkInfo.homeworkId}`,
        });
      }
    } else if (
      task.exercise?.wordExerciseModel === "RECOGNIZE_AND_FOLLOW" ||
      task.exercise?.wordExerciseModel === "RECOGNIZE"
    ) {
      // 判断是否已完成，如果已完成则跳转到结果页，否则跳转到练习页
      if (task.submitted) {
        uni.navigateTo({
          url: `/sub-exercise/word-exercise/word-recognition-review-result?id=${task.exerciseId}&homeworkId=${state.homeworkInfo.homeworkId}`,
        });
      } else {
        uni.navigateTo({
          url: `/sub-exercise/word-exercise/word-recognition-review?id=${task.exerciseId}&homeworkId=${state.homeworkInfo.homeworkId}`,
        });
      }
    } else if (task.exercise?.wordExerciseModel === "COMPREHENSIVE") {
      // 判断是否已完成，如果已完成则跳转到结果页，否则跳转到练习页
      if (task.submitted) {
        uni.navigateTo({
          url: `/sub-exercise/word-exercise/word-play-result?id=${task.exerciseId}&homeworkId=${state.homeworkInfo.homeworkId}`,
        });
      } else {
        uni.navigateTo({
          url: `/sub-exercise/word-exercise/word-play?id=${task.exerciseId}&homeworkId=${state.homeworkInfo.homeworkId}`,
        });
      }
    }
  } else if (task.exerciseCategory === 2) {
    uni.navigateTo({
      url: `/sub-exercise/article-follow/article-follow?id=${task.exerciseId}&homeworkId=${state.homeworkInfo.homeworkId}`,
    });
  } else {
    // 显示提交弹窗
    nextTick(() => {
      homeworkSubmitRef.value?.open();
    });
  }
};

const handleSubmitClose = () => {
  currentExerciseId.value = 0;
};

// 处理提交成功
const handleSubmitSuccess = () => {
  // 本地更新状态
  state.homeworkDays.forEach((day) => {
    day.tasks.forEach((task) => {
      if (task.exerciseId === currentExerciseId.value) {
        task.submitted = true;
      }
    });
  });

  // 显示提交成功的 toast
  uni.showToast({
    title: "提交成功",
    icon: "success",
    duration: 300,
  });

  // 延迟 300ms 后刷新作业详情
  setTimeout(() => {
    fetchHomeworkDetail();
  }, 300);
};

// Add helper function to process graded images
const getGradedImages = (
  contentImages: UploadFileInfo[],
  gradingImages?: Array<Homework.GradingImage>
) => {
  if (!gradingImages?.length) return contentImages;

  return contentImages.map((image) => {
    const gradedImage = gradingImages.find((g) => g.originalUrl === image.url);
    if (gradedImage?.gradedUrl) {
      return {
        ...image,
        url: gradedImage.gradedUrl,
      };
    }
    return image;
  });
};

// 解析语音评语内容
const parseVoiceFeedback = (content: string) => {
  try {
    const voiceData = JSON.parse(content);
    return {
      url: voiceData.url || "",
      duration: Number(voiceData.duration) || 0,
    };
  } catch (error) {
    console.error("解析语音评语失败:", error);
    return {
      url: "",
      duration: 0,
    };
  }
};

const getExerciseButtonText = (task: any) => {
  if (task.exercise?.wordExerciseModel === "DICTATION") {
    return "去听写";
  }
  return "开始练习";
};

// 添加处理无需提交的作业完成函数
const handleNoNeedCommitTask = async (task: any) => {
  try {
    uni.showLoading({ title: "提交中..." });

    // 格式化当前时间
    const currentTime = Date.now();
    const formattedTime = formatTime(currentTime);

    // 创建提交请求数据
    const submitData = {
      exerciseId: task.exerciseId,
      homeworkId: state.homeworkInfo.homeworkId,
      contentText: "",
    };

    // 调用接口
    const res = await createOrUpdateHomeworkSubmissionDetail(submitData);

    if (res.code === 0) {
      // 本地更新状态
      state.homeworkDays.forEach((day) => {
        day.tasks.forEach((t) => {
          if (t.exerciseId === task.exerciseId) {
            t.submitted = true;
          }
        });
      });

      // 隐藏loading
      uni.hideLoading();

      // 显示恭喜弹窗
      showCongratulationsPopup(task);
    } else {
      uni.showToast({
        title: res.msg || "提交失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("完成作业失败:", error);
    uni.showToast({
      title: "网络异常，请稍后再试",
      icon: "none",
    });
  } finally {
    uni.hideLoading();
  }
};

// 显示恭喜完成作业弹窗
const showCongratulationsPopup = (task: any) => {
  // 设置恭喜信息
  congratsInfo.value = {
    score: 100,
    totalWords: 0,
    correctWords: 0,
    incorrectWords: 0,
    exerciseType: "no-need-commit",
  };

  // 延迟300ms打开弹窗
  setTimeout(() => {
    congratsPopup.value?.open();
  }, 500);
};

onLoad((options: any) => {
  console.log("页面参数:", options);
  if (options.id) {
    state.homeworkInfo.homeworkId = Number(options.id);
    state.loading = true;
    fetchHomeworkDetail();
  } else {
    uni.showToast({
      title: "作业ID不能为空",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 分享给好友
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  return {
    title: state.homeworkInfo.title || "白熊作业",
    path: `/homework-package/homework-detail-daily/homework-detail-daily?id=${state.homeworkInfo.homeworkId}&from=share`,
    imageUrl:
      state.homeworkInfo.contentImages &&
      state.homeworkInfo.contentImages.length > 0
        ? state.homeworkInfo.contentImages[0].url
        : "/static/icon.png", // 默认分享图，需要添加一张默认分享图
    success: function (res: any) {
      // 转发成功的回调函数
      uni.showToast({
        title: "分享成功",
        icon: "success",
      });
    },
    fail: function (res: any) {
      // 转发失败的回调函数
      uni.showToast({
        title: "分享失败",
        icon: "none",
      });
    },
  };
});

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: state.homeworkInfo.title || "白熊作业",
    query: `id=${state.homeworkInfo.homeworkId}&from=timeline`,
    imageUrl:
      state.homeworkInfo.contentImages &&
      state.homeworkInfo.contentImages.length > 0
        ? state.homeworkInfo.contentImages[0].url
        : "/static/icon.png", // 默认分享图
  };
});
// #endif

// 添加按钮文案计算方法
const getButtonText = (task: any) => {
  if (task.submitType === "5") {
    return task.submitted ? "已完成" : "完成作业";
  }

  if (task.exerciseCategory === 1) {
    if (task.submitted) {
      return "已完成";
    }
    return getExerciseButtonText(task);
  }

  if (task.exerciseCategory === 2) {
    if (task.submitted) {
      return "已完成";
    }
    return "开始跟读";
  }

  return task.submitted ? "去修改" : "去提交";
};

// 添加一个辅助函数来确定提交状态
const determineSubmissionStatus = (exercise: any) => {
  // 提交类型为5的情况
  if (exercise.submitType === "5") {
    return exercise.extSubmissionDetail !== null;
  }

  // 练习类别为（单词练习1，文章跟读2）的情况
  if (exercise.exerciseCategory === 1) {
    return !!exercise.extSubmissionDetail?.wordEvaluationResult?.isFinished;
  }

  if (exercise.exerciseCategory === 2) {
    return !!exercise.extSubmissionDetail?.articleEvaluationResult?.isFinished;
  }

  // 其他情况
  return exercise.extSubmissionDetail !== null;
};

const filterPopup = ref();

// 显示筛选弹窗
const showFilterPopup = () => {
  filterPopup.value?.open();
};

// 关闭筛选弹窗
const closeFilterPopup = () => {
  filterPopup.value?.close();
};

// 设置筛选类型
const setFilterType = (type: "all" | "online" | "paper") => {
  state.filterType = type;
  closeFilterPopup();
};

// 获取筛选后的作业日列表
const filteredHomeworkDays = computed(() => {
  if (state.filterType === "all") {
    return state.homeworkDays;
  }

  return state.homeworkDays
    .map((day) => {
      // 复制日期对象，但过滤任务
      return {
        ...day,
        tasks: day.tasks.filter((task) => {
          if (state.filterType === "online") {
            // 在线练习是单词练习，exerciseCategory === 1
            return task.exerciseCategory === 1;
          } else {
            // 书面作业是非单词练习
            return task.exerciseCategory !== 1;
          }
        }),
      };
    })
    .filter((day) => day.tasks.length > 0); // 只保留有任务的日期
});

// 获取当前筛选文本
const getFilterText = computed(() => {
  switch (state.filterType) {
    case "online":
      return "在线练习";
    case "paper":
      return "书面作业";
    default:
      return "全部作业";
  }
});

// 添加计算属性来过滤不匹配任何day标签的评论
const filteredGeneralFeedbacks = computed(() => {
  return (
    state.homeworkInfo.extMySubmitAssignees?.extFeedbackList?.filter(
      (feedback) => {
        // 如果没有tag，或者tag不是以day开头的，就显示在通用评语区域
        return !feedback.tag || !feedback.tag.startsWith("day");
      }
    ) || []
  );
});

// 解析评论内容，移除【dayX】前缀
const formatFeedbackContent = (content: string) => {
  // 匹配【dayX】格式
  const regex = /^\s*【day\d+】\s*/;
  return content.replace(regex, "");
};

// 添加自定义图片预览相关的状态
const imagePreviewVisible = ref(false);
const imagePreviewUrls = ref<string[]>([]);
const imagePreviewCurrent = ref(0);
const hasVoiceFeedback = computed(() => {
  // 检查所有评论中是否有语音评论 (type === 2)
  const hasVoice =
    state.homeworkInfo.extMySubmitAssignees?.extFeedbackList?.some(
      (feedback) => feedback.type === 2
    ) || false;

  console.log("是否有语音评论:", hasVoice);
  return hasVoice;
});


// 处理自定义图片预览事件
const handleCustomPreview = (data: { urls: string[]; current: number }) => {
  console.log("接收到自定义预览事件", data);
  imagePreviewUrls.value = data.urls;
  imagePreviewCurrent.value = data.current;

  // 使用setTimeout确保视图更新
  setTimeout(() => {
    imagePreviewVisible.value = true; // 显示自定义预览组件
    console.log(
      "自定义预览",
      imagePreviewVisible.value,
      imagePreviewUrls.value.length
    );
  }, 50);
};

// 关闭自定义图片预览
const handleImagePreviewClose = () => {
  console.log("关闭自定义预览");
  imagePreviewVisible.value = false;
};
</script>

<style lang="scss" scoped>
.homework-detail-daily {
  min-height: 100vh;
  background-color: #f5f5f5;

  // 添加 loading 样式
  .loading-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;

    .loading-spinner {
      width: 80rpx;
      height: 80rpx;
      border: 12rpx solid #e7e7e7;
      border-top: 12rpx solid #ffd600;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20rpx;
    }

    .loading-text {
      font-size: 32rpx;
      color: #666;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .header {
    background-color: #fff;
    padding: 20px 16px;

    .title-row {
      display: flex;
      align-items: center;
      margin-bottom: 18rpx;

      .title {
        font-size: 34rpx;
        font-family: STYuanti-SC-Regular;
        color: #000;
        line-height: 48rpx;
      }
    }

    .class-info,
    .study-info {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .icon {
        width: 30rpx;
        height: 30rpx;
        margin-right: 8rpx;
      }

      text {
        font-size: 30rpx;
        color: #666;
        line-height: 42rpx;
      }
    }

    .summary-list {
      margin-top: 28rpx;

      .summary-item {
        display: flex;
        align-items: center;
        background: #f8f8f8;
        border-radius: 8rpx;
        padding: 26rpx 32rpx;
        margin-bottom: 16rpx;

        .icon {
          width: 40rpx;
          height: 40rpx;
          margin-right: 12rpx;
        }

        .text {
          flex: 1;
          font-size: 28rpx;
          color: #000;
          line-height: 40rpx;
        }

        .arrow {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }

  .content {
    margin-top: 12rpx;
    background-color: #fff;
    padding: 20px 16px;

    .tab-header {
      display: flex;
      justify-content: center;
      margin-bottom: 20rpx;
      position: relative;

      .tab-item-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0 40rpx;

        .tab-item {
          font-size: 34rpx;
          color: #333;
          line-height: 48rpx;

          &.active {
            color: #000;
            font-weight: 500;
          }
        }

        .tab-line {
          width: 40rpx;
          height: 12rpx;
          margin-top: 4rpx;
        }
      }
    }

    /* 添加筛选标签样式 */
    .filter-tag-container {
      display: flex;
      justify-content: flex-end;
      padding: 10rpx;

      .filter-tag {
        display: inline-flex;
        align-items: center;
        border-radius: 8rpx;

        text {
          font-size: 24rpx;
          color: #666;
          margin-right: 4rpx;
        }

        .iconfont {
          font-size: 24rpx;
          color: #666;
        }
      }
    }

    .homework-list {
      .day-section {
        margin-top: 42rpx;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 24rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

        .day-header {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            left: 0;
            bottom: -10rpx;
            width: 60rpx;
            height: 4rpx;
            background: linear-gradient(to right, #ffd600, transparent);
            border-radius: 2rpx;
          }

          .day-title {
            font-size: 34rpx;
            color: #000;
            line-height: 48rpx;
            font-weight: 500;
          }

          .day-icon {
            width: 42rpx;
            height: 42rpx;
            margin-left: 8rpx;
          }
        }

        .task-list {
          background: #f8f8f8;
          border-radius: 8rpx;
          padding: 20rpx;

          .task-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20rpx 0;

            .task-left {
              display: flex;
              align-items: flex-start;
              flex: 1;

              .task-type {
                padding: 4rpx 16rpx;
                border-radius: 4rpx;
                font-size: 22rpx;
                margin-right: 12rpx;
                white-space: nowrap;

                &.vocabulary {
                  color: #9d7be1;
                  background: rgba(157, 123, 225, 0.1);
                }

                &.comprehensive {
                  color: #ff7676;
                  background: rgba(255, 118, 118, 0.1);
                }

                &.notes {
                  color: #5c8eff;
                  background: rgba(92, 142, 255, 0.1);
                }

                &.preview {
                  color: #ff9736;
                  background: rgba(255, 151, 54, 0.1);
                }

                &.review {
                  color: #36d1ab;
                  background: rgba(54, 209, 171, 0.1);
                }

                &.listening {
                  color: #f5bc46;
                  background: rgba(245, 188, 70, 0.1);
                }

                &.speaking {
                  color: #f7966a;
                  background: rgba(247, 150, 106, 0.1);
                }

                &.reading {
                  color: #afcf54;
                  background: rgba(175, 207, 84, 0.1);
                }

                &.writing {
                  color: #85ceff;
                  background: rgba(133, 206, 255, 0.1);
                }
              }

              .task-desc {
                font-size: 26rpx;
                color: #666;
                line-height: 38rpx;
                flex: 1;
              }
            }

            .task-btn {
              padding: 8rpx 28rpx;
              border-radius: 24rpx;
              font-size: 26rpx;
              color: #000;
              background: #ffd600;
              margin-left: 16rpx;

              &.btn-submitted {
                background: #f0f0f0;
              }
            }
          }

          .divider {
            height: 2rpx;
            background: #e8e8e8;
            margin: 0 92rpx;
          }
        }
      }
    }

    .submission-list {
      .day-section {
        margin-top: 42rpx;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 24rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

        .day-header {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;

          .day-title {
            font-size: 34rpx;
            color: #000;
            line-height: 48rpx;
            font-weight: 500;
          }

          .day-icon {
            width: 42rpx;
            height: 42rpx;
            margin-left: 8rpx;
          }
        }

        .submission-content {
          background: #f8f8f8;
          border-radius: 12rpx;
          padding: 24rpx;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            left: 30rpx;
            top: -10rpx;
            width: 20rpx;
            height: 20rpx;
            background-color: #f8f8f8;
            transform: rotate(45deg);
          }

          .text-content {
            font-size: 28rpx;
            color: #333;
            line-height: 40rpx;
            margin-bottom: 20rpx;
            white-space: pre-wrap;
            word-break: break-all;
          }

          .audio-list {
            margin: 20rpx 0;

            .audio-item {
              margin-bottom: 16rpx;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }

          /* 添加老师评语样式 */
          .submission-feedback {
            margin-top: 30rpx;
            border-top: 1px dashed #e8e8e8;
            padding-top: 24rpx;

            .feedback-title {
              display: flex;
              align-items: center;
              margin-bottom: 16rpx;

              .icon {
                width: 32rpx;
                height: 32rpx;
                margin-right: 8rpx;
              }

              text {
                font-size: 28rpx;
                color: #333;
                font-weight: 500;
              }
            }

            .feedback-list {
              .feedback-item {
                background: #ffffff;
                border-radius: 12rpx;
                padding: 16rpx 20rpx;
                margin-bottom: 16rpx;
                box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
                position: relative;

                &:last-child {
                  margin-bottom: 0;
                }

                .feedback-header {
                  display: flex;
                  align-items: center;
                  margin-bottom: 10rpx;

                  .teacher-info {
                    display: flex;
                    align-items: center;
                    width: 100%;

                    .name {
                      font-size: 28rpx;
                      color: #333;
                      font-weight: 500;
                    }

                    .time {
                      font-size: 24rpx;
                      color: #999;
                      margin-left: auto; /* 将时间放在右侧 */
                    }
                  }
                }

                .feedback-content {
                  padding: 0 2rpx;

                  text {
                    font-size: 28rpx;
                    color: #333;
                    line-height: 42rpx;
                    word-break: normal;
                    word-wrap: break-word;
                    white-space: pre-wrap;
                  }

                  /* 添加音频播放器样式 */
                  :deep(.audio-player) {
                    margin: 4rpx 0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .teacher-feedback {
    margin-top: 24rpx;
    background-color: #fff;
    padding: 20rpx;
    border-radius: 8rpx;

    .feedback-title {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .icon {
        width: 42rpx;
        height: 42rpx;
        margin-right: 8rpx;
      }

      text {
        font-size: 34rpx;
        color: #000;
        line-height: 48rpx;
      }
    }

    .feedback-list {
      .feedback-item {
        background: #f8f8f8;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .feedback-header {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;
          width: 100%;

          .teacher-info {
            display: flex;
            align-items: center;
            width: 100%;

            .name {
              font-size: 28rpx;
              color: #333;
              font-weight: 500;
            }

            .time {
              font-size: 24rpx;
              color: #999;
              margin-left: auto; /* 将时间放在右侧 */
            }
          }
        }

        .feedback-content {
          text {
            font-size: 28rpx;
            color: #333;
            line-height: 40rpx;
            word-break: normal;
            word-wrap: break-word;
          }
        }
      }
    }
  }

  .congrats-popup {
    background: #ffffff;
    border-radius: 24rpx;
    padding: 40rpx;
    width: 560rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    transform: scale(0.5);
    opacity: 0;
    animation: popIn 0.3s ease-out forwards;

    @keyframes popIn {
      from {
        transform: scale(0.5);
        opacity: 0;
      }
      to {
        transform: scale(1);
        opacity: 1;
      }
    }

    .congrats-icon {
      font-size: 160rpx;
      color: #ffb800;
      margin-bottom: 24rpx;
    }

    .congrats-title {
      font-size: 36rpx;
      color: #333333;
      font-weight: bold;
      margin-bottom: 24rpx;
      opacity: 0;
      animation: fadeInUp 0.3s ease-out 0.2s forwards;
    }

    .congrats-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 32rpx;
      opacity: 0;
      animation: fadeInUp 0.3s ease-out 0.4s forwards;

      text {
        font-size: 28rpx;
        color: #666666;
      }
    }

    .congrats-btn {
      width: 320rpx;
      height: 80rpx;
      background: #ffe251;
      border-radius: 40rpx;
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      animation: fadeInUp 0.3s ease-out 0.6s forwards;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.95);
        background: #ffd600;
      }
    }

    @keyframes fadeInUp {
      from {
        transform: translateY(20rpx);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }
  }

  // 添加空状态样式
  .empty-submission,
  .empty-homework {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    background: #ffffff;
    border-radius: 12rpx;
    margin-top: 42rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 20rpx;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 32rpx;
      color: #666;
      margin-bottom: 12rpx;
    }
  }

  // 添加筛选弹窗样式
  .filter-popup {
    background: #fff;
    border-radius: 16rpx 16rpx 0 0;
    padding: 30rpx 30rpx 40rpx;

    .filter-header {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 30rpx;
      position: relative;

      .filter-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        text-align: center;
      }

      .close-btn {
        font-size: 32rpx;
        color: #999;
        line-height: 1;
        position: absolute;
        right: 10rpx;
        top: 0;
      }
    }

    .filter-options {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      gap: 20rpx;
      padding: 0 10rpx;

      .filter-option {
        flex: 1;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        border-radius: 36rpx;
        background: #f5f5f5;
        color: #666;

        .iconfont {
          font-size: 30rpx;
          margin-right: 8rpx;
          color: #666;
        }

        // 移除针对不同选项的固定样式
        &.option-all,
        &.option-online,
        &.option-paper {
          // 不需要单独设置样式
        }

        // 只保留激活状态的样式
        &.active {
          background: #ffd600;
          color: #333;
          font-weight: 500;

          .iconfont {
            color: #333;
          }
        }
      }
    }
  }
}

// 添加动画延迟类
.animate__delay_300ms {
  animation-delay: 300ms;
}

.animate__delay_500ms {
  animation-delay: 500ms;
}

.general-feedback {
  margin: 24rpx 0;
  border-radius: 12rpx;
  background-color: #fff;
  padding: 24rpx;
  border-top: none;

  .feedback-title {
    margin-bottom: 20rpx;

    .icon {
      width: 36rpx;
      height: 36rpx;
    }

    text {
      font-size: 30rpx;
    }
  }
}
</style>
