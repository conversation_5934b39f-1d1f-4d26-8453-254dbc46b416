import { createPinia } from "pinia";
import { createSSRApp } from "vue";
import App from "./App.vue";
import {
  getTraceId,
  initLogService,
  LogLevel,
  uploadLogNonBlocking,
} from "./utils/log";

// 初始化日志服务
setTimeout(() => {
  try {
    const initResult = initLogService();
    console.log("日志服务已初始化");

    // 如果日志服务初始化成功，上报应用启动日志
    if (initResult) {
      // 延迟1秒上报日志，确保初始化完全完成
      setTimeout(() => {
        try {
          // 获取当前 traceId
          const traceId = getTraceId();
          console.log("当前应用 traceId:", traceId);

          // 获取系统信息
          let systemInfo: UniApp.GetSystemInfoResult | null = null;
          try {
            systemInfo = uni.getSystemInfoSync();
          } catch (e) {
            console.error("获取系统信息失败:", e);
          }

          // 上报应用启动日志（非阻塞方式）
          uploadLogNonBlocking(LogLevel.INFO, `应用启动`);
        } catch (logError) {
          console.error("提交日志时出错:", logError);
        }
      }, 1000);
    }
  } catch (error) {
    console.error("初始化日志服务失败:", error);
  }
}, 2000); // 延迟2秒初始化，确保应用已完全加载

export function createApp() {
  const app = createSSRApp(App);
  const pinia = createPinia();
  app.use(pinia);
  return {
    app,
  };
}
