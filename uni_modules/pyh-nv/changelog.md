## 1.6.0（2024-01-16）
优化css代码格式
## 1.5.9（2023-12-14）
修复返回键自定义图标覆盖问题
## 1.5.8（2023-09-19）
兼容pc端浏览小程序时胶囊处理
## 1.5.7（2023-09-19）
修复小程序model时胶囊占位问题
## 1.5.6（2023-09-18）
bgColor添加transparent设置透明底色
## 1.5.5（2023-08-24）
1、优化/修复小程序胶囊错位以及所占区域大小问题  
2、新增shadow来设置导航栏阴影
## 1.5.4（2023-07-03）
添加输入框搜索图标的控制，详情查看config->search->icon，以及示例项目
## 1.5.3（2023-05-17）
nvue兼容优化
## 1.5.2（2023-04-23）
```markdown
一、添加type->slot插槽模式,nvSlotTap点击事件
二、调整position写法，改为{type:'fixed'}格式，代码兼容之前版本的写法，position->top目前仅model模式可用，控制距离顶部的距离（tips:页面内导航栏fixedAssist.hide时，再使用model导航会用到）
```
## 1.5.1（2023-04-12）
返回键自定义图标
## 1.5.0（2023-03-30）
```markdown
修改补充1.4.8版说明，如下：
1、新增全局隐藏设置（getApp().globalData.nvShow），可以单组件覆盖，model模型不受影响；
2、model模型导航栏可添加多个并且能固定定位到对应位置；
3、nvue兼容优化
```
## 1.4.9（2023-03-30）
修改补充1.4.8版说明
## 1.4.8（2023-03-30）
```markdown
1、新增全局隐藏设置（getApp().globalData.nvShow），可以单组件覆盖，model模型不受影响；
2、model模型导航栏可添加多个并且能固定定位到对应位置；
3、nvue兼容优化
```
## 1.4.6（2023-03-20）
去除了示例项目的nv的命名，统一使用pyh-nv。如果之前有使用nv组件名的，注意兼容（pages.json->easycom）
## 1.4.5（2023-03-15）
优化search的字体颜色和图标颜色
## 1.4.4（2023-03-01）
修复部分vue3已知问题
## 1.4.3（2023-02-17）
pc端浏览器导航栏兼容优化
## 1.4.2（2023-02-17）
优化pc端浏览器导航的显示（浏览器窗口拖动变化时导航变化灵敏度）
## 1.4.1（2023-02-17）
优化pc端浏览器导航的显示（浏览器窗口改变导致导航宽度问题）
## 1.4.0（2023-02-17）
修复pc端浏览器的导航栏高度问题
## 1.3.9（2023-02-14）
```markdown
一、新增单页面活动态颜色配置mainColor，可覆盖getApp().globalData.mainColor，活动态颜色目前仅部分导航栏有用到（搜索、tab切换）
二、更新示例项目（对应插件版本：1.3.9）
三、更新以下插件说明：
	1、去掉了文档旧的属性的说明(hideback，backpress)，新版的有兼容，已使用的不受影响
	2、添加了mainColor说明和全局变量getApp().globalData的官方参考链接
```
## 1.3.8（2023-02-06）
兼容vue3（注意：easycom的写法已更新），可参考说明文档或示例项目
## 1.3.7（2023-01-04）
修复导航栏背景图固定问题
## 1.3.6（2022-09-14）
优化代码，监听config赋值更新配置
## *******（2022-08-31）
无功能更新，优化config配置
## *******（2022-08-31）
修复1.3.5问题
## 1.3.5（2022-08-31）
1、新增back，来控制返回键，前版本的backpress和hideback功能都迁移到了back，详细用法见文档；
2、规范/优化事件，比如nvInputTap、nvBackTap
## *******（2022-08-26）
1、优化address字体颜色问题，默认继承导航栏字体颜色，也可以传入color固定。
2、优化了渐变导航栏在h5端开发时颜色显示问题
## *******（2022-08-26）
1、修复1.3.4版本reverse的bug
2、address已调整为可以在其它type下出现（之前只有type为search可以）
## *******（2022-08-25）
1.3.4的bug修复
## 1.3.4（2022-08-25）
渐变添加反向变透明选项reverse，可以渐变为透明
## 1.3.3（2022-06-30）
1、修改了地址的样式（想去可参考示例search.vue）\r\n
2、添加了type为search或image时使用componentsFlex来控制flex的align-items，其中image只能控制图片
## 1.3.2（2022-06-28）
1、优化tabArr活动态切换（组件内代码根据点击切换选项），传入的active改为初始活动态的选项（可不传），和旧版本不冲突；
2、优化logo的mod类型（根据传入style来自动判断类型）；
3、新增windowInfo来自定义窗口，不传默认为uni.getSystemInfoSync()的窗口
## 1.3.1（2021-12-17）
修复判断屏幕方向导致ipad等大屏显示高度不正常
## 1.3.0（2021-11-23）
1、补充1.2.9版本空navigationBarTitleText时赋值浏览器标题。\n
2、补充说明，h5端浏览器导航栏的配置，最好pages.json内不填写navigationBarTitleText，可参考示例项目
## 1.2.9（2021-11-23）
1、移除pyh-nv.vue内配置h5AutoTitle，效果不变，如果config.title键存在，则不取当前浏览器标题，否则取当前浏览器标题。注：浏览器默认标题为pages.json内配置的navigationBarTitleText。
2、新增sysncTitle属性，可在组件内、globalData和单页面config修改，用于是否控制浏览器标题与当前标题同步
## 1.2.8（2021-08-17）
1、修复搜索导航栏按钮组位置问题
## 1.2.7（2021-08-13）
1、h5端自动同步标签标题（如微信标题）<br>
2、组件内新增h5AutoTitle，配置后可以h5端自动获取页面标题，详情看描述文件
## 1.2.6（2021-08-13）
更新说明文档
## 1.2.5（2021-08-13）
修复按钮组位置偏差问题
## 1.2.4（2021-07-21）
1、btn的icon添加text，可以带文字<br>
2、修复样式（btn右边间距、输入框字体大小改为inherit等）
## 1.2.3（2021-07-20）
1、开启了model滚动监听<br>
2、按钮组添加了badge设置角标
## 1.2.2（2021-05-25）
1、新增设置样式事件setStyle()，可通过ref来调用
2、修改config内style传参格式（改为string类型），兼容小程序
3、新增组件内注释
4、其它小优化
## 1.2.1（2021-03-16）
处理了手机从竖屏变横屏后显示错乱问题
## 1.2.0（2021-03-05）
处理了右按钮样式小程序兼容错误
## 1.1.9（2021-03-01）
处理nvue兼容性（引入scss失败、css错误）
## 1.1.8（2021-02-25）
1、兼容nvue
2、添加背景图
## 1.1.7（2021-02-24）
1、兼容uni_modules（官方新推出的插件管理）；兼容nv写法，需要在pages.json添加代码："easycom": {"nv": "@/uni_modules/pyh-nv/components/pyh-nv/pyh-nv.vue"}
2、nvRoute函数更名为nv

## 1.1.6（2021-02-01）
1、修复model定位问题

## 1.1.5（2021-02-01）
1、全类型支持设置右方按钮组
2、添加h5 document.title等于config的标题
3、添加属性model，可在页面内独立使用，常用于不满足右方按钮小程序不显示的兼容方案（使用2个nv）
4、优化代码结构

## 1.1.4（2020-11-18）
1、修复微信公众号中input的disabled点击跳转失效问题
2、修复右上角纯图片按钮变形问题

## 1.1.3（2020-09-27）
修复config空值有时会报错的bug

## 1.1.2（2020-09-14）
添加通用导航栏渐变色背景功能（渐变色背景会导致transparent背景色渐变失效）

## 1.1.1（2020-09-01）
修复icon原生组件在小程序内高度铺满导致的错误问题

## 1.1.0（2020-08-27）
1、使用icon代替图片图标,完全独立组件
2、添加回到顶部的功能

## 1.0.9（2020-08-18）
''' 1、单logo模式，支持全样式设置，可实现全背景图等
2、优化了路由跳转判断及多端跳转
3、组件内，利用了scss的特性，优化了主色的修改
4、示例项目内添加了全局变量globalData，以及全路由封装函数nvRoute，组件也做了兼容处理，可快速设置配置，如需路由做特殊处理（比如history模式等），可使用封装的nvRoute统一处理 '''

## 1.0.8（2020-08-17）
1、修改搜索框动态赋值方式，更加方便，直接修改search.value，需要初始化value，旧的赋值方式已废弃。（重要） 2、注释样式：上版本组件内样式，没有注释uni.scss的部分

## 1.0.7（2020-08-13）
1、修改标题字体的size和weight,等同于uniapp的h5样式 2、补充组件主色覆盖样式的注释，可去除注释快速修改，也可使用uni.scss快速修改主色

## 1.0.6（2020-07-29）
1、补充文档对于搜索框赋值的说明，添加动态赋值功能

## 1.0.5（2020-07-20）
补充单组件文件缺少的文件iconfont.wxss(后续版本已移除该文件)

## 1.0.4（2020-07-08）
1、修复fixed定位，辅助容器高度问题

2、补充示例项目属性项

## 1.0.3（2020-07-08）
1、添加config.position属性，并且默认为'fixed' 2、添加config.fixedAssist属性———固定定位辅助导航栏，高度与导航栏一致，可设置背景色 3、原home返回键背景取消，如需要，需使用componentBgColor 4、状态栏字体颜色与导航栏字体颜色一致（状态栏字体只支持#000000或#ffffff） 5、config.color 改为导航栏和状态栏字体色，也用于渐变完成时字体色（状态栏字体只支持#000000或#ffffff） 6、transparent.initColor代替之前的状态栏字体颜色设置，该值为导航栏与状态栏初始色（状态栏字体只支持#000000或#ffffff） 7、修改默认字体色为'#000000'

## 1.0.2（2020-07-07）
修改示例配置，更友好上手

## 1.0.1（2020-07-07）
上传初版,更新说明文档

## 1.0.0（2020-07-07）
上传初版