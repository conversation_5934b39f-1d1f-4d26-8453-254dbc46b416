<script setup lang="ts">
// @ts-ignore
import { onHide, onLaunch, onShow } from "@dcloudio/uni-app";
import { checkUpdate } from "./utils/version";

onLaunch(() => {
  console.log("App Launch");

  // 检查微信小程序版本更新
  checkUpdate();

  // 设置默认分享内容
  uni.onAppShow((options) => {
    // 监听小程序启动或切前台，可根据场景值(options.scene)设置不同分享内容
    console.log("App Show with options:", options);
  });
});

onShow(() => {
  console.log("App Show");
});

onHide(() => {
  console.log("App Hide");
});

// 全局分享设置
// #ifdef MP-WEIXIN
uni.showShareMenu({
  withShareTicket: true,
  menus: ["shareAppMessage", "shareTimeline"],
});
// #endif
</script>

<style lang="scss">
/*每个页面公共css */
@import "./static/iconfont/iconfont.css";

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica,
    Segoe UI, Arial, Roboto, "PingFang SC", "miui", "Hiragino Sans GB",
    "Microsoft Yahei", sans-serif;
}

/* 修复文字颜色渲染问题 */
text,
view {
  opacity: 0.999;
}

button {
  margin: 0;
  padding: 0;
  border: 0 none;
  background: none;
  outline: none;
  position: relative;
  overflow: visible;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: none;
    transform: none;
    border-radius: inherit;
    pointer-events: none;
  }
}

/* #ifdef MP-WEIXIN */
text,
view {
  opacity: 0.999;
}
/* #endif */
/* 定义按钮的基础样式 */
</style>
