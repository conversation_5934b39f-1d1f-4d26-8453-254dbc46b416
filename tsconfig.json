{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["./*"]}, "types": ["@dcloudio/types", "@types/node", "./types/uni-app-extend"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true}, "include": ["**/*.ts", "**/*.d.ts", "**/*.tsx", "**/*.vue"], "exclude": ["node_modules", "unpackage"]}