<template>
  <uni-popup ref="settingsPopup" type="bottom">
    <view class="settings-popup">
      <view class="settings-title">选择播放次数</view>
      <view class="play-times">
        <view
          v-for="time in [1, 2, 3]"
          :key="time"
          :class="['time-item', { active: modelValue.playTimes === time }]"
          @click="updatePlayTimes(time)"
        >
          {{ time }}次
        </view>
      </view>

      <view class="settings-title">选择停顿时长</view>
      <view class="pause-times">
        <view
          v-for="time in [2, 4, 6, 8]"
          :key="time"
          :class="['time-item', { active: modelValue.pauseSeconds === time }]"
          @click="updatePauseSeconds(time)"
        >
          {{ time }}s
        </view>
      </view>

      <view class="settings-actions">
        <button class="cancel-btn" @click="handleCancel">取消</button>
        <button class="confirm-btn" @click="handleConfirm">确定</button>
      </view>
    </view>
  </uni-popup>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    default: () => ({
      playTimes: 2,
      pauseSeconds: 2,
    }),
  },
});

const emit = defineEmits(["update:modelValue", "save", "cancel"]);

const settingsPopup = ref();

const updatePlayTimes = (time: number) => {
  emit("update:modelValue", { ...props.modelValue, playTimes: time });
};

const updatePauseSeconds = (time: number) => {
  emit("update:modelValue", { ...props.modelValue, pauseSeconds: time });
};

const handleConfirm = () => {
  emit("save");
  settingsPopup.value.close();
};

const handleCancel = () => {
  emit("cancel");
  settingsPopup.value.close();
};

// 暴露方法给父组件
defineExpose({
  open: () => settingsPopup.value.open(),
  close: () => settingsPopup.value.close(),
});
</script>

<style lang="scss" scoped>
// 添加按钮重置样式
button {
  margin: 0;
  padding: 0;
  border: 0 none;
  background: none;
  outline: none;
  position: relative;
  overflow: visible;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: none;
    transform: none;
    border-radius: inherit;
    pointer-events: none;
  }
}

.settings-popup {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  box-sizing: border-box;

  .settings-title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 32rpx;
    font-weight: 500;
  }

  .play-times,
  .pause-times {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 48rpx;
    padding: 0 12rpx;
    box-sizing: border-box;

    .time-item {
      width: 112rpx;
      height: 76rpx;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      color: #666;

      &.active {
        background-color: rgba(255, 214, 0, 0.1);
        color: #333;
        border: 2rpx solid #ffd600;
        font-weight: 500;
      }
    }
  }

  .settings-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;
    margin-top: 48rpx;
    padding: 0 12rpx;
    box-sizing: border-box;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 72rpx;
      border-radius: 36rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30rpx;
      transition: all 0.3s ease;
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: #666;

      &:active {
        background-color: #ebebeb;
      }
    }

    .confirm-btn {
      background-color: #ffd600;
      color: #333;
      font-weight: 500;

      &:active {
        opacity: 0.9;
      }
    }
  }
}
</style>
