<template>
  <view class="dictation-tips">
    <!-- 使用单一的完整图片，根据设备类型显示不同图片 -->
    <view class="tips-image-container">
      <image
        v-if="isIPad"
        src="/static/icons/icon-diction-tips-ipad.png"
        mode="widthFix"
        class="tips-image-ipad"
      ></image>
      <image
        v-else
        src="/static/icons/icon-diction-tips.png"
        mode="widthFix"
        class="tips-image"
      ></image>
    </view>

    <!-- 音频播放器（隐藏，只保留功能） -->
    <view class="tips-audio" style="display: none">
      <view class="audio-player">
        <view class="audio-waves" :class="{ 'is-playing': isPlaying }">
          <view
            v-for="i in 16"
            :key="i"
            class="wave-bar"
            :style="{ animationDelay: `${i * 0.05}s` }"
          ></view>
        </view>
      </view>
    </view>

    <!-- 按钮 -->
    <button
      class="know-btn"
      @click="handleKnow"
      :disabled="countdown > 0"
      :class="{ disabled: countdown > 0 }"
    >
      {{ countdown > 0 ? `我知道了 (${countdown}s)` : "我知道了" }}
    </button>
  </view>
</template>

<script lang="ts" setup>
import { AudioPlayer } from "@/sub-exercise/utils/audio-player";
import { computed, onMounted, onUnmounted, ref } from "vue";

const emit = defineEmits(["close"]);

const isPlaying = ref(false);
const audioPlayer = new AudioPlayer();
const audioUrl = "https://file.bpbl68.cn/config/diction-tips-sounds.mp3";

// 添加倒计时变量
const countdown = ref(0);
const countdownTimer = ref<any>(null);
const COUNTDOWN_KEY = "dictation_tips_shown";

// 判断是否是iPad设备
const isIPad = computed(() => {
  // 兼容多种判断方式
  let result = false;

  // 方式1：通过系统信息判断
  try {
    const systemInfo = uni.getSystemInfoSync();
    console.log("systemInfo", systemInfo);
    console.log("systemInfo.osName", systemInfo.osName);
    console.log("systemInfo.windowWidth", systemInfo.windowWidth);
    // iPad的屏幕宽度通常大于768px
    if (
      systemInfo.osName.toLowerCase() === "ios" &&
      systemInfo.windowWidth >= 768
    ) {
      console.log("isIPad", systemInfo);
      result = true;
    }
  } catch (e) {
    console.error("获取系统信息失败:", e);
  }

  // 方式2：通过媒体查询判断
  if (typeof window !== "undefined" && window.matchMedia) {
    if (window.matchMedia("(min-width: 768px)").matches) {
      console.log("isIPad", window.matchMedia("(min-width: 768px)"));
      result = true;
    }
  }

  console.log("isIPad", result);
  return result;
});

// 点击"我知道了"按钮
const handleKnow = () => {
  // 如果还在倒计时，则不执行操作
  if (countdown.value > 0) return;

  // 停止音频播放
  if (isPlaying.value) {
    audioPlayer.stop();
  }
  emit("close");
};

// 检查是否是首次查看提示
const checkFirstView = () => {
  try {
    const hasShown = uni.getStorageSync(COUNTDOWN_KEY);
    if (!hasShown) {
      // 首次查看，设置倒计时
      countdown.value = 5;
      startCountdown();
      // 保存显示记录
      uni.setStorageSync(COUNTDOWN_KEY, "true");
    }
  } catch (error) {
    console.error("获取缓存失败:", error);
    countdown.value = 5;
    startCountdown();
  }
};

// 开始倒计时
const startCountdown = () => {
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value);
    }
  }, 1000);
};

// 组件挂载时
onMounted(() => {
  // 检查是否需要倒计时
  checkFirstView();

  // 自动播放音频
  setTimeout(() => {
    audioPlayer.play(audioUrl, {
      onStart: () => {
        isPlaying.value = true;
      },
      onEnd: () => {
        isPlaying.value = false;
      },
      onError: (error) => {
        console.error("播放音频失败:", error);
        isPlaying.value = false;
      },
    });
  }, 500);
});

// 组件卸载时清除计时器和音频播放
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }
  audioPlayer.dispose();
});
</script>

<style lang="scss" scoped>
// 按钮重置样式
button {
  margin: 0;
  padding: 0;
  border: 0 none;
  background: none;
  outline: none;
  position: relative;
  overflow: visible;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: none;
    transform: none;
    border-radius: inherit;
    pointer-events: none;
  }
}

.dictation-tips {
  width: 100%;
  height: 100%;
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10rpx;

  .cartoon-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    height: 200rpx;
    margin-bottom: 20rpx;

    .cartoon-image {
      width: 120rpx;
      height: 120rpx;
      position: absolute;
      top: 0;
      left: 0;
    }

    .speech-bubble {
      width: 340rpx;
      height: 70rpx;
      background-color: #ffe978;
      border-radius: 35rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 40rpx;
      left: 140rpx;

      &::after {
        content: "";
        position: absolute;
        left: -10rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-top: 10rpx solid transparent;
        border-bottom: 10rpx solid transparent;
        border-right: 15rpx solid #ffe978;
      }

      .bubble-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }
  }

  .tips-image-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    padding: 40rpx 20rpx;

    .tips-image {
      width: 70%;
      max-width: 650rpx;
    }

    .tips-image-ipad {
      width: 80%;
      max-width: 650rpx;
    }
  }

  .know-btn {
    width: 520rpx;
    height: 88rpx;
    background: #ffd600;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 30rpx auto;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      opacity: 0.9;
    }

    &.disabled {
      background: #f0f0f0;
      color: #999;
      pointer-events: none;
    }
  }
}

@keyframes wave {
  0%,
  100% {
    transform: scaleY(0.5);
  }
  50% {
    transform: scaleY(1);
  }
}

// 平板适配
@media screen and (min-width: 768px) {
  .dictation-tips {
    .tips-image-container {
      .tips-image {
        max-width: 800rpx;
      }
    }
  }
}
</style>
