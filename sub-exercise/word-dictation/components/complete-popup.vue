<template>
  <uni-popup ref="popup" type="center" :mask-click="!tempImagePath">
    <view class="complete-popup">
      <view class="popup-header">
        <view class="complete-title">听写结束</view>
        <view class="complete-content">
          <text>已完成听写，拍照检查吧！</text>
          <text class="sub-text">记得拍清楚，\n确保每个单词都清晰可见哦～</text>
        </view>
      </view>

      <!-- 未上传状态 -->
      <view class="upload-area" v-if="!tempImagePath">
        <view class="upload-actions">
          <button class="upload-btn camera" @click="takePhoto">
            <text class="iconfont icon-camera"></text>
            <text>拍照上传</text>
          </button>
        </view>
      </view>

      <!-- 已上传图片状态 -->
      <view class="image-container" v-else>
        <view class="image-wrapper">
          <image
            :src="tempImagePath"
            mode="aspectFit"
            class="preview-image"
            @click="previewImage"
          ></image>

          <!-- 图片扫描效果和OCR处理状态 - 合并显示在图片上 -->
          <view class="ocr-overlay" v-if="isProcessing">
            <!-- 扫描线效果 -->
            <view class="scan-line"></view>

            <!-- OCR识别状态 -->
            <view class="ocr-status">
              <view class="processing-container">
                <view class="processing-title">正在识别...</view>
                <view class="processing-steps">
                  <view class="step" :class="{ active: processingStep >= 1 }">
                    <text class="step-icon">1</text>
                    <text class="step-text">图像上传</text>
                  </view>
                  <view
                    class="step-line"
                    :class="{ active: processingStep >= 1 }"
                  ></view>
                  <view class="step" :class="{ active: processingStep >= 2 }">
                    <text class="step-icon">2</text>
                    <text class="step-text">文字识别</text>
                  </view>
                  <view
                    class="step-line"
                    :class="{ active: processingStep >= 2 }"
                  ></view>
                  <view class="step" :class="{ active: processingStep >= 3 }">
                    <text class="step-icon">3</text>
                    <text class="step-text">智能批改</text>
                  </view>
                </view>
                <view class="processing-progress">
                  <view
                    class="progress-bar"
                    :style="{ width: processingProgress + '%' }"
                  ></view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="image-actions">
          <button class="action-btn secondary" @click="resetImage">
            重新上传
          </button>
          <button class="action-btn primary" @click="submitImage">
            提交批改
          </button>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script lang="ts" setup>
import { uploadFile } from "@/api/file";
import { submitDictation } from "@/api/homework-submission";
import { ref, watch } from "vue";

// 定义属性
const props = defineProps({
  words: {
    type: Array,
    default: () => [],
  },
  exerciseId: {
    type: Number,
    default: 0,
  },
});

// 定义事件
const emit = defineEmits(["grade-result"]);

// 状态变量
const popup = ref();
const tempImagePath = ref("");
const isProcessing = ref(false);
const processingStep = ref(0);
const processingProgress = ref(0);
const uploadStartTime = ref(0);
const recognitionStartTime = ref(0);
const maskClickable = ref(true);

// 监听图片上传状态变化
watch(tempImagePath, (newVal) => {
  maskClickable.value = !newVal;
});

// 方法
// 打开弹窗
const open = () => {
  popup.value.open();
};

// 关闭弹窗
const closePopup = () => {
  popup.value.close();
};

// 拍照
const takePhoto = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ["original", "compressed"],
    sourceType: ["camera"],
    success: (res) => {
      tempImagePath.value = res.tempFilePaths[0];
    },
  });
};

// 重置图片
const resetImage = () => {
  tempImagePath.value = "";
};

// 提交图片
const submitImage = () => {
  if (!tempImagePath.value) {
    uni.showToast({
      title: "请先上传图片",
      icon: "none",
    });
    return;
  }

  // 显示OCR处理动效
  isProcessing.value = true;
  processingStep.value = 0;
  processingProgress.value = 0;

  // 记录开始时间
  uploadStartTime.value = Date.now();

  // 开始处理流程
  processImageAndSubmit();
};

// 处理图片并提交
const processImageAndSubmit = async () => {
  try {
    // 启动进度条动画 - 图像上传阶段 (0-30%)
    startProgressAnimation(0, 30);

    // 上传图片
    const imageUrl = await uploadImageToServer();

    if (!imageUrl) {
      throw new Error("图片上传失败");
    }

    // 计算上传耗时并更新进度
    const uploadTime = Date.now() - uploadStartTime.value;
    processingStep.value = 1;
    processingProgress.value = 30;

    // 记录识别开始时间
    recognitionStartTime.value = Date.now();

    // 启动进度条动画 - 文字识别阶段 (30-70%)
    startProgressAnimation(30, 70);

    // 延迟一小段时间再进入识别阶段，确保用户能看到阶段变化
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 调用听写批改接口，设置超时时间为 20 秒
    const response = await submitDictation(
      {
        exerciseId: props.exerciseId,
        imageUrl: imageUrl,
      },
      20000
    ); // 设置超时时间为 20 秒

    // 检查响应码，处理特殊错误情况
    if (response.code === 1002006000) {
      // 图片无法识别的错误码
      resetImage(); // 清空已上传的图片
      throw {
        code: 1002006000,
        message: "图片内容无法识别，请确保书写清晰并重新拍照上传",
      } as ApiError;
    }

    // 计算识别耗时
    const recognitionTime = Date.now() - recognitionStartTime.value;

    // 更新进度到第三阶段
    processingStep.value = 2;
    processingProgress.value = 70;

    // 启动进度条动画 - 智能批改阶段 (70-100%)
    startProgressAnimation(70, 100);

    // 延迟一小段时间再进入批改阶段，确保用户能看到阶段变化
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 更新到最终阶段
    processingStep.value = 3;

    // 确保进度条完成后再显示结果
    setTimeout(() => {
      isProcessing.value = false;
      const resultData = response.getData() || {};

      // 组装结果数据
      const result = {
        exerciseId: props.exerciseId,
        totalWords: props.words.length,
        correctWords:
          resultData.correctCount || Math.floor(props.words.length * 0.8),
        imageUrl: imageUrl,
        details: resultData.details || [],
      };

      // 发送结果事件
      emit("grade-result", result);

      // 关闭当前弹窗
      closePopup();

      // 重置图片状态，方便下次使用
      resetImage();
    }, Math.max(500, 3700 - uploadTime - recognitionTime)); // 确保总时间至少有3.7秒的体验
  } catch (error) {
    handleApiError(error, "处理失败");
  }
};

// 上传图片到服务器
const uploadImageToServer = async (): Promise<string> => {
  try {
    uni.showLoading({
      title: "上传中...",
      mask: true,
    });

    // 使用file.ts中的uploadFile函数
    const response = await uploadFile(tempImagePath.value);

    uni.hideLoading();

    if (response.code === 0 && response.data) {
      return response.data.url;
    } else {
      throw new Error(response.msg || "上传失败");
    }
  } catch (error) {
    uni.hideLoading();
    throw error;
  }
};

// 定义API错误接口类型
interface ApiError {
  code: number;
  message: string;
}

// 处理API错误
const handleApiError = (error: unknown, defaultMsg: string) => {
  console.error("API错误:", error);
  isProcessing.value = false;

  // 检查是否是特定的1002006000错误码
  if (
    error &&
    typeof error === "object" &&
    "code" in error &&
    (error as ApiError).code === 1002006000
  ) {
    // 使用模态框显示错误，更醒目
    uni.showModal({
      title: "识别失败",
      content:
        (error as ApiError).message ||
        "图片内容无法识别，请确保书写清晰并重新拍照上传",
      showCancel: false,
      confirmText: "重新上传",
      success: () => {
        // 强制回到上传状态
        resetImage();
      },
    });
  } else {
    // 其他错误使用Toast提示
    uni.showToast({
      title:
        typeof error === "string"
          ? error
          : error && typeof error === "object" && "message" in error
          ? (error as ApiError).message
          : defaultMsg,
      icon: "none",
      duration: 3000, // 延长提示显示时间
    });

    // 不重置图片状态，允许用户再次尝试提交
    // 当错误是网络问题时，保留已上传的图片让用户可以再次点击提交
  }
};

// 启动进度条动画
const startProgressAnimation = (start: number, end: number) => {
  // 计算合理的动画时间，基于剩余百分比
  const remainingPercentage = end - start;
  const animationDuration = remainingPercentage * 30; // 每1%大约需要30ms

  const stepSize = 1;
  const steps = remainingPercentage / stepSize;
  const intervalTime = animationDuration / steps;

  const timer = setInterval(() => {
    if (processingProgress.value < end) {
      processingProgress.value += stepSize;
    } else {
      clearInterval(timer);
    }
  }, intervalTime);
};

// 预览图片
const previewImage = () => {
  if (tempImagePath.value) {
    uni.previewImage({
      urls: [tempImagePath.value],
      current: 0,
      indicator: "number",
      loop: false,
    });
  }
};

// 向外暴露方法
defineExpose({
  open,
  setUploadedImage(imagePath: string) {
    tempImagePath.value = imagePath;
  },
});
</script>

<style lang="scss" scoped>
.complete-popup {
  width: 600rpx;
  background: linear-gradient(160deg, #fbf3d3 0%, #ffffff 100%);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;

  .popup-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40rpx;

    .complete-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #ffbf00;
      margin-bottom: 20rpx;
      text-align: center;
    }

    .complete-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      text {
        font-size: 30rpx;
        color: #333333;
        font-weight: 500;
        line-height: 1.5;
      }

      .sub-text {
        margin-top: 12rpx;
        font-size: 26rpx;
        color: #666666;
        font-weight: normal;
        white-space: pre-line;
      }
    }
  }

  // 上传区域样式
  .upload-area {
    display: flex;
    flex-direction: column;
    margin-bottom: 30rpx;

    .upload-actions {
      display: flex;
      justify-content: center;
      gap: 20rpx;

      .upload-btn {
        width: 80%;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40rpx;
        font-size: 28rpx;
        font-weight: 500;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;

        .iconfont {
          margin-right: 8rpx;
          font-size: 32rpx;
        }

        &:active {
          transform: scale(0.95);
          opacity: 0.9;
        }

        &.camera {
          background-color: #ffde59;
          color: #333;

          .iconfont {
            color: #333;
          }
        }
      }
    }
  }

  // 图片容器样式
  .image-container {
    margin-bottom: 30rpx;

    .image-wrapper {
      position: relative;
      width: 100%;
      height: 360rpx;
      margin-bottom: 30rpx;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

      .preview-image {
        width: 100%;
        height: 100%;
        border-radius: 16rpx;
        cursor: pointer;
      }

      // OCR图片覆盖层 - 将扫描效果和状态组合
      .ocr-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(
          0,
          0,
          0,
          0.65
        ); // 半透明黑色背景，确保文字清晰可见
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20rpx;

        // 扫描线效果
        .scan-line {
          position: absolute;
          left: 0;
          width: 100%;
          height: 4rpx;
          background: linear-gradient(
            to right,
            transparent,
            #ffde59,
            transparent
          );
          box-shadow: 0 0 10rpx rgba(255, 222, 89, 0.8);
          animation: scanImage 2s linear infinite;
          z-index: 1;
        }

        // OCR识别状态 - 覆盖在图片上
        .ocr-status {
          width: 100%;
          z-index: 2;

          .processing-container {
            width: 100%;
            position: relative;

            .processing-title {
              font-size: 32rpx;
              font-weight: bold;
              margin-bottom: 24rpx;
              text-align: center;
              color: #fff; // 白色文字在深色背景上更醒目
              text-shadow: 0 0 8rpx rgba(0, 0, 0, 0.3);
            }

            .processing-steps {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 24rpx;

              .step {
                display: flex;
                flex-direction: column;
                align-items: center;

                .step-icon {
                  width: 54rpx;
                  height: 54rpx;
                  border-radius: 50%;
                  background-color: rgba(255, 255, 255, 0.2);
                  color: rgba(255, 255, 255, 0.7);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 26rpx;
                  font-weight: bold;
                  margin-bottom: 10rpx;
                  transition: all 0.3s ease;
                }

                .step-text {
                  font-size: 22rpx;
                  color: rgba(255, 255, 255, 0.7);
                  transition: all 0.3s ease;
                }

                &.active {
                  .step-icon {
                    background-color: #ffd600;
                    color: #333;
                    transform: scale(1.1);
                    box-shadow: 0 0 12rpx rgba(255, 214, 0, 0.5);
                  }

                  .step-text {
                    color: #fff;
                    font-weight: 500;
                  }
                }
              }

              .step-line {
                flex: 1;
                height: 2rpx;
                background-color: rgba(255, 255, 255, 0.2);
                margin: 0 8rpx;
                margin-bottom: 32rpx;
                transition: all 0.3s ease;

                &.active {
                  background: linear-gradient(to right, #ffd600, #ffcc00);
                  height: 3rpx;
                  box-shadow: 0 0 8rpx rgba(255, 214, 0, 0.3);
                }
              }
            }

            .processing-progress {
              width: 100%;
              height: 8rpx;
              background-color: rgba(255, 255, 255, 0.2);
              border-radius: 4rpx;
              overflow: hidden;

              .progress-bar {
                height: 100%;
                background: linear-gradient(to right, #ffe251, #ffcc00);
                border-radius: 4rpx;
                transition: width 0.3s ease;
                position: relative;
                overflow: hidden;

                &::after {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background: linear-gradient(
                    to right,
                    rgba(255, 255, 255, 0) 0%,
                    rgba(255, 255, 255, 0.6) 50%,
                    rgba(255, 255, 255, 0) 100%
                  );
                  animation: shimmer 1.5s infinite;
                }
              }
            }
          }
        }
      }
    }

    .image-actions {
      display: flex;
      justify-content: space-between;
      gap: 20rpx;

      .action-btn {
        flex: 1;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40rpx;
        font-size: 28rpx;
        font-weight: 500;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
          opacity: 0.9;
        }

        &.secondary {
          background-color: #f5f5f5;
          color: #666;
        }

        &.primary {
          background-color: #ffde59;
          color: #333;
          font-weight: bold;
        }
      }
    }
  }
}

@keyframes scanImage {
  0% {
    top: 0;
  }
  100% {
    top: 100%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes titlePulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style>
