<template>
  <view class="ready-page">
    <image
      class="ready-image"
      src="@/static/icons/dictation-write.svg"
      mode="aspectFit"
    />
    <text class="ready-title">请准备好纸笔</text>
    <text class="ready-subtitle">听写即将开始啦</text>
    <button class="ready-btn" @click="handleReady">准备好了</button>
  </view>
</template>

<script lang="ts" setup>
const emit = defineEmits(["ready"]);

const handleReady = () => {
  emit("ready");
};
</script>

<style lang="scss" scoped>
// 添加按钮重置样式
button {
  margin: 0;
  padding: 0;
  border: 0 none;
  background: none;
  outline: none;
  position: relative;
  overflow: visible;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: none;
    transform: none;
    border-radius: inherit;
    pointer-events: none;
  }
}

.ready-page {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  .ready-image {
    width: 400rpx;
    height: 400rpx;
    margin-bottom: 48rpx;
  }

  .ready-title {
    font-size: 36rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
  }

  .ready-subtitle {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 64rpx;
  }

  .ready-btn {
    width: 320rpx;
    height: 88rpx;
    background: #ffd600;
    border-radius: 44rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      opacity: 0.9;
    }
  }
}
</style>
