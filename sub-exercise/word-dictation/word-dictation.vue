<template>
  <common-box :loading="loading" title="单词听写" @back="handleBack">
    <!-- 添加 left-txt 插槽内容 -->
    <template #left-txt>
      <view class="left-txt" v-if="!loading"
        >{{ currentIndex + 1 }}/{{ totalWords }}</view
      >
    </template>

    <template v-if="!loading">
      <view class="dictation-container">
        <!-- 准备状态 -->
        <ready-page v-if="!isReady" @ready="handleReady" />

        <!-- 添加听写提示页面 -->
        <dictation-tips v-if="isReady && showTips" @close="handleTipsClose" />

        <!-- 听写状态 -->
        <template v-else-if="isReady && !showTips">
          <!-- 听写卡片区域 -->
          <swiper
            class="dictation-swiper"
            :current="currentIndex"
            :previous-margin="'80rpx'"
            :next-margin="'80rpx'"
            @change="handleSwiperChange"
          >
            <swiper-item
              v-for="(word, index) in words"
              :key="word.id"
              class="swiper-item"
            >
              <view
                class="dictation-card"
                :class="{ active: currentIndex === index }"
              >
                <!-- 音频波形图 -->
                <view class="audio-wave">
                  <view class="wave-container">
                    <view
                      v-for="i in 16"
                      :key="i"
                      class="wave-bar"
                      :class="{
                        'is-playing': isAudioPlaying && currentIndex === index,
                      }"
                      :style="{ animationDelay: `${i * 0.05}s` }"
                    ></view>
                  </view>
                  <text class="tip-text"
                    >每个单词读{{ settings.playTimes }}遍
                    {{ `(第${currentPlayCount}遍)` }}</text
                  >
                </view>
                <!-- 设置按钮 -->
                <view class="settings-btn" @click="showSettings">
                  <text class="iconfont icon-setting-fill"></text>
                </view>
                <!-- 添加中文提示按钮 -->
                <view class="hint-btn" @click="showHint">
                  <text class="iconfont icon-light"></text>
                  <text class="hint-text">中文提示</text>
                </view>
              </view>
            </swiper-item>
          </swiper>

          <!-- 音频控制区域 -->
          <view class="audio-controls">
            <view class="control-btn prev" @click="playPrevWord">
              <text class="iconfont icon-left-f"></text>
              <text class="btn-text">上一词</text>
            </view>
            <view class="control-btn play" @click="playCurrentWord">
              <text
                class="iconfont"
                :class="
                  isDictationPlaying ? 'icon-pause-circle' : 'icon-play-circle'
                "
              ></text>
            </view>
            <view class="control-btn next" @click="playNextWord">
              <text class="iconfont icon-right-f"></text>
              <text class="btn-text">下一词</text>
            </view>
          </view>
        </template>
      </view>
    </template>

    <!-- 配置弹窗 -->
    <settings-popup
      v-model="settings"
      ref="settingsPopup"
      @save="saveSettings"
      @cancel="closeSettings"
    />

    <!-- 完成弹窗 -->
    <complete-popup
      ref="completePopup"
      :words="words"
      :exercise-id="exerciseId"
      @grade-result="handleGradeResult"
    />

    <!-- 添加继续听写弹窗 -->
    <uni-popup ref="continuePopup" type="center">
      <view class="continue-popup">
        <view class="continue-title">继续听写</view>
        <view class="continue-content"
          >上次听写到第{{
            savedProgress.currentIndex + 1
          }}个单词，是否继续？</view
        >
        <view class="continue-actions">
          <button class="restart-btn" @click="handleRestart">重新开始</button>
          <button class="continue-btn" @click="handleContinue">继续听写</button>
        </view>
      </view>
    </uni-popup>
  </common-box>
</template>

<script lang="ts" setup>
import { getHomeworkExerciseDetail } from "@/api/homework-exercise";

import CommonBox from "@/components/common-box/commonBox.vue";

import { AudioPlayer } from "@/sub-exercise/utils/audio-player";
// @ts-ignore
import { onLoad } from "@dcloudio/uni-app";
import { computed, nextTick, onUnmounted, ref } from "vue";
import CompletePopup from "./components/complete-popup.vue";
import ReadyPage from "./components/ready-page.vue";
import SettingsPopup from "./components/settings-popup.vue";
// 引入听写提示组件
import DictationTips from "./components/dictation-tips.vue";

// 添加类型定义
interface LoadOptions {
  id?: string;
  homeworkId?: string;
  isRetake?: string;
  fromResult?: string;
  [key: string]: any; // 其他可能的参数
}

// 页面参数
const exerciseId = ref<number>(0);
const isRetake = ref<boolean>(false); // 是否是重拍模式
const fromResult = ref<boolean>(false); // 是否从结果页返回

// 状态变量
const loading = ref(true);
const currentIndex = ref(0);
const totalWords = ref(0);
const isAudioPlaying = ref(false); // 音频播放状态
const isDictationPlaying = ref(false); // 单词默写状态
const words = ref<any[]>([]);

// 添加提示页面显示状态
const showTips = ref(true);

// 弹窗 ref
const settingsPopup = ref();
const completePopup = ref();
const continuePopup = ref();

// 配置相关
const DEFAULT_SETTINGS = {
  playTimes: 2,
  pauseSeconds: 2,
};

const settings = ref({ ...DEFAULT_SETTINGS });

// 播放相关
const currentPlayCount = ref(0);
const playTimer = ref<any>(null);

// 创建音频播放器实例
const audioPlayer = new AudioPlayer();

// 添加新的计时器变量
const startPlayTimer = ref<any>(null);

// 计算属性
const isLastWord = computed(() => currentIndex.value === totalWords.value - 1);

// 添加准备状态变量
const isReady = ref(false);

// 添加进度相关变量
const savedProgress = ref({
  exerciseId: 0,
  currentIndex: 0,
});

// 获取练习详情
const fetchExerciseDetail = async () => {
  try {
    loading.value = true;
    const res = await getHomeworkExerciseDetail(exerciseId.value);
    const data = res.getData();

    // 处理听写数据
    if (data.extraWordData?.questions) {
      words.value = data.extraWordData.questions.map((q: any) => ({
        word: q.words.word,
        audioUrl: q.words.audioUrl,
        id: q.words.id,
        chineseMeaning: q.words.chineseMeaning || "暂无中文释义",
      }));
      totalWords.value = words.value.length;

      // 如果是重拍模式，等待数据加载完成后直接打开完成弹窗
      if (isRetake.value) {
        // 需要在DOM渲染完成后再打开弹窗
        nextTick(() => {
          isReady.value = true; // 设置为准备状态
          showTips.value = false; // 不显示提示页面
          setTimeout(() => {
            completePopup.value.open();
          }, 300);
        });
      }

      // 如果是从结果页返回，直接设置为准备状态
      if (fromResult.value) {
        isReady.value = true;
        showTips.value = false; // 不显示提示页面
        // 清除之前的进度
        clearProgress();
        // 重置到第一个单词
        currentIndex.value = 0;
        // 延迟一下再开始播放
        nextTick(() => {
          setTimeout(() => {
            playCurrentWord();
          }, 300);
        });
      }
    } else {
      uni.showToast({
        title: "练习数据格式错误",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("获取练习详情失败:", error);
    uni.showToast({
      title: typeof error === "string" ? error : "获取数据失败",
      icon: "none",
    });
  } finally {
    loading.value = false;
  }
};

// 从 localStorage 获取配置
const loadSettings = () => {
  try {
    const savedSettings = uni.getStorageSync("dictationSettings");
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings);
      settings.value = {
        playTimes: parsed.playTimes || DEFAULT_SETTINGS.playTimes,
        pauseSeconds: parsed.pauseSeconds || DEFAULT_SETTINGS.pauseSeconds,
      };
    }
  } catch (error) {
    console.error("加载配置失败:", error);
    settings.value = { ...DEFAULT_SETTINGS };
  }
};

// 保存配置到 localStorage
const saveSettings = () => {
  uni.setStorageSync("dictationSettings", JSON.stringify(settings.value));
  settingsPopup.value.close();
};

// 显示设置弹窗
const showSettings = () => {
  settingsPopup.value.open();
};

// 关闭设置弹窗
const closeSettings = () => {
  settingsPopup.value.close();
};

// 播放当前单词音频
const playCurrentWord = async () => {
  // 如果正在默写中，则暂停整个默写过程
  if (isDictationPlaying.value) {
    isDictationPlaying.value = false;
    isAudioPlaying.value = false;
    audioPlayer.stop();
    if (playTimer.value) {
      clearTimeout(playTimer.value);
      playTimer.value = null;
    }
    return;
  }
  // 如果已经播放完指定次数，切换到下一个单词
  if (currentPlayCount.value >= settings.value.playTimes) {
    if (currentIndex.value < totalWords.value - 1) {
      currentIndex.value++;
      currentPlayCount.value = 0;
      isDictationPlaying.value = true;
      saveProgress(); // 保存进度
      startWordDictation();
    } else {
      // 如果是最后一个单词，显示完成弹窗
      clearProgress(); // 清除进度
      completePopup.value.open();
    }
    return;
  }

  // 开始默写过程
  isDictationPlaying.value = true;
  startWordDictation();
};

// 开始单词默写
const startWordDictation = () => {
  // 清除所有计时器
  if (startPlayTimer.value) {
    clearTimeout(startPlayTimer.value);
    startPlayTimer.value = null;
  }
  if (playTimer.value) {
    clearTimeout(playTimer.value);
    playTimer.value = null;
  }
  // 停止之前的音频播放
  audioPlayer.stop();

  // 添加 500ms 延迟
  startPlayTimer.value = setTimeout(() => {
    isAudioPlaying.value = true;

    audioPlayer.play(words.value[currentIndex.value].audioUrl, {
      onStart: () => {
        isAudioPlaying.value = true;
      },
      onEnd: () => {
        isAudioPlaying.value = false;
        currentPlayCount.value++;

        // 如果还没播放完指定次数且默写状态未被暂停
        if (
          currentPlayCount.value < settings.value.playTimes &&
          isDictationPlaying.value
        ) {
          // 等待指定秒数后播放下一次
          playTimer.value = setTimeout(() => {
            console.log("播放完指定次数后，等待指定秒数切换到下一个单词");
            if (isDictationPlaying.value) {
              audioPlayer.play(words.value[currentIndex.value].audioUrl, {
                onStart: () => {
                  isAudioPlaying.value = true;
                },
                onEnd: () => {
                  isAudioPlaying.value = false;
                  currentPlayCount.value++;

                  // 判断是否需要切换到下一个单词
                  if (
                    currentPlayCount.value >= settings.value.playTimes &&
                    isDictationPlaying.value
                  ) {
                    // 播放完指定次数后，等待指定秒数切换到下一个单词
                    playTimer.value = setTimeout(() => {
                      if (isDictationPlaying.value) {
                        if (currentIndex.value < totalWords.value - 1) {
                          currentIndex.value++;
                          currentPlayCount.value = 0;
                          saveProgress(); // 保存进度
                          startWordDictation();
                        } else {
                          // 如果是最后一个单词，显示完成弹窗
                          isDictationPlaying.value = false;
                          clearProgress(); // 清除进度
                          completePopup.value.open();
                        }
                      }
                    }, settings.value.pauseSeconds * 1000);
                  }
                },
                onError: (error) => {
                  console.error("播放音频失败:", error);
                  isAudioPlaying.value = false;
                  uni.showToast({
                    title: error.customMsg || "音频播放失败",
                    icon: "none",
                    duration: 2000,
                  });
                },
              });
            }
          }, settings.value.pauseSeconds * 1000);
        } else if (isDictationPlaying.value) {
          // 播放完指定次数后，等待指定秒数切换到下一个单词
          playTimer.value = setTimeout(() => {
            console.log("播放完指定次数后，等待指定秒数切换到下一个单词");
            if (isDictationPlaying.value) {
              if (currentIndex.value < totalWords.value - 1) {
                currentIndex.value++;
                currentPlayCount.value = 0;
                saveProgress(); // 保存进度
                startWordDictation();
              } else {
                // 如果是最后一个单词，显示完成弹窗
                isDictationPlaying.value = false;
                clearProgress(); // 清除进度
                completePopup.value.open();
              }
            }
          }, settings.value.pauseSeconds * 1000);
        }
      },
      onError: (error) => {
        console.error("播放音频失败:", error);
        isAudioPlaying.value = false;
        uni.showToast({
          title: error.customMsg || "音频播放失败",
          icon: "none",
          duration: 2000,
        });
      },
    });
  }, 500);
};

// 播放上一个单词
const playPrevWord = () => {
  // 停止当前播放
  isDictationPlaying.value = false;
  isAudioPlaying.value = false;
  audioPlayer.stop();
  if (playTimer.value) {
    clearTimeout(playTimer.value);
    playTimer.value = null;
  }
  currentPlayCount.value = 0;

  if (currentIndex.value > 0) {
    currentIndex.value--;
    saveProgress(); // 添加保存进度
    playCurrentWord();
  }
};

// 播放下一个单词
const playNextWord = () => {
  // 停止当前播放
  isDictationPlaying.value = false;
  isAudioPlaying.value = false;
  audioPlayer.stop();
  if (playTimer.value) {
    clearTimeout(playTimer.value);
    playTimer.value = null;
  }
  currentPlayCount.value = 0;

  if (currentIndex.value < totalWords.value - 1) {
    currentIndex.value++;
    saveProgress(); // 添加保存进度
    playCurrentWord();
  } else {
    // 如果是最后一个单词，显示完成弹窗
    clearProgress(); // 清除进度
    completePopup.value.open();
  }
};

// 处理 swiper 切换
const handleSwiperChange = (e: any) => {
  currentIndex.value = e.detail.current;
  // 停止当前播放
  isDictationPlaying.value = false;
  isAudioPlaying.value = false;
  audioPlayer.stop();
  if (playTimer.value) {
    clearTimeout(playTimer.value);
    playTimer.value = null;
  }
  currentPlayCount.value = 0;
  saveProgress(); // 添加保存进度
  // 自动开始新的默写
  playCurrentWord();
};

// 添加准备完成处理方法
const handleReady = () => {
  if (loadProgress()) {
    continuePopup.value.open();
  } else {
    isReady.value = true;
    // 显示听写提示页面
    showTips.value = true;
  }
};

// 处理听写提示关闭
const handleTipsClose = () => {
  showTips.value = false;
  nextTick(() => {
    playCurrentWord();
  });
};

// 保存进度
const saveProgress = () => {
  const progress = {
    exerciseId: exerciseId.value,
    currentIndex: currentIndex.value,
  };
  uni.setStorageSync(
    `dictationProgress_${exerciseId.value}`,
    JSON.stringify(progress)
  );
};

// 加载进度
const loadProgress = () => {
  try {
    const savedData = uni.getStorageSync(
      `dictationProgress_${exerciseId.value}`
    );
    if (savedData) {
      const progress = JSON.parse(savedData);
      savedProgress.value = progress;
      return true;
    }
  } catch (error) {
    console.error("加载进度失败:", error);
  }
  return false;
};

// 清除进度
const clearProgress = () => {
  uni.removeStorageSync(`dictationProgress_${exerciseId.value}`);
};

// 处理继续听写
const handleContinue = () => {
  continuePopup.value.close();
  currentIndex.value = savedProgress.value.currentIndex;
  isReady.value = true;
  // 不再显示提示页面
  showTips.value = false;
  nextTick(() => {
    playCurrentWord();
  });
};

// 处理重新开始
const handleRestart = () => {
  continuePopup.value.close();
  clearProgress();
  currentIndex.value = 0;
  isReady.value = true;
  // 显示提示页面
  showTips.value = true;
};

// 显示中文提示
const showHint = () => {
  uni.showToast({
    title: words.value[currentIndex.value].chineseMeaning,
    icon: "none",
    duration: 2000,
  });
};

// 添加处理批改结果的方法
const handleGradeResult = (result: any) => {
  // 使用 storage 存储结果
  uni.setStorageSync("dictation_result", result);

  // 发送练习完成事件，包含成绩信息
  const exerciseData = {
    score: result.score || 0,
    totalWords: result.totalWords || 0,
    correctWords: result.correctWords || 0,
    incorrectWords: result.totalWords - result.correctWords || 0,
    exerciseType: "word-dictation",
  };

  // 发送练习完成事件
  uni.$emit("exerciseCompleted", exerciseData);

  // 关闭当前页面，跳转到结果页面，并标记来源为听写页面
  uni.redirectTo({
    url: `/sub-exercise/word-dictation/word-dictation-result?id=${exerciseId.value}&fromDictation=true`,
  });
};

// 添加返回处理方法
const handleBack = () => {
  uni.navigateBack();
};

// 页面加载
onLoad((options: LoadOptions) => {
  if (options.id) {
    exerciseId.value = Number(options.id);
  }

  // 检查是否是重拍模式
  if (options.isRetake === "true") {
    isRetake.value = true;
  }

  // 检查是否是从结果页返回
  if (options.fromResult === "true") {
    fromResult.value = true;
  }

  loadSettings(); // 加载配置
  fetchExerciseDetail();
});

// 页面卸载时清除定时器和音频上下文
onUnmounted(() => {
  if (startPlayTimer.value) {
    clearTimeout(startPlayTimer.value);
  }
  if (playTimer.value) {
    clearTimeout(playTimer.value);
  }
  audioPlayer.dispose();
});
</script>

<style lang="scss" scoped>
// 添加在样式文件最前面，作为全局按钮重置样式
button {
  margin: 0;
  padding: 0;
  border: 0 none;
  background: none;
  outline: none;
  position: relative;
  overflow: visible;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: none;
    transform: none;
    border-radius: inherit;
    pointer-events: none;
  }
}

.dictation-container {
  flex: 1;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.dictation-swiper {
  width: 100vw;
  height: 800rpx;
  display: block;
  transition: all 0.3s ease;

  // 平板设备适配
  @media screen and (min-width: 768px) {
    height: 600rpx;
  }
}

.swiper-item {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.dictation-card {
  position: relative;
  width: calc(100vw - 160rpx);
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1rpx solid #eceef2;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  min-height: 680rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transform: scale(0.85);
  opacity: 0.6;
  transition: all 0.3s ease;

  // 平板设备适配
  @media screen and (min-width: 768px) {
    min-height: 480rpx;
  }

  &.active {
    transform: scale(1);
    opacity: 1;
    border: 2rpx solid #e5e7eb;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  }

  .audio-wave {
    display: flex;
    flex-direction: column;
    align-items: center;

    .wave-container {
      display: flex;
      align-items: center;
      gap: 8rpx;
      height: 240rpx;
      margin-bottom: 24rpx;

      .wave-bar {
        width: 8rpx;
        height: 80rpx;
        background-color: #ffd600;
        border-radius: 4rpx;
        transform-origin: center;

        &.is-playing {
          animation: wave 0.5s ease-in-out infinite;
          animation-play-state: running;
        }

        &:not(.is-playing) {
          animation-play-state: paused;
        }
      }
    }

    .tip-text {
      font-size: 24rpx;
      color: #999;
    }
  }

  .settings-btn {
    position: absolute;
    bottom: 32rpx;
    right: 32rpx;
    width: 64rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f8f8;
    border-radius: 16rpx;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      background-color: #f0f0f0;
    }

    .iconfont {
      font-size: 36rpx;
      color: #666;
    }
  }

  .hint-btn {
    position: absolute;
    bottom: 32rpx;
    left: 32rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f8f8;
    border-radius: 16rpx;
    padding: 0 24rpx;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      background-color: #f0f0f0;
    }

    .iconfont {
      font-size: 36rpx;
      color: #666;
      margin-right: 8rpx;
    }

    .hint-text {
      font-size: 26rpx;
      color: #666;
    }
  }

  // 平板设备适配
  @media screen and (min-width: 768px) {
    .hint-btn {
      bottom: 24rpx;
      left: 24rpx;
      height: 56rpx;
      padding: 0 20rpx;

      .iconfont {
        font-size: 32rpx;
        margin-right: 6rpx;
      }

      .hint-text {
        font-size: 24rpx;
      }
    }
  }
}

.audio-controls {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 96rpx;
  padding: 0 32rpx;
  margin-top: 32rpx;
  margin-bottom: 32rpx;

  .control-btn {
    display: flex;
    flex-direction: column;
    align-items: center;

    .iconfont {
      font-size: 96rpx;
      height: 96rpx;
      width: 96rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #d1d5db;
    }

    .btn-text {
      font-size: 24rpx;
      color: #9ca3af;
      margin-top: 8rpx;
    }

    &.prev,
    &.next {
      .iconfont {
        font-size: 64rpx;
        border-radius: 50%;
      }
    }

    &.play {
      .iconfont {
        color: #ffd600;
      }
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

@keyframes wave {
  0%,
  100% {
    height: 40rpx;
  }
  50% {
    height: 160rpx;
  }
}

.settings-popup {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  box-sizing: border-box;

  .settings-title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 32rpx;
    font-weight: 500;
  }

  .play-times,
  .pause-times {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 48rpx;
    padding: 0 12rpx;
    box-sizing: border-box;

    .time-item {
      width: 112rpx;
      height: 76rpx;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      color: #666;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      &.active {
        background-color: rgba(255, 214, 0, 0.1);
        color: #333;
        border: 2rpx solid #ffd600;
        font-weight: 500;
      }
    }
  }

  .settings-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;
    margin-top: 48rpx;
    padding: 0 12rpx;
    box-sizing: border-box;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 84rpx;
      border-radius: 12rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30rpx;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: #666;

      &:active {
        background-color: #ebebeb;
      }
    }

    .confirm-btn {
      background-color: #ffd600;
      color: #333;
      font-weight: 500;

      &:active {
        opacity: 0.9;
      }
    }
  }
}

.continue-popup {
  width: 560rpx;
  background-color: #fff;
  border-radius: 32rpx;
  padding: 32rpx;
  overflow: hidden;

  .continue-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 24rpx;
  }

  .continue-content {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 48rpx;
  }

  .continue-actions {
    display: flex;
    justify-content: space-between;
    gap: 24rpx;
    margin: 0 12rpx;

    .restart-btn,
    .continue-btn {
      flex: 1;
      height: 72rpx;
      border-radius: 36rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        opacity: 0.9;
      }
    }

    .restart-btn {
      background-color: #f5f5f5;
      color: #666;
    }

    .continue-btn {
      background-color: #ffd600;
      color: #333;
      font-weight: 500;
    }
  }
}

// 添加 left-txt 样式
.left-txt {
  font-size: 16px;
  color: #333;
  padding: 3px 15px;
  border: 1px solid #333;
  border-radius: 25px;
}
</style>
