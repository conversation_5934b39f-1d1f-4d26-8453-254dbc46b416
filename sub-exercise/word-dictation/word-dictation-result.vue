<template>
  <commonBox
    :loading="loading"
    title="听写结果"
    :use-default-back="false"
    @back="handleBack"
  >
    <template v-if="!loading">
      <view class="result-container">
        <!-- 表情图标展示 -->
        <view class="emotion-section">
          <image
            :src="emotionImageSrc"
            class="emotion-image"
            @tap="playResultAudio"
          ></image>
        </view>

        <!-- 统计数据 -->
        <view class="stats-section">
          <view class="stats-header">
            <text class="header-item">听写单词</text>
            <text class="header-item">正确字词</text>
            <text class="header-item">存疑字词</text>
          </view>
          <view class="stats-content">
            <view class="stats-item">
              <text class="stats-value">{{ totalWords }}个</text>
            </view>
            <view class="stats-item">
              <text class="stats-value">{{ correctWords }}个</text>
            </view>
            <view class="stats-item">
              <text class="stats-value">{{ pendingWords }}个</text>
            </view>
          </view>
        </view>

        <!-- 听写回顾标题 -->
        <view class="review-title">听写回顾</view>

        <!-- 单词列表 -->
        <view class="word-list">
          <view class="word-item" v-for="(word, index) in words" :key="index">
            <view class="word-info">
              <view class="word-header">
                <text class="word-text">{{ word.text }}</text>
                <text class="word-type">{{ word.type }}</text>
                <text class="word-meaning">{{ word.meaning }}</text>
                <!-- 添加音频播放按钮 -->
                <view
                  class="sound-btn"
                  @tap="playAudio(word, word.text + index)"
                  v-if="word.audioUrl"
                >
                  <text
                    class="iconfont icon-sound"
                    :class="{
                      playing:
                        playingWordId === word.text + index && isAudioPlaying,
                    }"
                  ></text>
                </view>
              </view>
            </view>
            <view class="word-status" :class="word.status">
              <text v-if="word.status === 'pending'" class="status-text"
                >待巩固</text
              >
              <text v-else class="status-icon">👍</text>
            </view>
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="bottom-actions">
          <button class="check-image-btn" @click="openImagePopup">
            查看批改图
          </button>
          <button class="retake-btn" @click="handleRetake">重拍</button>
          <button
            class="redictate-btn"
            @click="handleRedictate"
            v-if="!fromDictation"
          >
            重新听写
          </button>
        </view>
      </view>
    </template>

    <!-- 批改图查看弹窗 -->
    <uni-popup ref="imagePopup" type="center">
      <view class="image-popup">
        <view class="popup-header">
          <text class="popup-title">批改明细</text>
          <text class="close-icon" @click="closeImagePopup">×</text>
        </view>
        <view class="image-container">
          <image
            :src="
              imageUrl ||
              'https://file.bpbl68.cn/test%2F99d3ab02a5b460c99ed35febfbb61c83c4e925de.png'
            "
            mode="aspectFit"
            class="correction-image"
          ></image>
        </view>
        <view class="popup-footer">
          <button class="correction-btn" @click="openCorrectionPopup">
            批改有误，点击更正
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 批改明细弹窗 -->
    <uni-popup ref="correctionPopup" type="center">
      <view class="correction-popup">
        <view class="popup-header">
          <text class="popup-title">批改明细</text>
          <text class="close-icon" @click="closeCorrectionPopup">×</text>
        </view>
        <view class="correction-content">
          <!-- 简单修正说明 -->
          <view class="correction-tip">
            <text class="tip-icon">💡</text>
            <text class="tip-text">点击单词项可以修改批改结果</text>
          </view>

          <view class="correction-list">
            <view
              class="correction-item"
              v-for="(word, index) in words"
              :key="index"
              @click="toggleWordCorrection(word, index)"
            >
              <view class="word-info">
                <text class="word-text">{{ word.text }}</text>
                <text class="word-type">{{ word.type }}</text>
                <text class="word-meaning">{{ word.meaning }}</text>
              </view>

              <view class="correction-status-wrapper">
                <view class="status-indicator" :class="word.status">
                  <text class="correct-icon" v-if="word.status === 'correct'"
                    >✓</text
                  >
                  <text class="pending-icon" v-else>✕</text>
                </view>
                <text class="status-text">{{
                  word.status === "correct" ? "正确" : "错误"
                }}</text>
              </view>

              <!-- 变更提示 -->
              <view class="change-indicator" v-if="isWordChanged(word, index)">
                <text class="change-text">已修改</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 统计信息 -->
        <view class="correction-stats">
          <view class="stat-item">
            <text class="stat-label">正确:</text>
            <text class="stat-value correct">{{ correctWords }}个</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">错误:</text>
            <text class="stat-value pending">{{ pendingWords }}个</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">修改后得分:</text>
            <text class="stat-value score">{{ updatedScore }}分</text>
          </view>
        </view>

        <view class="popup-footer">
          <button class="cancel-btn" @click="closeCorrectionPopup">取消</button>
          <button
            class="confirm-btn"
            @click="confirmCorrection"
            :disabled="!hasChanges"
          >
            {{ hasChanges ? "确定更正" : "暂无修改" }}
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 添加拍照上传弹窗组件 -->
    <CompletePopup
      ref="completePopup"
      :exercise-id="exerciseId"
      :words="words.map((w) => w.text)"
      @grade-result="handleGradeResult"
    />
  </commonBox>
</template>

<script lang="ts" setup>
import { getHomeworkExerciseDetail } from "@/api/homework-exercise";
import commonBox from "@/components/common-box/commonBox.vue";

import { correctDictation } from "@/api/homework-submission";
// @ts-ignore
import { AudioPlayer } from "@/sub-exercise/utils/audio-player";
// @ts-ignore
import { onLoad } from "@dcloudio/uni-app";
import { computed, onUnmounted, ref } from "vue";
import CompletePopup from "./components/complete-popup.vue";

interface DictationWordUI {
  text: string;
  type: string;
  meaning: string;
  status: "correct" | "pending";
  audioUrl?: string;
}

// 图片URL - 从听写结果中获取
const imageUrl = ref("");

// 弹窗引用
const imagePopup = ref();
const correctionPopup = ref();
const completePopup = ref();

// 当前练习ID
const exerciseId = ref<number>(0);
const ocrRecordId = ref<number>(0);
// 添加来源判断变量
const fromDictation = ref<boolean>(false);

// 单词数据
const words = ref<DictationWordUI[]>([]);

// 加载状态
const loading = ref(false);

// 修正前的原始数据，用于取消恢复
const originalWords = ref<DictationWordUI[]>([]);

// 计算属性
const score = ref(0);
const totalWords = computed(() => words.value.length);
const correctWords = computed(
  () => words.value.filter((w) => w.status === "correct").length
);
const pendingWords = computed(
  () => words.value.filter((w) => w.status === "pending").length
);

// 计算修改后的分数
const updatedScore = computed(() => {
  return Math.floor((correctWords.value / totalWords.value) * 100);
});

// 判断是否有变更
const hasChanges = computed(() => {
  if (!originalWords.value.length || !words.value.length) return false;

  for (let i = 0; i < words.value.length; i++) {
    if (words.value[i].status !== originalWords.value[i].status) {
      return true;
    }
  }
  return false;
});

// 检查单词状态是否已变更
const isWordChanged = (word: DictationWordUI, index: number) => {
  if (!originalWords.value[index]) return false;
  return word.status !== originalWords.value[index].status;
};

// 音频播放相关
const isAudioPlaying = ref(false);
const playingWordId = ref<string | null>(null);
// 创建音频播放器实例
const audioPlayer = new AudioPlayer();

// 计算表情图片路径和对应的音频
const emotionData = computed(() => {
  // 防止除以零
  if (totalWords.value === 0) {
    return {
      imageSrc: "/static/icons/nice.png",
      audioSrc: "/static/sounds/nice.mp3",
    };
  }

  const correctRate = correctWords.value / totalWords.value;
  if (correctRate === 1) {
    // 100% 正确率
    return {
      imageSrc: "/static/icons/excellent.png",
      audioSrc: "/static/sounds/excellent2.mp3",
    };
  } else if (correctRate >= 0.5) {
    // 50-90% 正确率
    return {
      imageSrc: "/static/icons/nice.png",
      audioSrc: "/static/sounds/nice.mp3",
    };
  } else {
    // 低于50% 正确率
    return {
      imageSrc: "/static/icons/keep-it-up.png",
      audioSrc: "/static/sounds/keep-it-up.mp3",
    };
  }
});

// 简化访问
const emotionImageSrc = computed(() => emotionData.value.imageSrc);
const emotionAudioSrc = computed(() => emotionData.value.audioSrc);

// 结果音频播放器
const resultAudioPlayer = new AudioPlayer();

// 播放结果音频
const playResultAudio = () => {
  if (emotionAudioSrc.value) {
    resultAudioPlayer.play(emotionAudioSrc.value, {
      onStart: () => {
        console.log("结果音频开始播放");
      },
      onEnd: () => {
        console.log("结果音频播放结束");
      },
      onError: () => {
        console.error("结果音频播放失败");
      },
    });
  }
};

// 处理页面加载
onLoad((options: any) => {
  if (!options.id) {
    uni.showToast({
      title: "练习ID不能为空",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
    return;
  }

  exerciseId.value = Number(options.id);

  // 判断是否是从听写页面直接进入
  if (options.fromDictation === "true") {
    fromDictation.value = true;
  }

  fetchExerciseDetail(exerciseId.value);
});

// 获取练习详情
const fetchExerciseDetail = async (id: number) => {
  try {
    loading.value = true;
    const res = await getHomeworkExerciseDetail(id);

    if (res.code === 0 && res.data) {
      console.log("获取到数据:", res.data);

      // 处理响应数据
      const exerciseData = res.data;

      // 获取单词数据
      let wordList: any[] = [];
      if (
        exerciseData.extraWordData?.questions &&
        Array.isArray(exerciseData.extraWordData.questions)
      ) {
        wordList = exerciseData.extraWordData.questions.map(
          (q: any) => q.words
        );
      }

      // 处理提交结果数据
      if (exerciseData.extSubmissionDetail) {
        const submissionDetail = exerciseData.extSubmissionDetail;

        // 检查是否有批改数据
        // 使用 any 类型绕过类型检查
        const wordEvalResult = submissionDetail.wordEvaluationResult as any;

        const dictResult = wordEvalResult.dictationResult;

        // 设置图片URL (优先使用已批改的图片)
        imageUrl.value =
          dictResult.correctedImageUrl || dictResult.originalImageUrl || "";

        // 设置 ocrRecordId
        ocrRecordId.value = dictResult.ocrRecordId;

        // 从 wordScore 获取分数信息
        const wordScore = dictResult.wordScore || {
          totalCount: wordList.length,
          correctCount: 0,
          wrongCount: 0,
          score: 0,
        };

        // 设置分数 - 直接使用 wordScore.score
        score.value = wordScore.score || 0;

        // 处理单词批改结果
        if (
          dictResult.correctedResults &&
          Array.isArray(dictResult.correctedResults)
        ) {
          // 创建单词UI数据
          const wordUIList: DictationWordUI[] = [];

          for (const item of dictResult.correctedResults) {
            // 找到对应的单词详情
            const wordDetail = wordList.find((w: any) => w.id === item.wordId);

            // 根据 matchType 判断状态
            // NO_MATCH - 错误，其他类型（MATCH、FUZZY_MATCH 等）为正确
            const isCorrect = item.matchType !== "NO_MATCH";

            wordUIList.push({
              text: wordDetail?.word || item.answer,
              type: wordDetail?.partOfSpeech || "n.",
              meaning: wordDetail?.chineseMeaning || "",
              status: isCorrect ? "correct" : "pending",
              audioUrl: wordDetail?.audioUrl,
            });
          }

          words.value = wordUIList;
        }

        // 数据加载完成后播放音频
        setTimeout(() => {
          playResultAudio();
        }, 500); // 延迟500ms播放，确保页面渲染完成
      } else {
        throw new Error("没有找到听写结果数据");
      }
    } else {
      throw new Error(res.msg || "获取练习详情失败");
    }
  } catch (error) {
    console.error("获取练习详情失败:", error);
    uni.showToast({
      title: typeof error === "string" ? error : "获取数据失败",
      icon: "none",
      duration: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 打开图片查看弹窗
const openImagePopup = () => {
  imagePopup.value.open();
};

// 关闭图片查看弹窗
const closeImagePopup = () => {
  imagePopup.value.close();
};

// 打开批改明细弹窗
const openCorrectionPopup = () => {
  // 备份原始数据，以便取消时恢复
  originalWords.value = JSON.parse(JSON.stringify(words.value));
  closeImagePopup();
  correctionPopup.value.open();
};

// 关闭批改明细弹窗
const closeCorrectionPopup = () => {
  // 恢复原始数据
  words.value = JSON.parse(JSON.stringify(originalWords.value));
  correctionPopup.value.close();
};

// 切换单词的批改状态
const toggleWordCorrection = (word: DictationWordUI, index: number) => {
  // 切换状态，仅在这两个状态间切换
  words.value[index].status = word.status === "correct" ? "pending" : "correct";
};

// 确认更正
const confirmCorrection = async () => {
  if (!hasChanges.value) {
    uni.showToast({
      title: "未做任何修改",
      icon: "none",
    });
    return;
  }

  try {
    uni.showLoading({
      title: "更新中...",
      mask: true,
    });

    // 构建更新数据 - 只包含已更改的单词
    const corrections = words.value
      .map((word, index) => {
        // 只有当当前单词的状态与原始状态不同时，才返回这个单词的数据
        if (word.status !== originalWords.value[index].status) {
          return {
            word: word.text,
            answer: word.text,
            correct: word.status === "correct",
          };
        }
        return null; // 状态未改变的单词返回null
      })
      .filter((item) => item !== null); // 过滤掉所有null值，只保留修改过的单词

    // 调用 API 更新批改结果
    const res = await correctDictation({
      id: ocrRecordId.value,
      corrections: corrections,
    });

    if (res.code !== 0) {
      throw new Error(res.msg || "更新批改结果失败");
    }

    uni.hideLoading();
    uni.showToast({
      title: "批改已更新",
      icon: "success",
      duration: 2000,
    });

    // 关闭弹窗
    closeCorrectionPopup();

    // 重新获取最新的练习结果
    setTimeout(() => {
      fetchExerciseDetail(exerciseId.value);
    }, 500); // 延迟500ms，确保服务器有足够时间处理数据
  } catch (error) {
    uni.hideLoading();
    console.error("更新批改结果失败:", error);
    uni.showToast({
      title: typeof error === "string" ? error : "更新失败，请重试",
      icon: "none",
      duration: 3000,
    });
  }
};

// 处理听写批改结果
const handleGradeResult = async (result: any) => {
  try {
    // 显示加载中
    uni.showLoading({
      title: "加载中...",
      mask: true,
    });

    // 立即从服务器获取最新数据
    await fetchExerciseDetail(exerciseId.value);

    // 显示成功提示
    uni.showToast({
      title: "重新批改成功",
      icon: "success",
      duration: 2000,
    });
  } catch (error) {
    console.error("获取最新数据失败:", error);
    uni.showToast({
      title: "获取最新数据失败，请稍后重试",
      icon: "none",
      duration: 3000,
    });
  } finally {
    uni.hideLoading();
  }
};

// 处理重拍功能 - 直接在当前页面弹出拍照弹窗
const handleRetake = () => {
  // 打开拍照弹窗
  completePopup.value.open();
};

// 添加重新听写方法
const handleRedictate = () => {
  // 跳转到听写页面，并传递参数表明是从结果页重新听写
  uni.redirectTo({
    url: `/sub-exercise/word-dictation/word-dictation?id=${exerciseId.value}&fromResult=true`,
  });
};

const handleBack = () => {
  uni.navigateBack();
};

// 修改音频播放函数
const playAudio = (word: any, id: string) => {
  if (!word.audioUrl) {
    uni.showToast({
      title: "音频不存在",
      icon: "none",
    });
    return;
  }

  // 如果当前有音频在播放，且点击的是同一个音频，则停止播放
  if (playingWordId.value === id && isAudioPlaying.value) {
    audioPlayer.stop();
    isAudioPlaying.value = false;
    playingWordId.value = null;
    return;
  }

  // 播放音频
  isAudioPlaying.value = true;
  playingWordId.value = id;

  audioPlayer.play(word.audioUrl, {
    onStart: () => {
      console.log("音频开始播放");
    },
    onEnd: () => {
      isAudioPlaying.value = false;
      playingWordId.value = null;
    },
    onError: () => {
      isAudioPlaying.value = false;
      playingWordId.value = null;
      uni.showToast({
        title: "音频播放失败",
        icon: "none",
      });
    },
  });
};

// 组件卸载时清理
onUnmounted(() => {
  audioPlayer.dispose();
  resultAudioPlayer.dispose(); // 清理结果音频播放器
});
</script>

<style lang="scss" scoped>
.result-container {
  padding: 32rpx;
  padding-bottom: 120rpx;

  .emotion-section {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40rpx 0;

    .emotion-image {
      width: 300rpx;
      height: 300rpx;
      transition: all 0.2s;

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .stats-section {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 1);
    overflow: hidden;
    box-shadow: 0.1px 0.1px 3px 1.5px rgba(0, 0, 0, 0.1);
    opacity: 1;
    border-radius: 10px;

    .stats-header {
      display: flex;
      background-color: #f0f0f0;
      width: 100%;

      .header-item {
        flex: 1;
        text-align: center;
        padding: 10px 0;
        font-size: 28rpx;
        color: rgba(105, 105, 105, 1);
      }
    }

    .stats-content {
      display: flex;
      width: 100%;

      .stats-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 24rpx 0;
        position: relative;

        .stats-value {
          font-size: 36rpx;
          color: rgba(0, 0, 0, 1);
        }
      }
    }
  }

  .review-title {
    font-size: 36rpx;
    color: #333333;
    font-weight: 600;
    margin: 48rpx 0 24rpx;
    text-align: center;
    width: 100%;
  }

  .word-list {
    background: rgba(255, 255, 255, 1);
    border-radius: 24rpx;
    padding: 0 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
    margin-bottom: 88rpx;

    .word-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 2rpx solid rgba(229, 229, 229, 1);

      &:last-child {
        border-bottom: none;
      }

      .word-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8rpx;
        position: relative;

        .word-header {
          display: flex;
          align-items: center;
          gap: 12rpx;
        }

        .word-text {
          font-size: 36rpx;
          color: #333333;
          font-weight: 500;
        }

        .word-type {
          font-size: 28rpx;
          color: #999999;
          margin-top: 4rpx;
        }

        .word-meaning {
          font-size: 32rpx;
          color: #666666;
          margin-top: 4rpx;
        }

        // 添加音频播放按钮
        .sound-btn {
          width: 60rpx;
          height: 60rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: rgba(255, 230, 81, 0.15);
          border-radius: 30rpx;
          margin-right: auto;

          .iconfont {
            font-size: 40rpx;
            color: #d0761c;
            opacity: 0.99;
            transition: all 0.3s ease;
          }

          .playing {
            animation: pulse 1s ease-in-out infinite;
          }
        }
      }

      .word-status {
        margin-left: 20rpx;

        &.pending {
          .status-text {
            font-size: 24rpx;
            color: #b8860b;
            background-color: #f5deb3;
            padding: 8rpx 20rpx;
            border-radius: 20rpx;
          }
        }

        .status-icon {
          font-size: 36rpx;
        }
      }
    }
  }
}

// 批改图查看弹窗样式
.image-popup {
  width: 650rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;

  .popup-title {
    font-size: 34rpx;
    font-weight: bold;
    color: #333;
  }

  .close-icon {
    font-size: 40rpx;
    color: #999;
    line-height: 1;
  }
}

.image-container {
  padding: 20rpx;
  height: 800rpx;

  .correction-image {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
  }
}

.popup-footer {
  padding: 20rpx 30rpx 40rpx;

  .correction-btn {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background: linear-gradient(to right, #ffe251, #ffcc00);
    color: #333;
    font-size: 30rpx;
    font-weight: 500;
    border-radius: 40rpx;

    &:active {
      opacity: 0.8;
    }
  }
}

// 批改明细弹窗样式
.correction-popup {
  width: 650rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .correction-content {
    max-height: 700rpx;
    overflow-y: auto;
    padding: 0 30rpx;

    .correction-tip {
      display: flex;
      align-items: center;
      background-color: #f8f8f8;
      padding: 16rpx 20rpx;
      border-radius: 12rpx;
      margin: 20rpx 0;

      .tip-icon {
        font-size: 32rpx;
        margin-right: 12rpx;
      }

      .tip-text {
        font-size: 26rpx;
        color: #666;
      }
    }

    .correction-list {
      .correction-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx 0;
        border-bottom: 1rpx solid #eee;
        position: relative;

        &:active {
          background-color: #f9f9f9;
        }

        &:last-child {
          border-bottom: none;
        }

        .word-info {
          display: flex;
          align-items: center;
          flex: 1;
          overflow: hidden;

          .word-text {
            font-size: 32rpx;
            color: #333;
            font-weight: 500;
            margin-right: 16rpx;
            white-space: nowrap;
          }

          .word-type {
            font-size: 26rpx;
            color: #999;
            margin-right: 16rpx;
            white-space: nowrap;
          }

          .word-meaning {
            font-size: 28rpx;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .correction-status-wrapper {
          display: flex;
          align-items: center;
          margin-left: 20rpx;

          .status-indicator {
            width: 44rpx;
            height: 44rpx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12rpx;

            &.correct {
              background-color: #e8f7e9;

              .correct-icon {
                color: #4caf50;
                font-size: 28rpx;
                font-weight: bold;
              }
            }

            &.pending {
              background-color: #ffefef;

              .pending-icon {
                color: #f44336;
                font-size: 28rpx;
                font-weight: bold;
              }
            }
          }

          .status-text {
            font-size: 26rpx;
            color: #999;
          }
        }

        .change-indicator {
          position: absolute;
          top: 6rpx;
          right: 6rpx;
          background: linear-gradient(to right, #ffaa00, #ffcc00);
          border-radius: 20rpx;
          padding: 4rpx 10rpx;

          .change-text {
            font-size: 20rpx;
            color: #fff;
          }
        }
      }
    }
  }

  .correction-stats {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    background-color: #f8f8f8;
    border-top: 1rpx solid #eee;

    .stat-item {
      display: flex;
      align-items: center;

      .stat-label {
        font-size: 26rpx;
        color: #666;
        margin-right: 6rpx;
      }

      .stat-value {
        font-size: 28rpx;
        font-weight: 500;

        &.correct {
          color: #4caf50;
        }

        &.pending {
          color: #f44336;
        }

        &.score {
          color: #ff9642;
        }
      }
    }
  }

  .popup-footer {
    display: flex;
    padding: 20rpx 30rpx 40rpx;
    gap: 20rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 40rpx;
      font-size: 30rpx;
    }

    .cancel-btn {
      background: #f5f5f5;
      color: #666;
    }

    .confirm-btn {
      background: linear-gradient(to right, #ffe251, #ffcc00);
      color: #333;
      font-weight: 500;

      &:disabled {
        background: #f0f0f0;
        color: #999;
        cursor: not-allowed;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 40rpx 32rpx;
  gap: 16rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #eceef2;

  button {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .check-image-btn {
    background-color: #ffe251;
    color: #333333;
    font-weight: 500;
  }

  .redictate-btn {
    background-color: #f5f5f5;
    color: #666666;
  }

  .retake-btn {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1rpx solid #91d5ff;
  }
}

// 平板设备适配
@media screen and (min-width: 768px) {
  .image-popup {
    width: 650px;
  }
  .image-container {
    height: 580rpx;
  }
}

// 添加动画效果
@keyframes pulse {
  0% {
    opacity: 0.99;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.99;
    transform: scale(1);
  }
}
</style>
