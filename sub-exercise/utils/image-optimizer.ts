/**
 * 图片优化工具类
 * 用于优化图片加载和流量使用
 */

/**
 * 缩放图片URL
 * 如果图片URL是file.bpbl68.cn开头的，则自动追加imageMogr2/thumbnail/320x参数
 * @param url 原始图片URL
 * @param width 缩放宽度，默认320
 * @returns 处理后的图片URL
 */
export function optimizeImageUrl(url: string, width: number = 320): string {
  if (!url) return url;

  // 检查URL是否以file.bpbl68.cn开头
  if (url.indexOf("file.bpbl68.cn") > -1) {
    // 检查URL是否已经包含参数
    const separator = url.includes("?") ? "&" : "?";
    // 添加缩放参数
    return `${url}${separator}imageMogr2/thumbnail/${width}x`;
  }

  // 如果不是目标域名的图片，则返回原始URL
  return url;
}

/**
 * 批量处理图片URL数组
 * @param urls 图片URL数组
 * @param width 缩放宽度，默认320
 * @returns 处理后的图片URL数组
 */
export function optimizeImageUrls(
  urls: string[],
  width: number = 320
): string[] {
  if (!urls || !Array.isArray(urls)) return urls;

  return urls.map((url) => optimizeImageUrl(url, width));
}

/**
 * 处理WordInfo对象中的图片URL
 * @param wordInfo 单词信息对象
 * @param width 缩放宽度，默认320
 * @returns 处理后的单词信息对象
 */
export function optimizeWordInfoImages(
  wordInfo: any,
  width: number = 320
): any {
  if (!wordInfo) return wordInfo;

  // 创建一个新对象，避免修改原始对象
  const optimizedWordInfo = { ...wordInfo };

  // 处理单词图片
  if (optimizedWordInfo.imageUrl) {
    optimizedWordInfo.imageUrl = optimizeImageUrl(
      optimizedWordInfo.imageUrl,
      width
    );
  }

  // 处理图片选项
  if (
    optimizedWordInfo.imageOptions &&
    Array.isArray(optimizedWordInfo.imageOptions)
  ) {
    optimizedWordInfo.imageOptions = optimizeImageUrls(
      optimizedWordInfo.imageOptions,
      width
    );
  }

  // 处理例句中的图片
  if (
    optimizedWordInfo.exampleSentences &&
    Array.isArray(optimizedWordInfo.exampleSentences)
  ) {
    optimizedWordInfo.exampleSentences = optimizedWordInfo.exampleSentences.map(
      (sentence: any) => {
        if (sentence && sentence.imageUrl) {
          return {
            ...sentence,
            imageUrl: optimizeImageUrl(sentence.imageUrl, width),
          };
        }
        return sentence;
      }
    );
  }

  return optimizedWordInfo;
}

export default {
  optimizeImageUrl,
  optimizeImageUrls,
  optimizeWordInfoImages,
};
