/**
 * 音频播放器工具
 * 统一处理音频播放逻辑，确保在设备静音状态下也能播放音频
 */
import { uploadObjectLog } from "@/utils/log/uploader";

// 添加一个标志位，用于记录是否已经初始化了音频会话
let audioSessionInitialized = false;

/**
 * 初始化音频会话，解决 iOS 静音开关问题
 * 这个函数应该在应用启动时调用一次
 */
export const initAudioSession = (): void => {
  if (audioSessionInitialized) return;

  // 检测平台
  const systemInfo = uni.getSystemInfoSync();
  const isIOS =
    systemInfo.platform === "ios" || /ios/i.test(systemInfo.platform);

  if (isIOS) {
    try {
      // 使用系统音效API（如果在小程序环境中可用）
      if (typeof uni.setInnerAudioOption === "function") {
        uni.setInnerAudioOption({
          obeyMuteSwitch: false,
          success: () => {
            console.log("设置全局音频选项成功");
            audioSessionInitialized = true;
          },
          fail: (err) => {
            console.error("设置全局音频选项失败:", err);
          },
        });
      } else {
        audioSessionInitialized = true;
      }
    } catch (error) {
      console.error("初始化音频会话失败:", error);
    }
  } else {
    // 非iOS平台，直接标记为已初始化
    audioSessionInitialized = true;
  }
};

/**
 * 创建具有统一配置的音频上下文
 * @returns 配置好的音频上下文
 */
export const createAudioContext = (): UniApp.InnerAudioContext => {
  // 确保音频会话已初始化
  if (!audioSessionInitialized) {
    initAudioSession();
  }

  const audioContext = uni.createInnerAudioContext();
  // 关键设置：确保在设备静音状态下也能播放音频
  audioContext.obeyMuteSwitch = false;
  audioContext.autoplay = false;

  return audioContext;
};

/**
 * 播放音频
 * @param audioUrl 音频URL
 * @param options 播放选项
 * @returns 音频上下文对象
 */
export const playAudio = (
  audioUrl: string,
  options: {
    onStart?: () => void;
    onEnd?: () => void;
    onError?: (error: any) => void;
    volume?: number; // 0-1
  } = {}
): UniApp.InnerAudioContext | null => {
  if (!audioUrl) {
    console.error("音频URL不能为空");
    options.onError?.({ errMsg: "音频URL不能为空" });
    return null;
  }

  try {
    const audioContext = createAudioContext();
    audioContext.src = audioUrl;

    // 设置音量（如果提供）
    if (typeof options.volume === "number") {
      audioContext.volume = Math.max(0, Math.min(1, options.volume));
    }

    // 绑定事件
    if (options.onStart) {
      audioContext.onPlay(() => {
        options.onStart?.();
      });
    }

    if (options.onEnd) {
      audioContext.onEnded(() => {
        options.onEnd?.();
      });
    }

    if (options.onError) {
      audioContext.onError((error) => {
        console.error(
          "音频播放错误:",
          error,
          "音频路径:",
          audioUrl,
          "错误代码:",
          error.errCode,
          "错误信息:",
          error.errMsg
        );
        // 使用新方法上报错误对象
        uploadObjectLog("基础音频播放错误", {
          ...error,
          audioUrl,
          volume: options.volume,
        });
        options.onError?.(error);
      });
    }

    // 播放音频
    audioContext.play();
    return audioContext;
  } catch (error: any) {
    uploadObjectLog("创建音频上下文失败", {
      ...error,
    });
    console.error("创建音频上下文失败:", error);
    options.onError?.(error);
    return null;
  }
};

/**
 * 停止并销毁音频上下文
 * @param audioContext 音频上下文
 */
export const disposeAudio = (
  audioContext: UniApp.InnerAudioContext | null
): void => {
  if (!audioContext) return;

  try {
    audioContext.stop();
    audioContext.destroy();
  } catch (error: any) {
    uploadObjectLog("销毁音频上下文失败", {
      ...error,
    });
    console.error("销毁音频上下文失败:", error);
  }
};

/**
 * 音频播放管理类
 * 用于管理单个音频实例，避免重复创建
 */
export class AudioPlayer {
  private audioContext: UniApp.InnerAudioContext | null = null;
  private isPlaying: boolean = false;
  private isIOS: boolean;
  // 添加用于存储当前音频URL和播放选项的属性
  private currentAudioUrl: string = "";
  private currentOptions: any = {};
  // 添加网络状态监听器
  private networkListener: any = null;
  // 添加标记表示是否在等待网络恢复
  private waitingForNetwork: boolean = false;

  constructor() {
    // 检测平台
    const systemInfo = uni.getSystemInfoSync();
    this.isIOS =
      systemInfo.platform === "ios" || /ios/i.test(systemInfo.platform);

    // 确保音频会话已初始化
    if (!audioSessionInitialized) {
      initAudioSession();
    }

    // 初始化网络状态监听
    this.setupNetworkListener();
  }

  /**
   * 设置网络状态监听
   * 在网络恢复时尝试重新播放
   */
  private setupNetworkListener(): void {
    // 确保移除之前可能存在的监听器
    if (this.networkListener) {
      uni.offNetworkStatusChange(this.networkListener);
    }

    // 创建新的监听器
    this.networkListener = (res: any) => {
      console.log("网络状态变化:", res);
      // 如果网络恢复连接且有等待重试的音频
      if (res.isConnected && this.waitingForNetwork && this.currentAudioUrl) {
        console.log("网络已恢复，尝试重新播放:", this.currentAudioUrl);
        this.waitingForNetwork = false;
        // 延迟一点时间再重试，确保网络稳定
        setTimeout(() => {
          this.retryPlay();
        }, 1000);
      }
    };

    // 添加网络状态变化监听
    uni.onNetworkStatusChange(this.networkListener);
  }

  /**
   * 重试播放当前音频
   */
  private retryPlay(): void {
    if (!this.currentAudioUrl) return;

    // 由于要重新创建音频上下文，需要保存当前URL和选项
    const audioUrl = this.currentAudioUrl;
    const options = { ...this.currentOptions };

    // 完全销毁并重新创建音频上下文
    if (this.audioContext) {
      disposeAudio(this.audioContext);
      this.audioContext = null;
    }

    // 重新调用播放方法
    console.log("重试播放音频:", audioUrl);
    this.play(audioUrl, options);
  }

  /**
   * 播放音频
   * @param audioUrl 音频URL
   * @param options 播放选项
   * @returns 是否成功开始播放
   */
  play(
    audioUrl: string,
    options: {
      onStart?: () => void;
      onEnd?: () => void;
      onError?: (error: any) => void;
      volume?: number; // 0-1
    } = {}
  ): boolean {
    // 保存当前的URL和选项，用于可能的重试
    this.currentAudioUrl = audioUrl;
    this.currentOptions = { ...options };

    // 如果当前有音频在播放，先停止它
    if (this.isPlaying && this.audioContext) {
      this.audioContext.stop();
      this.isPlaying = false;
    }

    if (!audioUrl) {
      console.error("音频URL不能为空");
      options.onError?.({ errMsg: "音频URL不能为空" });
      return false;
    }

    // 添加网络检查
    uni.getNetworkType({
      success: (res) => {
        if (res.networkType === "none") {
          this.waitingForNetwork = true;
          console.log("当前无网络连接，等待网络恢复后自动重试");
          options.onError?.({
            errMsg: "network unavailable",
            customMsg: "网络不可用，恢复连接后将自动重试",
          });
        }
      },
    });

    try {
      // 复用现有的音频上下文或创建新的
      if (!this.audioContext) {
        this.audioContext = createAudioContext();
      } else {
        // 清除之前的所有事件监听
        this.audioContext.offCanplay(null as any);
        this.audioContext.offPlay(null as any);
        this.audioContext.offEnded(null as any);
        this.audioContext.offStop(null as any);
        this.audioContext.offError(null as any);
      }

      // 设置新的音频源
      this.audioContext.src = audioUrl;

      // 设置音量（如果提供）
      if (typeof options.volume === "number") {
        this.audioContext.volume = Math.max(0, Math.min(1, options.volume));
      }

      // 添加 canplay 事件监听，确保音频已经准备好可以播放
      this.audioContext.onCanplay(() => {
        console.log("音频已准备好播放:", audioUrl);
        // 重置网络等待状态
        this.waitingForNetwork = false;
      });

      // 绑定事件
      this.audioContext.onPlay(() => {
        console.log("音频开始播放:", audioUrl);
        this.isPlaying = true;
        options.onStart?.();
      });

      this.audioContext.onEnded(() => {
        console.log("音频播放结束:", audioUrl);
        this.isPlaying = false;
        options.onEnd?.();
      });

      this.audioContext.onStop(() => {
        console.log("音频播放停止:", audioUrl);
        this.isPlaying = false;
      });

      this.audioContext.onError((error) => {
        this.isPlaying = false;
        console.error(
          "音频播放错误:",
          error,
          "音频路径:",
          audioUrl,
          "错误代码:",
          error.errCode,
          "错误信息:",
          error.errMsg
        );

        // 使用新方法上报错误对象
        uploadObjectLog("AudioPlayer类音频播放错误", {
          ...error,
          audioUrl,
          isPlaying: this.isPlaying,
          isIOS: this.isIOS,
          waitingForNetwork: this.waitingForNetwork,
          options: this.currentOptions,
        });

        // 增强错误处理，区分网络错误和其他类型错误
        let errorMsg = "音频播放失败";
        let isNetworkError = false;

        // 检查错误代码，区分网络错误
        if (
          error.errCode === 10002 ||
          error.errMsg?.includes("network") ||
          error.errMsg?.includes("request:fail")
        ) {
          errorMsg = "网络不佳，音频加载失败";
          isNetworkError = true;
          // 标记等待网络恢复
          this.waitingForNetwork = true;
        } else if (error.errCode === 10001) {
          errorMsg = "音频格式不支持";
        }

        // 传递更详细的错误信息
        options.onError?.({ ...error, customMsg: errorMsg });

        // 如果是网络错误，检查当前网络状态
        if (isNetworkError) {
          uni.getNetworkType({
            success: (res) => {
              // 如果当前有网络，可能是暂时性错误，尝试立即重试一次
              if (res.networkType !== "none") {
                console.log("检测到网络可用，立即尝试重新播放");
                setTimeout(() => {
                  this.retryPlay();
                }, 1000);
              } else {
                console.log("无网络连接，等待网络恢复");
              }
            },
          });
        }
      });

      // 播放音频
      this.audioContext.play();

      return true;
    } catch (error: any) {
      // 捕获创建音频上下文时的错误
      uploadObjectLog("创建音频上下文失败", {
        ...error,
      });
      console.error("创建音频上下文失败:", error);
      options.onError?.(error);
      return false;
    }
  }

  /**
   * 停止播放当前音频
   */
  stop(): void {
    if (this.audioContext) {
      try {
        this.audioContext.stop();
        this.isPlaying = false;
      } catch (error) {
        console.error("停止音频播放失败:", error);
      }
    }
  }

  /**
   * 销毁音频播放器
   */
  dispose(): void {
    // 移除网络状态监听
    if (this.networkListener) {
      uni.offNetworkStatusChange(this.networkListener);
      this.networkListener = null;
    }

    if (this.audioContext) {
      disposeAudio(this.audioContext);
      this.audioContext = null;
      this.isPlaying = false;
    }

    // 清除保存的音频信息
    this.currentAudioUrl = "";
    this.currentOptions = {};
    this.waitingForNetwork = false;
  }

  /**
   * 获取当前是否正在播放
   */
  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  /**
   * 手动触发重试播放
   * 可以在UI上添加重试按钮调用此方法
   */
  retryCurrentAudio(): boolean {
    if (this.currentAudioUrl) {
      this.retryPlay();
      return true;
    }
    return false;
  }
}
