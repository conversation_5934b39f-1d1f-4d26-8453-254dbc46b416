type SoundType = "click" | "error" | "success";

import { uploadObjectLog } from "@/utils/log/uploader";
import { createAudioContext } from "./audio-player";

interface PlayOptions {
  volume?: number; // 音量 0-1
  onError?: (error: any) => void; // 错误回调
  onComplete?: () => void; // 完成回调
}

// 音效资源映射
const SOUND_RESOURCES: Record<SoundType, string> = {
  click: "/static/sounds/click.mp3",
  error: "/static/sounds/wrong-answer.mp3",
  success: "/static/sounds/success.mp3",
};

// 音频实例缓存
const audioCache: Record<SoundType, UniApp.InnerAudioContext> = {
  click: createAudioContext(),
  error: createAudioContext(),
  success: createAudioContext(),
};

// 当前正在播放的音频
let currentPlayingAudio: UniApp.InnerAudioContext | null = null;

/**
 * 播放音效
 * @param type 音效类型
 * @param options 播放选项
 */
export const playSound = (type: SoundType, options: PlayOptions = {}) => {
  try {
    // 如果有正在播放的音频，先停止它
    if (currentPlayingAudio) {
      currentPlayingAudio.stop();
    }

    // 获取或创建音频实例
    let audio = audioCache[type];
    if (!audio) {
      audio = createAudioContext();
      audio.src = SOUND_RESOURCES[type];
      audio.autoplay = false;
      audioCache[type] = audio;
    }

    // 设置音量
    if (typeof options.volume === "number") {
      audio.volume = Math.max(0, Math.min(1, options.volume));
    }

    // 绑定事件
    const onEnded = () => {
      options.onComplete?.();
      audio.offEnded(onEnded);
      currentPlayingAudio = null;
    };

    const onError = (e: any) => {
      console.error(
        "音效播放错误:",
        e,
        "音效类型:",
        type,
        "音效路径:",
        SOUND_RESOURCES[type]
      );
      // 使用新方法上报错误对象
      uploadObjectLog("音效播放错误", {
        ...e,
        soundType: type,
        soundPath: SOUND_RESOURCES[type],
        volume: options.volume,
      });
      options.onError?.(e);
      audio.offError(onError);
      currentPlayingAudio = null;
    };

    audio.onEnded(onEnded);
    audio.onError(onError);

    // 记录当前播放的音频
    currentPlayingAudio = audio;

    // 播放音效
    audio.play();
  } catch (error: any) {
    options.onError?.(error);
  }
};

// 预加载所有音效
export const preloadSounds = () => {
  Object.entries(SOUND_RESOURCES).forEach(([type, src]) => {
    const audio = audioCache[type as SoundType];
    audio.src = src;
  });
};

// 释放所有音效资源
export const disposeSounds = () => {
  try {
    if (currentPlayingAudio) {
      try {
        currentPlayingAudio.stop();
      } catch (error: any) {
        console.error("停止当前播放音频失败:", error);
        uploadObjectLog("停止当前播放音频失败", {
          ...error,
        });
      }
      currentPlayingAudio = null;
    }

    Object.entries(audioCache).forEach(([type, audio]) => {
      if (audio) {
        try {
          audio.stop();
          audio.destroy();
          // 重新创建音频实例以保持 audioCache 结构完整
          audioCache[type as SoundType] = createAudioContext();
          audioCache[type as SoundType].src =
            SOUND_RESOURCES[type as SoundType];
        } catch (error) {
          console.error(`销毁音频 ${type} 失败:`, error);
        }
      }
    });
  } catch (error: any) {
    console.error("释放音效资源时发生错误:", error);
    uploadObjectLog("释放音效资源时发生错误", {
      ...error,
    });
  }
};
