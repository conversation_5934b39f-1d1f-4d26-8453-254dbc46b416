<template>
  <commonBox :loading="isLoading" title="文章跟读" @back="handleBack">
    <!-- 文章内容 -->
    <scroll-view
      class="article-container"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-with-animation="true"
      @scroll="handleScroll"
      :style="{ height: scrollHeight + 'px' }"
    >
      <view class="content-wrapper">
        <!-- 文章封面 -->
        <view class="article-cover" v-if="article.coverImage">
          <image :src="article.coverImage" mode="aspectFill"></image>
          <view class="cover-gradient"></view>
        </view>

        <!-- 文章标题 (当没有封面时显示) -->
        <view class="article-title" v-if="!article.coverImage">{{
          article.title
        }}</view>

        <!-- 文章内容 -->
        <view class="article-content">
          <!-- 直接使用Vue模板渲染段落和句子，不使用rich-text -->
          <view
            v-for="(paragraph, pIndex) in paragraphs"
            :key="'p-' + pIndex"
            :class="[
              'paragraph',
              { 'paragraph-title': paragraph.type === 1 },
              { 'paragraph-overview': paragraph.type === 2 },
              { 'paragraph-image': paragraph.type === 4 },
            ]"
          >
            <!-- 如果是图片段落，显示图片 -->
            <template v-if="paragraph.type === 4">
              <image
                :src="paragraph.text"
                mode="widthFix"
                class="paragraph-image-content"
              ></image>
            </template>
            <!-- 如果段落中没有标记任何句子，整段显示 -->
            <template v-else-if="paragraph.sentences.length === 0">
              {{ paragraph.text }}
            </template>

            <!-- 如果有句子，则按句子渲染 -->
            <template v-else>
              <!-- 渲染每个句子及句子间的文本 -->
              <template
                v-for="(sentence, sIndex) in paragraph.sentences"
                :key="'s-' + sentence.index"
              >
                <!-- 当前句子 -->
                <text
                  :class="[
                    getSentenceBaseClass(paragraph, sentence),
                    sIndex === 0 ? 'first-sentence' : '',
                  ]"
                  :data-index="sentence.index"
                  @click="handleSentenceClick(sentence.index)"
                >
                  <block
                    v-if="sentenceStates[sentence.index]?.scoreData?.words"
                  >
                    <text
                      v-for="(token, idx) in getSplitSentenceTokens(sentence)"
                      :key="idx"
                      :class="[
                        ...getTokenClass(token),
                        sIndex === 0 && idx === 0 ? 'first-token' : '',
                      ]"
                      >{{ token.text }}</text
                    >
                  </block>
                  <block v-else>
                    <!-- 未评分文本，使用HTML标签处理 -->
                    <template
                      v-for="(segment, idx) in applyHtmlTagsToText(
                        sentence.content,
                        sentence.htmlTags || []
                      )"
                      :key="'seg-' + idx"
                    >
                      <text :class="[...getHtmlTagClasses(segment.tags)]">{{
                        segment.text
                      }}</text>
                    </template>
                  </block>
                </text>
              </template>
            </template>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部控制面板 -->
    <view class="control-panel">
      <!-- 播放控制 - 改用reading-controls样式 -->
      <view class="reading-controls">
        <!-- 播放/暂停按钮 -->
        <view
          class="control-btn play-btn"
          :class="{
            disabled:
              !sentenceStates[currentSentenceIndex]?.scoreData?.audioUrl,
          }"
        >
          <view
            class="image commonBtn my-btn"
            @click="playScoreAudio"
            :class="{
              disabled:
                !sentenceStates[currentSentenceIndex]?.scoreData?.audioUrl,
              blinking:
                isPlayingScoreAudio &&
                sentenceStates[currentSentenceIndex]?.scoreData?.audioUrl,
            }"
          >
            <text
              v-if="sentenceStates[currentSentenceIndex]?.score"
              class="score-badge ipad-badge"
              >{{ sentenceStates[currentSentenceIndex]?.score }}分</text
            >
            <image
              src="/static/play.png"
              mode="aspectFit"
              class="play-icon"
            ></image>
          </view>
          <text>我的</text>
        </view>

        <!-- 录音按钮 -->
        <view
          class="control-btn record-btn"
          :class="{ recording: isRecording, disabled: !canRecord }"
        >
          <view
            class="reading-btn commonBtn"
            :class="{ blinking: canRecord && !isRecording }"
            @click="handleRecordClick"
          >
            <view class="reading-btn-reading" v-if="isRecording">
              <view class="bar left"></view>
              <view class="bar middle"></view>
              <view class="bar right"></view>
            </view>
            <image v-else :src="'/static/speak.png'" mode="aspectFit"></image>
          </view>
          <text>{{ recordButtonText }}</text>
        </view>

        <!-- 下一句按钮 -->
        <view
          class="control-btn next-btn"
          :class="{
            disabled: !canMoveToNextSentence,
            active: canMoveToNextSentence,
          }"
          @click="moveToNextSentence"
        >
          <view
            class="image commonBtn"
            :class="{
              disabled: !canMoveToNextSentence,
              active: canMoveToNextSentence,
            }"
          >
            <image src="/static/rij.png" mode="aspectFit"></image>
          </view>
          <text>下一句</text>
        </view>
      </view>
    </view>

    <!-- 使用ResultPopup组件 -->
    <ResultPopup
      :score="averageScore"
      :visible="resultPopupVisible"
      @close="handleResultClose"
      @continue="handleResultContinue"
    />
  </commonBox>
</template>

<script lang="ts" setup>
import { Article as ArticleType, evaluateArticleReading } from "@/api/article";
import { getHomeworkExerciseDetail } from "@/api/homework-exercise";
import commonBox from "@/components/common-box/commonBox.vue";
// @ts-ignore
import {
  computed,
  nextTick,
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
} from "vue";
// @ts-ignore
// 引入结果弹窗组件
import { uploadObjectLog } from "@/utils/log/uploader";
import ResultPopup from "./components/result-popup.vue";

// HTML标签接口定义
interface HtmlTag {
  tagType: string; // 标签类型，如 'b' 表示加粗
  startIndex: number; // 在文本中的起始位置
  endIndex: number; // 在文本中的结束位置
  attributes: string; // 标签的额外属性
}

// 自定义类型，用于内部处理
interface ApiSentence {
  id: number;
  index: number;
  content: string;
  startTime: number;
  endTime: number;
  duration: number;
  active?: boolean;
  sequence?: number;
  htmlTags?: HtmlTag[]; // 添加htmlTags属性
}

// 页面参数
const articleId = ref<number>(0);
const homeworkId = ref<number>(0);
const exerciseId = ref<number>(0);

// 文章数据
const article = reactive<ArticleType>({
  id: 0,
  title: "",
  content: "",
  audioUrl: "",
  duration: 0,
  coverImage: "",
  createTime: 0,
  extraParagraphs: [],
});

// 音频播放相关
const audioContext = ref<UniApp.InnerAudioContext | null>(null);
const isPlaying = ref<boolean>(false);
const currentTime = ref<number>(0);
const currentSentenceIndex = ref<number>(0);
const isLoading = ref<boolean>(false);

// 滚动相关
const scrollTop = ref<number>(0);
const scrollHeight = ref<number>(0);
const lastScrollTop = ref<number>(0);

// 处理段落和句子的关系
interface ParagraphSentence extends ApiSentence {
  active: boolean;
}

interface Paragraph {
  text: string;
  sentences: ParagraphSentence[];
  type?: number;
}

const paragraphs = computed((): Paragraph[] => {
  if (!article.extraParagraphs || article.extraParagraphs.length === 0) {
    return [];
  }

  // 直接使用后端返回的段落和句子信息
  return article.extraParagraphs.map((paragraph) => {
    // 转换句子格式
    const paragraphSentences: ParagraphSentence[] = paragraph.sentences.map(
      (sentence) => ({
        id: sentence.id,
        index: sentence.index,
        content: sentence.content,
        startTime: sentence.startTime,
        endTime: sentence.endTime,
        duration: sentence.duration,
        active: sentence.index === currentSentenceIndex.value,
        sequence: sentence.index + 1,
        htmlTags: (sentence as any).htmlTags || [], // 使用类型断言
      })
    );
    return {
      text: paragraph.text,
      sentences: paragraphSentences,
      type: paragraph.type,
    };
  });
});

// 添加一个计算属性，用来获取所有句子
const allSentences = computed<ApiSentence[]>(() => {
  if (!article.extraParagraphs || article.extraParagraphs.length === 0) {
    return [];
  }

  const sentences: ApiSentence[] = [];

  article.extraParagraphs.forEach((paragraph) => {
    paragraph.sentences.forEach((sentence) => {
      sentences.push({
        id: sentence.id,
        index: sentence.index,
        content: sentence.content,
        startTime: sentence.startTime,
        endTime: sentence.endTime,
        duration: sentence.duration,
        active: false,
        sequence: sentence.index + 1,
        htmlTags: (sentence as any).htmlTags || [], // 使用类型断言
      });
    });
  });

  // 按索引排序并返回
  return sentences.sort((a, b) => a.index - b.index);
});

// 跟读相关状态
const isRecording = ref<boolean>(false);
const recorderManager = ref<UniApp.RecorderManager | null>(null);
const tempFilePath = ref<string>("");
const canRecord = ref<boolean>(false);
const canMoveToNextSentence = ref<boolean>(false);
const currentSentenceText = ref<string>("");
// 定时器引用
const sentenceEndTimer = ref<any>(null);

// 句子状态记录
const sentenceStates = reactive<{
  [key: number]: {
    completed: boolean;
    score: number | null;
    scoreData?: any;
    sentenceId?: number;
  };
}>({});

// 评分结果
const scoreResult = reactive<{
  totalScore: number;
  pronunciation: number;
  fluency: number;
  integrity: number;
  tone: number;
}>({
  totalScore: 0,
  pronunciation: 0,
  fluency: 0,
  integrity: 0,
  tone: 0,
});

// 添加评分完成相关状态
const resultPopupVisible = ref<boolean>(false);

// 计算属性 - 检查是否所有句子都已完成评分
const allCompleted = computed(() => {
  // 获取所有句子数量
  const totalSentences = allSentences.value.length;
  if (totalSentences === 0) return false;

  // 获取已完成评分的句子数量
  const completedSentences = Object.values(sentenceStates).filter(
    (state) => state.completed
  ).length;

  // 如果所有句子都已完成评分，返回 true
  return completedSentences === totalSentences;
});

// 计算平均分
const averageScore = computed(() => {
  const scores = Object.values(sentenceStates).map((state) => state.score || 0);
  if (scores.length === 0) return 0;

  const sum = scores.reduce((acc, score) => acc + score, 0);
  return Math.round(sum / scores.length);
});

// 播放评分音频相关
const isPlayingScoreAudio = ref<boolean>(false);
const scoreAudioContext = ref<UniApp.InnerAudioContext | null>(null);

// 新增计算属性 `recordButtonText`
const recordButtonText = computed(() => {
  if (isRecording.value) {
    return "跟读中...";
  }
  const currentState = sentenceStates[currentSentenceIndex.value];
  return currentState?.completed ? "再读一次" : "点击开始跟读";
});

// 初始化页面
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1] as any;
  const options = currentPage?.options || {};

  articleId.value = Number(options.id || 0);
  homeworkId.value = Number(options.homeworkId || 0);
  exerciseId.value = Number(options.id || 0);

  // 计算滚动区域高度
  calculateScrollHeight();

  // 获取文章数据
  fetchArticleDetail();

  // 监听窗口大小变化
  uni.onWindowResize(handleResize);

  // 初始化录音管理器
  initRecorderManager();
});

onBeforeUnmount(() => {
  // 销毁音频上下文
  destroyAudioContext();

  // 销毁评分音频上下文
  if (scoreAudioContext.value) {
    scoreAudioContext.value.destroy();
    scoreAudioContext.value = null;
  }

  // 移除事件监听
  uni.offWindowResize(handleResize);
});

// 计算滚动区域高度
const calculateScrollHeight = () => {
  const windowHeight = uni.getSystemInfoSync().windowHeight;
  // 控制面板高度约200rpx(100px)，commonBox已经处理了头部高度
  scrollHeight.value = windowHeight - 140; // 适当调整高度以适应新增的控制面板
};

// 处理窗口大小变化
const handleResize = () => {
  calculateScrollHeight();
};

// 获取文章详情
const fetchArticleDetail = async () => {
  try {
    isLoading.value = true;
    uni.showLoading({ title: "加载中...", mask: true });

    const response = await getHomeworkExerciseDetail(exerciseId.value);

    if (response.code === 0 && response.data) {
      // 从练习数据中提取文章数据
      const exerciseData = response.data as any; // 使用any类型避免类型错误

      if (exerciseData.extraArticleData) {
        // 设置文章数据
        Object.assign(article, exerciseData.extraArticleData);
      } else {
        uni.showToast({ title: "文章数据不存在", icon: "none" });
        return;
      }

      if (!article.extraParagraphs) {
        article.extraParagraphs = [];
      }

      // 处理已有的评分数据（如果存在）
      if (
        exerciseData.extSubmissionDetail?.articleEvaluationResult
          ?.articleResults
      ) {
        const articleResults =
          exerciseData.extSubmissionDetail.articleEvaluationResult
            .articleResults;

        // 遍历评分结果，根据句子ID更新sentenceStates
        Object.keys(articleResults).forEach((sentenceId) => {
          const scoreData = articleResults[sentenceId];
          // 在所有句子中查找匹配此ID的句子
          const matchingSentence = findSentenceById(parseInt(sentenceId));
          if (matchingSentence) {
            // 更新状态，使用句子索引作为sentenceStates的键
            sentenceStates[matchingSentence.index] = {
              completed: true,
              score: scoreData.totalScore,
              scoreData: scoreData,
              sentenceId: parseInt(sentenceId), // 存储句子ID以便后续使用
            };
          }
        });
      }

      nextTick(() => {
        // 初始化音频上下文
        initAudioContext();

        // 数据加载完成后，主动设置第一个句子，确保currentSentenceText被正确初始化
        if (allSentences.value.length > 0) {
          console.log("数据加载完成，初始化第一个句子");
          // 默认选择第一个句子，只更新状态不自动播放
          handleSentenceChange(0);

          // 检查是否所有句子都已完成评分，如果是则显示结果弹窗
          console.log("检查是否所有句子已完成", allCompleted.value);
          if (allCompleted.value) {
            console.log("所有句子都已完成评分，显示结果弹窗");
            // 延迟显示弹窗，确保组件已挂载
            setTimeout(() => {
              showResultPopup();
            }, 500);
          }
        }
      });
    } else {
      uni.showToast({ title: response.msg || "获取数据失败", icon: "none" });
    }
  } catch (error) {
    console.error("获取文章详情失败:", error);
    uni.showToast({ title: "获取数据失败", icon: "none" });
  } finally {
    isLoading.value = false;
    uni.hideLoading();
  }
};

// 增加一个辅助函数，通过ID查找句子
const findSentenceById = (id: number): ApiSentence | null => {
  if (!article.extraParagraphs) return null;

  for (const paragraph of article.extraParagraphs) {
    for (const sentence of paragraph.sentences) {
      if (sentence.id === id) {
        return {
          id: sentence.id,
          index: sentence.index,
          content: sentence.content,
          startTime: sentence.startTime,
          endTime: sentence.endTime,
          duration: sentence.duration,
          htmlTags: (sentence as any).htmlTags || [], // 添加htmlTags属性
        };
      }
    }
  }
  return null;
};

// 初始化录音管理器
const initRecorderManager = () => {
  recorderManager.value = uni.getRecorderManager();

  recorderManager.value.onStart(() => {
    console.log("录音开始");
    isRecording.value = true;
  });

  recorderManager.value.onStop((res) => {
    console.log("录音结束", res);
    isRecording.value = false;
    tempFilePath.value = res.tempFilePath;

    // 提交录音进行评分
    submitRecordingForEvaluation();
  });

  recorderManager.value.onError((err) => {
    console.error(
      "录音错误",
      err,
      "错误代码:",
      err.errCode,
      "错误信息:",
      err.errMsg
    );
    // 使用新方法上报错误对象
    uploadObjectLog("文章跟读录音错误", {
      ...err,
      currentSentenceIndex: currentSentenceIndex.value,
      currentSentenceText: currentSentenceText.value,
    });
    isRecording.value = false;
    uni.showToast({
      title: "录音失败",
      icon: "none",
    });
  });
};

// 处理句子点击时，更新当前句子文本
const handleSentenceClick = (sentenceIndex: number) => {
  console.log("句子被点击:", sentenceIndex);

  // 检查是否正在录音，如果是则提示用户
  if (isRecording.value) {
    uni.showToast({
      title: "请先暂停录音",
      icon: "none",
    });
    return;
  }

  // 使用已有的validateSentenceData方法验证句子数据
  const sentence = validateSentenceData(sentenceIndex);
  if (!sentence) return;

  const sentenceNotChanged = currentSentenceIndex.value === sentenceIndex;
  // 判断该句子是否已被选中
  if (sentenceNotChanged) {
    console.log("当前句子已被选中，切换播放状态");
    // 切换播放/暂停状态
    togglePlay();
    return;
  }

  // 检查该句子是否已评分
  const sentenceCompleted = sentenceStates[sentenceIndex]?.completed;

  // 更新当前句子状态
  handleSentenceChange(sentenceIndex);

  if (sentenceCompleted && sentenceStates[sentenceIndex]?.scoreData) {
    // 如果是已评分的句子，切换到该句子但不自动播放
    updateAudioState(sentenceIndex, false);
  } else {
    // 如果是未评分的句子，切换并自动播放
    updateAudioState(sentenceIndex, true);
  }
};

// 统一处理句子变化和定时器逻辑
const handleSentenceChange = (sentenceIndex: number) => {
  console.log(`句子变化：切换到句子 ${sentenceIndex}`);
  const sentences = allSentences.value;

  // 验证句子索引有效性
  if (sentenceIndex < 0 || sentenceIndex >= sentences.length) {
    console.error("无效的句子索引:", sentenceIndex);
    return;
  }

  // 停止播放评分音频
  if (isPlayingScoreAudio.value) {
    stopScoreAudio();
  }

  // 获取新句子
  const sentence = sentences[sentenceIndex];
  if (!sentence || sentence.startTime === undefined) {
    console.error("无法处理，句子数据无效:", sentence);
    return;
  }

  // 更新当前句子索引和文本
  currentSentenceIndex.value = sentenceIndex;
  // 确保设置当前句子文本，如果content为空或undefined，设置为空字符串
  currentSentenceText.value = sentence.content || "";
  console.log("当前句子文本已设置:", currentSentenceText.value);
};

// 更新音频状态 (原seekToSentence改名为updateAudioState)
const updateAudioState = (sentenceIndex: number, autoPlay: boolean = true) => {
  console.log("尝试更新音频状态, 句子:", sentenceIndex, "自动播放:", autoPlay);

  // 1. 验证句子数据
  const sentence = validateSentenceData(sentenceIndex);
  if (!sentence) return;

  // 2. 跳转到句子时间位置
  const seekSuccess = seekToSentence(sentence);
  if (!seekSuccess) return;

  // 3. 控制播放状态和定时器
  controlPlaybackState(sentence, autoPlay);
};

// 验证句子数据是否有效
const validateSentenceData = (sentenceIndex: number): ApiSentence | null => {
  const sentences = allSentences.value;

  // 验证音频上下文和句子索引
  if (
    !audioContext.value ||
    sentenceIndex < 0 ||
    sentenceIndex >= sentences.length
  ) {
    console.error("无法更新音频状态，句子索引无效或音频上下文不存在");
    return null;
  }

  const sentence = sentences[sentenceIndex];
  if (!sentence || sentence.startTime === undefined) {
    console.error("无法更新音频状态，句子数据无效:", sentence);
    return null;
  }

  return sentence;
};

// 跳转到句子的时间位置
const seekToSentence = (sentence: ApiSentence): boolean => {
  if (!audioContext.value) return false;

  // 计算要跳转的时间（毫秒转秒）
  const targetTime = sentence.startTime / 1000;
  console.log("设置音频位置:", targetTime, "秒, 句子内容:", sentence.content);

  // 跳转到对应时间
  audioContext.value.seek(targetTime);
  return true;
};

// 控制播放状态和定时器
const controlPlaybackState = (sentence: ApiSentence, autoPlay: boolean) => {
  // 如果需要自动播放
  if (autoPlay) {
    // 如果未播放，则开始播放并设置定时器
    if (!isPlaying.value) {
      console.log("开始播放音频");
      togglePlay(); // 这会触发onPlay事件，在那里会设置定时器
    } else {
      // 如果已经在播放，只需要更新定时器
      setupSentenceEndTimer(sentence);
    }
  } else {
    // 不需要自动播放，确保清除定时器
    clearSentenceTimer();

    // 如果正在播放，暂停
    if (isPlaying.value) {
      audioContext.value?.pause();
    }
  }
};

// 统一处理定时器逻辑
const setupSentenceEndTimer = (sentence: ApiSentence) => {
  console.log("设置定时器，句子:", sentence);

  if (!audioContext.value || !isPlaying.value) return;

  // 先确定音频位置，获取是否重置了位置
  const isReset = determineAudioPosition(sentence);

  // 再设置播放定时器
  setupPlaybackTimer(sentence, isReset);
};

// 确定音频位置
const determineAudioPosition = (sentence: ApiSentence): boolean => {
  if (!audioContext.value) return false;

  // 获取当前音频播放时间（毫秒）
  const currentTimeMs = audioContext.value.currentTime * 1000;

  // 判断是否需要重置播放位置
  const isWithinSentence =
    currentTimeMs >= sentence.startTime && currentTimeMs < sentence.endTime;
  const hasEnoughTimeLeft = sentence.endTime - currentTimeMs > 1000;

  // 如果在句子范围内且剩余时间足够，继续播放；否则重置
  if (isWithinSentence && hasEnoughTimeLeft) {
    // 不需要重置位置，返回false
    return false;
  } else {
    // 需要重置到句子开始位置
    seekToSentence(sentence);
    // 已重置位置，返回true
    return true;
  }
};

// 设置播放定时器
const setupPlaybackTimer = (sentence: ApiSentence, isReset: boolean) => {
  // 清除可能存在的定时器
  clearSentenceTimer();

  if (!audioContext.value || !isPlaying.value) return;

  // 获取当前音频播放时间（毫秒）
  const currentTimeMs = audioContext.value.currentTime * 1000;

  let timerDuration;

  if (isReset) {
    // 如果已重置位置，使用整个句子的持续时间
    timerDuration = sentence.duration;
    console.log(`定时器：重置后将在 ${timerDuration.toFixed(0)}ms 后暂停`);
  } else {
    // 如果未重置位置，计算剩余时间
    timerDuration = sentence.endTime - currentTimeMs;
    console.log(`定时器：继续播放，将在 ${timerDuration.toFixed(0)}ms 后暂停`);
  }

  // 设置定时器
  sentenceEndTimer.value = setTimeout(() => {
    console.log("定时器：触发暂停");
    pauseAudio();
    sentenceEndTimer.value = null;
  }, timerDuration);
};

// 统一暂停音频逻辑
const pauseAudio = () => {
  if (audioContext.value && isPlaying.value) {
    audioContext.value.pause();
    isPlaying.value = false;
    canRecord.value = true;
  }
};

// 统一清除定时器逻辑
const clearSentenceTimer = () => {
  if (sentenceEndTimer.value !== null) {
    clearTimeout(sentenceEndTimer.value);
    sentenceEndTimer.value = null;
  }
};

// 音频上下文状态更新（简化版）
const setAudioContextEvents = () => {
  if (!audioContext.value) return;

  // 注册所有音频事件监听器
  // 可以播放事件
  audioContext.value.onCanplay(() => {
    console.log("音频可以播放，时长:", audioContext.value!.duration);
  });

  // 播放进度更新事件（简化为只检测句子变化）
  audioContext.value.onTimeUpdate(() => {
    // 更新当前时间
    currentTime.value = audioContext.value!.currentTime;
  });

  // 播放事件
  audioContext.value.onPlay(() => {
    isPlaying.value = true;
    canRecord.value = false; // 播放时禁止录音
    console.log("音频开始播放");

    // 如果已经设置了当前句子，确保为其设置定时器
    const currentSentence = allSentences.value[currentSentenceIndex.value];
    if (currentSentence) {
      setupSentenceEndTimer(currentSentence);
    }
  });

  // 暂停事件
  audioContext.value.onPause(() => {
    isPlaying.value = false;
    console.log("音频已暂停，当前时间:", currentTime.value * 1000, "ms");

    // 清除定时器
    clearSentenceTimer();

    // 检查当前句子是否已经开始播放
    const currentSentence = allSentences.value[currentSentenceIndex.value];
    if (
      currentSentence &&
      currentTime.value * 1000 >= currentSentence.startTime
    ) {
      canRecord.value = true; // 允许录音
      console.log("允许录音");
    }
  });

  // 播放停止事件
  audioContext.value.onStop(() => {
    clearSentenceTimer();
  });

  // 播放结束事件
  audioContext.value.onEnded(() => {
    clearSentenceTimer();
    isPlaying.value = false;
    currentTime.value = 0;
    currentSentenceIndex.value = 0;
    scrollTop.value = 0;
    canRecord.value = false;
  });

  // 错误事件
  audioContext.value.onError((err) => {
    clearSentenceTimer();
    console.error(
      "音频播放错误:",
      err,
      "错误代码:",
      err.errCode,
      "错误信息:",
      err.errMsg,
      "音频地址:",
      article.audioUrl
    );
    // 使用新方法上报错误对象
    uploadObjectLog("文章跟读音频播放错误", {
      ...err,
      audioUrl: article.audioUrl,
      articleId: article.id,
      currentTime: currentTime.value,
    });
    isPlaying.value = false;
    uni.showToast({
      title: "音频播放失败",
      icon: "none",
    });
  });
};

// 初始化音频上下文
const initAudioContext = () => {
  // 销毁已有实例
  destroyAudioContext();

  if (!article.audioUrl) {
    console.error("音频地址无效");
    return;
  }

  const innerAudioContext = uni.createInnerAudioContext();
  audioContext.value = innerAudioContext;

  innerAudioContext.src = article.audioUrl;
  innerAudioContext.autoplay = false;
  innerAudioContext.obeyMuteSwitch = false;

  // 注册所有事件监听器
  setAudioContextEvents();
};

// 销毁音频上下文
const destroyAudioContext = () => {
  if (audioContext.value) {
    audioContext.value.destroy();
    audioContext.value = null;
  }

  // 清理定时器
  if (sentenceEndTimer.value !== null) {
    clearTimeout(sentenceEndTimer.value);
    sentenceEndTimer.value = null;
  }
};

// 播放/暂停
const togglePlay = () => {
  if (!audioContext.value) return;

  if (isPlaying.value) {
    audioContext.value.pause();
    // 定时器在onPause事件中会被清除
  } else {
    audioContext.value.play();
    // 定时器在onPlay事件中会被启动
  }
};

// 处理录音按钮点击
const handleRecordClick = () => {
  if (!canRecord.value) {
    uni.showToast({
      title: "请先听完当前句子",
      icon: "none",
    });
    return;
  }

  // 如果正在播放评分音频，先暂停
  if (isPlayingScoreAudio.value) {
    stopScoreAudio();
  }

  if (isRecording.value) {
    // 停止录音
    recorderManager.value?.stop();
  } else {
    // 开始录音
    recorderManager.value?.start({
      duration: 60000, // 最长录音时间，单位ms
      sampleRate: 44100, // 采样率
      numberOfChannels: 1, // 录音通道数
      encodeBitRate: 192000, // 编码码率
      format: "mp3", // 音频格式
      frameSize: 50, // 指定帧大小
    });
  }
};

// 新增音效播放方法
const playSound = (soundName: string) => {
  const soundPath = `/static/sounds/${soundName}.mp3`;
  const soundContext = uni.createInnerAudioContext();
  soundContext.src = soundPath;
  soundContext.autoplay = true;

  soundContext.onError((err) => {
    console.error("音效播放失败:", err);
  });
};

// 修改评分逻辑
const submitRecordingForEvaluation = () => {
  if (!tempFilePath.value || !currentSentenceText.value) {
    console.error(tempFilePath.value, currentSentenceText.value);
    uni.showToast({
      title: "录音或文本为空",
      icon: "none",
    });
    return;
  }

  uni.showLoading({
    title: "评分中...",
    mask: true,
  });

  // 获取当前句子
  const currentSentence = allSentences.value[currentSentenceIndex.value];

  if (!currentSentence || !currentSentence.id) {
    console.error("找不到当前句子或句子ID无效");
    uni.showToast({
      title: "句子数据错误",
      icon: "none",
    });
    uni.hideLoading();
    return;
  }

  // 保存当前操作的句子ID和索引，以便在异步回调中使用
  const sentenceId = currentSentence.id;
  const sentenceIndex = currentSentenceIndex.value;

  // 检查是否所有句子都已完成评分
  // 计算当前评估结束后是否所有句子都已评分
  // 首先假设当前句子会评分完成
  const tempSentenceStates = { ...sentenceStates };
  tempSentenceStates[sentenceIndex] = { completed: true, score: 0 };

  const willCompleteAll = allSentences.value.every(
    (sentence) => tempSentenceStates[sentence.index]?.completed === true
  );

  // 仅根据是否所有句子都已完成评分来设置isFinish
  const isFinish = willCompleteAll;

  // 使用API方法上传录音文件和文本进行评分，添加isFinish参数
  evaluateArticleReading(
    currentSentenceText.value,
    tempFilePath.value,
    sentenceId,
    exerciseId.value,
    isFinish
  )
    .then((res) => {
      uni.hideLoading();

      if (res.code === 0 && res.data) {
        // 保存评分结果
        Object.assign(scoreResult, res.data);

        // 根据评分分值播放音效
        const score = scoreResult.totalScore;
        if (score <= 50) {
          console.log("keep it up");
          playSound("keepitup-sentence");
        } else if (score <= 89) {
          console.log("nice");
          playSound("nice-sentence");
        } else {
          console.log("excellent");
          playSound("excellent-sentence");
        }

        // 更新句子状态，使用sentenceId确保即使当前句子已经变化，仍然更新正确的句子
        sentenceStates[sentenceIndex] = {
          completed: true,
          score: scoreResult.totalScore,
          scoreData: res.data, // 保存完整的评分数据
          sentenceId: sentenceId, // 存储句子ID
        };

        // 允许进入下一句
        canMoveToNextSentence.value = true;

        // 检查是否所有句子都已完成评分且isFinish为true
        if (isFinish) {
          console.log("所有句子都已完成评分，显示结果弹窗");
          showResultPopup();
        }
      } else {
        uni.showToast({
          title: res.msg || "评分失败",
          icon: "none",
        });
      }
    })
    .catch((err) => {
      uni.hideLoading();
      console.error("评分请求错误", err);
      uni.showToast({
        title: "评分请求失败",
        icon: "none",
      });
    });
};

// 移动到下一个句子
const moveToNextSentence = () => {
  if (
    !canMoveToNextSentence.value &&
    !sentenceStates[currentSentenceIndex.value]?.completed
  ) {
    uni.showToast({
      title: "请先完成当前句子的跟读",
      icon: "none",
    });
    return;
  }

  const nextIndex = currentSentenceIndex.value + 1;
  if (nextIndex < allSentences.value.length) {
    handleSentenceChange(nextIndex);
    updateAudioState(nextIndex);
  } else {
    // 所有句子都已完成
    uni.showToast({
      title: "恭喜！您已完成全部跟读",
      icon: "success",
    });
  }
};

// 处理滚动事件
const handleScroll = (e: any) => {
  lastScrollTop.value = e.detail.scrollTop;
};

// 解析句子为标记列表
const getSplitSentenceTokens = (
  sentence: ApiSentence
): Array<{ text: string; type: string; htmlTags?: string[] }> => {
  // 如果没有评分数据，返回原文
  const scoreData = sentenceStates[sentence.index]?.scoreData;
  if (!scoreData || !scoreData.words || scoreData.words.length === 0) {
    return [{ text: sentence.content, type: "normal" }];
  }

  // 原始文本
  const originalText = sentence.content.trim(); // 去除首尾空格

  // 从评分数据获取单词列表
  const scoredWords = scoreData.words;

  // 分割原始文本（只按单词分割，不单独处理空格）
  // 使用正则表达式匹配完整单词，包括其后的空格和标点
  const tokens = originalText.match(/\S+\s*|[^\w\s]+\s*/g) || [];
  console.log("tokens", tokens);

  // 单词计数器（用于匹配评分数据）
  let wordIndex = 0;

  // 构建标记列表
  const result: Array<{ text: string; type: string; htmlTags?: string[] }> = [];

  // 获取HTML标签信息
  const htmlTags = sentence.htmlTags || [];

  // 首先创建一个字符索引到标签的映射
  const charToTagsMap: Map<number, string[]> = new Map();

  // 初始化映射
  for (let i = 0; i < originalText.length; i++) {
    charToTagsMap.set(i, []);
  }

  // 填充映射
  for (const tag of htmlTags) {
    for (let i = tag.startIndex; i < tag.endIndex; i++) {
      if (charToTagsMap.has(i)) {
        charToTagsMap.get(i)!.push(tag.tagType);
      }
    }
  }

  // 处理每个标记
  let currentCharIndex = 0;

  for (const token of tokens) {
    // 提取单词（去除空格和标点）
    const wordMatch = token.trim().match(/^\w+/);
    const word = wordMatch ? wordMatch[0] : "";

    // 收集这个token中所有字符的标签
    const tokenTags = new Set<string>();
    for (let i = 0; i < token.length; i++) {
      const charIndex = currentCharIndex + i;
      if (charToTagsMap.has(charIndex)) {
        const tags = charToTagsMap.get(charIndex)!;
        tags.forEach((tag) => tokenTags.add(tag));
      }
    }
    // 转换为数组
    const tokenTagsArray = Array.from(tokenTags);

    // 更新当前字符索引
    currentCharIndex += token.length;

    if (word) {
      // 如果是单词，检查是否有对应的评分数据
      if (wordIndex < scoredWords.length) {
        const wordScore = scoredWords[wordIndex];
        const wordType = wordScore.type;

        // 根据类型添加不同标记，并包含HTML标签信息
        if (wordType === 2) {
          // 正常词
          result.push({
            text: token,
            type: "correct",
            htmlTags: tokenTagsArray,
          });
        } else if (wordType === 3) {
          // 错误词
          result.push({ text: token, type: "error", htmlTags: tokenTagsArray });
        } else if (wordType === 1) {
          // 漏词
          result.push({
            text: token,
            type: "missed",
            htmlTags: tokenTagsArray,
          });
        } else {
          result.push({
            text: token,
            type: "normal",
            htmlTags: tokenTagsArray,
          });
        }

        wordIndex++;
      } else {
        // 如果评分数据不足，保持原样
        result.push({ text: token, type: "normal", htmlTags: tokenTagsArray });
      }
    } else {
      // 非单词（标点等）
      result.push({ text: token, type: "correct", htmlTags: tokenTagsArray });
    }
  }
  console.log("result", result);
  return result;
};

// 获取标记的样式类
const getTokenClass = (token: {
  text: string;
  type: string;
  htmlTags?: string[];
}): string[] => {
  const classes: string[] = [];

  // 添加基于类型的样式类
  switch (token.type) {
    case "correct":
      classes.push("token-correct");
      break;
    case "error":
      classes.push("token-error");
      break;
    case "missed":
      classes.push("token-missed");
      break;
    case "space":
      classes.push("token-space");
      break;
  }

  // 添加HTML标签相关的样式类
  if (token.htmlTags && token.htmlTags.length > 0) {
    for (const tag of token.htmlTags) {
      classes.push(`tag-${tag}`);
    }
  }

  return classes;
};

// 返回按钮处理
const handleBack = () => {
  uni.navigateBack();
};

// 播放评分音频
const playScoreAudio = () => {
  // 获取当前句子的评分数据
  const currentState = sentenceStates[currentSentenceIndex.value];
  const audioUrl = currentState?.scoreData?.audioUrl;

  if (!audioUrl) {
    uni.showToast({
      title: "当前句子没有评分音频",
      icon: "none",
    });
    return;
  }

  // 如果原文音频正在播放，先暂停
  if (isPlaying.value) {
    audioContext.value?.pause();
    isPlaying.value = false;
  }

  // 判断是否正在播放评分音频
  if (isPlayingScoreAudio.value && scoreAudioContext.value) {
    // 如果已经在播放评分音频，则暂停
    scoreAudioContext.value.pause();
    isPlayingScoreAudio.value = false;
  } else {
    // 如果有之前创建的音频上下文，先销毁
    if (scoreAudioContext.value) {
      scoreAudioContext.value.destroy();
    }

    // 创建新的音频上下文播放评分音频
    const newScoreAudioCtx = uni.createInnerAudioContext();
    scoreAudioContext.value = newScoreAudioCtx;

    newScoreAudioCtx.src = audioUrl;
    newScoreAudioCtx.autoplay = true;

    isPlayingScoreAudio.value = true;

    // 监听播放结束
    newScoreAudioCtx.onEnded(() => {
      isPlayingScoreAudio.value = false;
    });

    // 监听错误
    newScoreAudioCtx.onError((err) => {
      console.error(
        "评分音频播放错误:",
        err,
        "错误代码:",
        err.errCode,
        "错误信息:",
        err.errMsg
      );
      // 使用新方法上报错误对象
      uploadObjectLog("文章跟读评分音频错误", {
        ...err,
        currentSentenceIndex: currentSentenceIndex.value,
        hasScoreData: !!sentenceStates[currentSentenceIndex.value]?.scoreData,
      });
      isPlayingScoreAudio.value = false;
      uni.showToast({
        title: "音频播放失败",
        icon: "none",
      });
    });
  }
};

// 停止评分音频播放
const stopScoreAudio = () => {
  if (scoreAudioContext.value) {
    scoreAudioContext.value.stop();
    scoreAudioContext.value.destroy();
    scoreAudioContext.value = null;
  }
  isPlayingScoreAudio.value = false;
};

// 添加一个帮助方法来获取句子的基础类名
const getSentenceBaseClass = (
  paragraph: Paragraph,
  sentence: ParagraphSentence
): string => {
  // 是否为标题
  const isTitle = paragraph.type === 1;
  // 是否激活
  const isActive = sentence.active;

  if (isTitle) {
    return isActive ? "active-title-sentence" : "title-sentence";
  } else {
    return isActive ? "active-sentence" : "sentence";
  }
};

// 显示结果弹窗
const showResultPopup = () => {
  console.log("准备显示结果弹窗，当前状态:", resultPopupVisible.value);
  // 强制关闭后再打开，确保状态变化能触发watch
  resultPopupVisible.value = false;

  // 使用setTimeout确保Vue能够检测到状态变化
  setTimeout(() => {
    console.log("设置弹窗状态为true");
    resultPopupVisible.value = true;
    console.log("设置完成，当前状态:", resultPopupVisible.value);
  }, 100);
};

// 处理关闭结果弹窗
const handleResultClose = () => {
  resultPopupVisible.value = false;
};

// 处理继续练习按钮点击
const handleResultContinue = () => {
  resultPopupVisible.value = false;
  // 可以添加额外的继续练习逻辑，如回到第一句
};

// 应用HTML标签到文本，返回带有样式信息的文本片段数组
const applyHtmlTagsToText = (
  text: string,
  htmlTags: HtmlTag[]
): Array<{ text: string; tags: string[] }> => {
  if (!htmlTags || htmlTags.length === 0) {
    // 如果没有标签，直接返回原文本
    return [{ text: text, tags: [] }];
  }

  // 将文本分割成多个片段，每个片段对应一个或多个标签
  const segments: Array<{
    startIndex: number;
    endIndex: number;
    tags: string[];
  }> = [];

  // 初始化一个段落，表示整个文本
  segments.push({ startIndex: 0, endIndex: text.length, tags: [] });

  // 根据每个HTML标签分割文本
  for (const tag of htmlTags) {
    const { tagType, startIndex, endIndex } = tag;
    if (startIndex >= endIndex || startIndex < 0 || endIndex > text.length) {
      console.warn("无效的HTML标签范围:", tag);
      continue;
    }

    // 创建新的分段
    const newSegments: typeof segments = [];

    for (const segment of segments) {
      // 检查当前段落是否与标签范围重叠
      if (segment.endIndex <= startIndex || segment.startIndex >= endIndex) {
        // 不重叠，保持原样
        newSegments.push(segment);
      } else {
        // 重叠情况，需要分割
        if (segment.startIndex < startIndex) {
          // 标签前的文本
          newSegments.push({
            startIndex: segment.startIndex,
            endIndex: startIndex,
            tags: [...segment.tags],
          });
        }

        // 标签覆盖的文本
        newSegments.push({
          startIndex: Math.max(segment.startIndex, startIndex),
          endIndex: Math.min(segment.endIndex, endIndex),
          tags: [...segment.tags, tagType],
        });

        if (segment.endIndex > endIndex) {
          // 标签后的文本
          newSegments.push({
            startIndex: endIndex,
            endIndex: segment.endIndex,
            tags: [...segment.tags],
          });
        }
      }
    }

    // 更新段落列表
    segments.length = 0;
    segments.push(...newSegments);
  }
  console.log(
    "segments123",
    segments
      .sort((a, b) => a.startIndex - b.startIndex)
      .map((segment) => ({
        text: text.substring(segment.startIndex, segment.endIndex),
        tags: segment.tags,
      }))
      .filter((segment) => segment.text.length > 0)
  );
  // 将索引转换成实际文本
  return segments
    .sort((a, b) => a.startIndex - b.startIndex)
    .map((segment) => ({
      text: text.substring(segment.startIndex, segment.endIndex),
      tags: segment.tags,
    }))
    .filter((segment) => segment.text.length > 0); // 过滤空段落
};

// 获取HTML标签样式类
const getHtmlTagClasses = (tags: string[]): string[] => {
  if (!tags || tags.length === 0) {
    return [];
  }

  // 将每个标签转换为对应的样式类
  return tags.map((tag) => `tag-${tag}`);
};
</script>

<style lang="scss" scoped>
.article-container {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20rpx;

  .content-wrapper {
    padding: 20rpx 30rpx;
    padding-bottom: 220rpx;

    .article-cover {
      width: 100%;
      height: 400rpx;
      border-radius: 24rpx;
      overflow: hidden;
      margin-bottom: 40rpx;
      position: relative;
      box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);

      image {
        width: 100%;
        height: 100%;
        transition: transform 0.3s ease;
        transform-origin: center;
      }

      .cover-gradient {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 60%;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      }

      .article-title-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 30rpx;
        font-size: 40rpx;
        font-weight: 600;
        color: #c8a97e; /* 金褐色 */
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
        text-transform: uppercase; /* 全大写 */
        letter-spacing: 2rpx; /* 增加字间距 */
      }
    }

    .article-title {
      font-size: 40rpx;
      font-weight: 600;
      color: #c8a97e; /* 金褐色 */
      margin-bottom: 40rpx;
      line-height: 1.4;
      position: relative;
      padding-bottom: 16rpx;
      letter-spacing: 2rpx; /* 增加字间距 */

      &:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 120rpx;
        height: 8rpx;
        background: #c8a97e; /* 金褐色 */
        border-radius: 4rpx;
      }
    }

    .article-content {
      background-color: #ffffff;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
      line-height: 1.8;
      font-size: 34rpx;
      color: #333333;
      padding: 20rpx;

      .paragraph {
        margin-bottom: 24rpx;
        text-align: left;
        word-spacing: normal;
      }

      .paragraph-title {
        margin-bottom: 30rpx;
        line-height: 1.4;
        position: relative;
        padding-bottom: 16rpx;
      }

      .paragraph-image {
        margin: 30rpx 0;
        text-align: center;
      }

      .paragraph-image-content {
        width: 100%;
        border-radius: 16rpx;
      }

      .title-sentence {
        display: inline;
        font-size: 40rpx;
        font-weight: 600;
        color: #c8a97e; /* 金褐色 */
        letter-spacing: 2rpx; /* 增加字间距 */
        transition: all 0.3s ease;
        cursor: pointer;
        border-radius: 6rpx;
      }

      .active-title-sentence {
        display: inline;
        font-size: 40rpx;
        font-weight: 600;
        color: #c8a97e; /* 保持金褐色不变 */
        letter-spacing: 2rpx;
        background-color: rgba(200, 169, 126, 0.2);
        padding: 2rpx 0;
        border-bottom: 4rpx solid #c8a97e;
        transition: all 0.3s ease;
        cursor: pointer;
        border-radius: 6rpx;
      }

      .paragraph-overview {
        position: relative;
        margin-bottom: 30rpx;
        color: #666666;
        background-color: #f9f9f9;
        padding: 20rpx 20rpx 20rpx 10rpx;
        border-radius: 12rpx;

        &:before {
          content: "";
          position: absolute;
          left: -6rpx;
          top: 0;
          bottom: 0;
          width: 6rpx;
          background-color: #ffd600;
          border-radius: 3rpx;
        }
      }

      .sentence,
      .active-sentence {
        display: inline;
        transition: all 0.3s ease;
        cursor: pointer;
        padding: 8rpx;
        border-radius: 6rpx;
        word-spacing: normal;
      }

      text {
        display: inline;
        word-spacing: normal;
      }

      ::v-deep(rich-text) {
        line-height: 1.8;
        word-spacing: normal;

        p {
          margin-bottom: 24rpx;
          text-align: left;
        }

        span {
          transition: all 0.3s ease;
        }
      }
    }
  }
}

.control-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  padding: 40rpx 20rpx;
  z-index: 10;
  display: flex;
  flex-direction: column;

  .reading-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;

    .control-btn {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
      opacity: 1;

      &.disabled {
        pointer-events: none;
        opacity: 0.3;
      }

      &.play-btn:not(.disabled) {
        .image {
          background-color: #ffd84e;
          position: relative;

          .play-icon {
            width: 16px;
            height: 16px;
          }
        }
      }

      &.play-btn.disabled {
        .image {
          background-color: rgba(196, 196, 196, 1);
        }
      }

      &.record-btn {
        .image {
          background-color: #ffebee;
        }

        &.recording {
          .image {
            background-color: #e53935;
          }
        }
      }

      &.next-btn {
        .image {
          background-color: #e3f2fd;
        }

        &.active {
          .image {
            background-color: #ffd84e;
          }
        }
      }

      .reading-btn {
        width: 70px;
        height: 70px;
        border-radius: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ffd84e;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

        image {
          width: 40px;
          height: 40px;
        }

        &-reading {
          position: relative;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px; // 控制柱子之间的间距

          .bar {
            width: 6px;
            height: 20px;
            background: #ffffff;
            border-radius: 4px;
            animation: recording 1s ease-in-out infinite;

            &.left {
              animation-delay: -0.4s;
            }

            &.middle {
              animation-delay: -0.2s;
            }

            &.right {
              animation-delay: 0s;
            }
          }
        }

        &.blinking {
          animation: blinking 1.5s ease-in-out infinite;
          background-color: #ffd84e;
        }
      }

      .image {
        height: 32px;
        width: 32px;
        border-radius: 50%;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(250, 199, 73, 1);

        &.disabled {
          background-color: rgba(196, 196, 196, 1);
          color: rgba(212, 212, 212, 1);
        }

        image {
          width: 16px;
          height: 16px;
          display: flex;
        }
      }

      text {
        font-size: 12px;
        color: #333;
      }
    }
  }
}

.ipad-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  min-width: 20px;
  height: 20px;
  background-color: #bedc75;
  color: white;
  border-radius: 10px;
  font-size: 12px;
  padding: 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

@media screen and (min-width: 768px) {
  .content-wrapper {
    max-width: 720rpx;
    margin: 0 auto;
  }

  .article-container {
    .content-wrapper {
      .article-cover {
        height: 500rpx;

        .article-title-overlay {
          font-size: 42rpx;
        }
      }

      .article-title {
        font-size: 42rpx;
      }

      .article-content {
        .sentence,
        .active-sentence {
          font-size: 32rpx !important;
        }

        .sentence {
          padding: 10rpx;
        }

        .active-sentence {
          padding: 10rpx;
          border-bottom: 4rpx solid #ffd600;
        }
      }
    }
  }

  .control-panel {
    padding: 10rpx 10rpx;

    .reading-controls {
      .control-btn {
        padding: 20rpx;

        .image {
          width: 40px;
          height: 40px;

          image,
          .play-icon {
            width: 20px;
            height: 20px;
          }
        }

        .reading-btn {
          width: 70px;
          height: 70px;
        }

        text {
          font-size: 16px;
        }
      }
    }
  }

  .ipad-badge {
    top: -14px !important;
    right: -18px !important;
    min-width: 24px !important;
    height: 24px !important;
    font-size: 14px !important;
    border-radius: 20px !important;
  }
}

@keyframes rotate {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }
  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

@keyframes recording {
  0%,
  100% {
    height: 16px;
  }
  50% {
    height: 24px;
  }
}

@keyframes blinking {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(255, 216, 78, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(255, 216, 78, 0.8);
  }
}

.first-sentence {
  padding-left: 0 !important;
}

.first-token {
  padding-left: 0 !important;
}

.active-sentence {
  background-color: rgba(255, 214, 0, 0.2);
  color: #000000;
  font-weight: 440;
  border-bottom: 4rpx solid #ffd600;
}

.token-correct {
  color: #4caf50;
  font-weight: inherit;
  display: inline;
  padding-left: 0;
  padding-right: 0;
}

.token-error {
  color: #f44336;
  font-weight: inherit;
  display: inline;
  padding-left: 0;
  padding-right: 0;
}

.token-missed {
  font-weight: inherit;
  display: inline;
  padding-left: 0;
  padding-right: 0;
}

.token-space {
  /* 保持空格原样 */
  font-weight: inherit;
  display: inline;
  padding-left: 0;
  padding-right: 0;
}

.my-btn {
  &.blinking {
    animation: blinking 1.5s ease-in-out infinite;
    background-color: #ffd84e;
  }
}

// HTML标签样式类
.tag-b {
  font-weight: 780;
}

// 为未来的其他标签预留样式
.tag-i {
  font-style: italic;
}

.tag-u {
  text-decoration: underline;
}
</style>
