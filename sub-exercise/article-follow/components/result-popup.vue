<template>
  <uni-popup ref="popup" type="center" :maskClick="false">
    <view class="result-popup" v-if="visible">
      <view class="result-header">
        <text class="result-title">跟读完成</text>
      </view>

      <!-- 表情图标展示 -->
      <view class="emotion-section">
        <image
          :src="emotionImageSrc"
          class="emotion-image"
          @tap="playResultAudio"
        ></image>
      </view>

      <!-- 评分结果 -->
      <view class="score-section">
        <view class="score-value">{{ score }}分</view>
        <view class="score-tips">{{ getScoreTips(score) }}</view>
      </view>

      <!-- 按钮区域 -->
      <view class="popup-footer">
        <button class="close-btn" @click="handleClose">关闭</button>
        <button class="continue-btn" @click="handleContinue">继续练习</button>
      </view>
    </view>
  </uni-popup>
</template>

<script lang="ts" setup>
// @ts-ignore
import { AudioPlayer } from "@/sub-exercise/utils/audio-player";
import uniPopup from "@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";

const props = defineProps<{
  score: number;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: "close"): void;
  (e: "continue"): void;
}>();

// 弹窗引用
const popup = ref();

// 音频播放器
const audioPlayer = new AudioPlayer();

// 获取评分提示文字
const getScoreTips = (score: number) => {
  if (score >= 90) {
    return "太棒了！发音非常标准";
  } else if (score >= 60) {
    return "不错！继续加油";
  } else {
    return "请继续练习，相信你会更好";
  }
};

// 基于分数计算表情和音频
const emotionData = computed(() => {
  const score = props.score;

  if (score >= 90) {
    return {
      imageSrc: "/static/icons/excellent.png",
      audioSrc: "/static/sounds/excellent2.mp3",
    };
  } else if (score >= 60) {
    return {
      imageSrc: "/static/icons/nice.png",
      audioSrc: "/static/sounds/nice.mp3",
    };
  } else {
    return {
      imageSrc: "/static/icons/keep-it-up.png",
      audioSrc: "/static/sounds/keep-it-up.mp3",
    };
  }
});

// 简化访问
const emotionImageSrc = computed(() => emotionData.value.imageSrc);
const emotionAudioSrc = computed(() => emotionData.value.audioSrc);

// 播放结果音频
const playResultAudio = () => {
  if (emotionAudioSrc.value) {
    audioPlayer.play(emotionAudioSrc.value, {
      onStart: () => {
        console.log("结果音频开始播放");
      },
      onEnd: () => {
        console.log("结果音频播放结束");
      },
      onError: () => {
        console.error("结果音频播放失败");
      },
    });
  }
};

// 关闭弹窗
const handleClose = () => {
  popup.value?.close();
  setTimeout(() => {
    emit("close");
  }, 200);
};

// 继续练习
const handleContinue = () => {
  popup.value?.close();
  setTimeout(() => {
    emit("continue");
  }, 200);
};

// 监听visible属性变化
watch(
  () => props.visible,
  (newValue) => {
    console.log("ResultPopup visible属性变化:", newValue);
    if (newValue) {
      console.log("尝试打开弹窗");
      // 确保popup已经初始化
      if (popup.value) {
        popup.value.open();
        console.log("弹窗已打开");
        // 延迟播放音频，确保弹窗已渲染
        setTimeout(() => {
          playResultAudio();
        }, 300);
      } else {
        console.error("弹窗引用未初始化!");
      }
    } else {
      console.log("尝试关闭弹窗");
      if (popup.value) {
        popup.value.close();
        console.log("弹窗已关闭");
      } else {
        console.error("弹窗引用未初始化!");
      }
    }
  }
);

// 组件挂载时检查visible初始值
onMounted(() => {
  console.log("ResultPopup组件挂载完成，初始visible值:", props.visible);
  console.log("popup引用:", popup.value);

  // 延迟一下确保引用已初始化
  setTimeout(() => {
    console.log("延迟后popup引用:", popup.value);
    if (props.visible) {
      if (popup.value) {
        popup.value.open();
        console.log("初始化时弹窗已打开");
        setTimeout(() => {
          playResultAudio();
        }, 300);
      } else {
        console.error("初始化时popup引用未初始化!");
      }
    }
  }, 100);
});

// 组件卸载时清理
onUnmounted(() => {
  audioPlayer.dispose();
});
</script>

<style lang="scss" scoped>
.result-popup {
  width: 650rpx;
  background: #fff;
  border-radius: 30rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0 50rpx;
}

.result-header {
  margin-bottom: 20rpx;
  text-align: center;
}

.result-title {
  font-size: 42rpx;
  font-weight: bold;
  color: #333;
}

.emotion-section {
  margin: 30rpx 0;
  text-align: center;
}

.emotion-image {
  width: 240rpx;
  height: 240rpx;
  transition: all 0.2s;
}

.emotion-image:active {
  transform: scale(0.95);
}

.score-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20rpx 0 40rpx;
}

.score-value {
  font-size: 80rpx;
  font-weight: bold;
  color: #ff9642;
  margin-bottom: 20rpx;
}

.score-tips {
  font-size: 32rpx;
  color: #666;
  text-align: center;
}

.popup-footer {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  width: 90%;
}

.close-btn,
.continue-btn {
  flex: 1;
  height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.close-btn {
  background-color: #f5f5f5;
  color: #666;
}

.continue-btn {
  background: linear-gradient(to right, #ffe251, #ffcc00);
  color: #333;
  font-weight: 500;
}

// 平板设备适配
@media screen and (min-width: 768px) {
  .result-popup {
    width: 500px;
  }

  .emotion-image {
    width: 180px;
    height: 180px;
  }
}
</style>
