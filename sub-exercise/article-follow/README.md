# 口语评测 WebSocket 接口使用说明

## 接口地址

```
ws://your-domain.com/api/wbclass/oral-eval
```

## 调用流程

1. 前端与服务端建立 WebSocket 连接
2. 发送初始化参数（文本消息）
3. 发送音频数据（二进制消息）
4. 发送结束标记（文本消息）
5. 接收评测结果（文本消息）

## 消息格式

### 1. 初始化消息（前端发送）

```json
{
  "type": "init",
  "language": "en", // 语言，可选值：en（英文）、cn（中文）
  "text": "hello world", // 评测文本
  "userId": "user123" // 用户ID，可选
}
```

### 2. 音频数据（前端发送）

直接发送二进制音频数据，要求：

- 采样率：16kHz
- 位深度：16bit
- 声道：单声道
- 格式：mp3（推荐）、speex 或 pcm

### 3. 结束标记（前端发送）

```json
{
  "type": "end"
}
```

### 4. 评测结果（服务端返回）

成功：

```json
{
  "type": "result",
  "success": true,
  "data": {
    "result": {
      // 具体评测结果，结构复杂，详见云声API文档
    },
    "area": "sh",
    "time": "1551409712576231666",
    "sid": "f4376e83-7ad0-4635-9812-bec949a2fa27",
    "errcode": "0",
    "errmsg": "ok"
  },
  "audioUrl": "https://edu.hivoice.cn/WebAudio-1.0-SNAPSHOT/audio/play/f4376e83-7ad0-4635-9812-bec949a2fa27/1551409712576231666/sh"
}
```

失败：

```json
{
  "type": "result",
  "success": false,
  "message": "评测失败: 错误描述"
}
```

错误消息：

```json
{
  "type": "error",
  "message": "错误描述"
}
```

## 前端示例代码

```javascript
// 建立WebSocket连接
const socket = new WebSocket("ws://your-domain.com/api/wbclass/oral-eval");

// 连接成功回调
socket.onopen = function (event) {
  console.log("WebSocket连接已建立");

  // 发送初始化参数
  const initParams = {
    type: "init",
    language: "en",
    text: "hello world",
    userId: "user123",
  };
  socket.send(JSON.stringify(initParams));
};

// 接收消息回调
socket.onmessage = function (event) {
  // 文本消息
  if (typeof event.data === "string") {
    const message = JSON.parse(event.data);

    if (message.type === "init" && message.success) {
      // 初始化成功，开始发送音频数据
      startSendingAudioData();
    } else if (message.type === "result") {
      // 收到评测结果
      if (message.success) {
        // 评测成功
        displayResult(message.data);
        playAudio(message.audioUrl);
      } else {
        // 评测失败
        showError(message.message);
      }
    } else if (message.type === "error") {
      // 错误消息
      showError(message.message);
    }
  }
};

// 错误回调
socket.onerror = function (error) {
  console.error("WebSocket错误:", error);
};

// 连接关闭回调
socket.onclose = function (event) {
  console.log("WebSocket连接已关闭:", event.code, event.reason);
};

// 发送音频数据示例（使用MediaRecorder）
function startSendingAudioData() {
  // 创建音频流
  navigator.mediaDevices
    .getUserMedia({ audio: true })
    .then(function (stream) {
      // 配置MediaRecorder，使用16kHz采样率
      const audioContext = new AudioContext({ sampleRate: 16000 });
      const mediaStreamSource = audioContext.createMediaStreamSource(stream);
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "audio/mp3",
        audioBitsPerSecond: 16000,
      });

      // 数据可用时发送
      mediaRecorder.ondataavailable = function (e) {
        if (e.data.size > 0) {
          socket.send(e.data);
        }
      };

      // 停止录音时发送结束标记
      mediaRecorder.onstop = function () {
        const endMessage = { type: "end" };
        socket.send(JSON.stringify(endMessage));
      };

      // 开始录音，每100ms发送一次数据
      mediaRecorder.start(100);

      // 录音5秒后停止（示例）
      setTimeout(function () {
        mediaRecorder.stop();
      }, 5000);
    })
    .catch(function (err) {
      console.error("获取麦克风失败:", err);
    });
}

// 显示评测结果
function displayResult(result) {
  console.log("评测结果:", result);
  // 根据不同的评测类型展示不同的UI
}

// 播放录音
function playAudio(url) {
  const audio = new Audio(url);
  audio.play();
}

// 显示错误
function showError(message) {
  console.error("错误:", message);
}
```

## 注意事项

1. 前端录制音频时，需要确保符合以下要求：

   - 采样率：16kHz
   - 位深度：16bit
   - 声道：单声道
   - 格式：mp3（推荐）、speex 或 pcm

2. 音频数据建议分块发送，每段不超过 100ms 的数据。

3. 评测结果中的具体字段含义，请参考云声口语评测 API 文档：https://ai.unisound.com/doc/sacalleval/WebSocket.html
