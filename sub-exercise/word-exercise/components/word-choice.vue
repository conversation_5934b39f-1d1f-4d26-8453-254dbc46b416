<template>
  <view class="word-choice">
    <!-- 添加隐藏的canvas用于测量文本 -->
    <canvas
      canvas-id="word-choice-canvas"
      style="position: absolute; opacity: 0; width: 1px; height: 1px"
    ></canvas>

    <!-- 根据题型加载不同组件 -->
    <picture-word-selection
      v-if="questionType === QuestionType.PICTURE_WORD_SELECTION"
      ref="pictureWordSelectionRef"
      :current-word="currentWord"
      @on-answer-status-change="handleAnswerStatusChange"
      @on-audio-status-change="handleAudioStatusChange"
      @on-can-go-next-change="handleCanGoNextChange"
    />

    <audio-picture-selection
      v-else-if="questionType === QuestionType.AUDIO_PICTURE_SELECTION"
      ref="audioPictureSelectionRef"
      :current-word="currentWord"
      @on-answer-status-change="handleAnswerStatusChange"
      @on-audio-status-change="handleAudioStatusChange"
      @on-can-go-next-change="handleCanGoNextChange"
    />

    <word-picture-selection
      v-else-if="questionType === QuestionType.WORD_PICTURE_SELECTION"
      ref="wordPictureSelectionRef"
      :current-word="currentWord"
      @on-answer-status-change="handleAnswerStatusChange"
      @on-audio-status-change="handleAudioStatusChange"
      @on-can-go-next-change="handleCanGoNextChange"
    />

    <audio-sentence-selection
      v-else-if="questionType === QuestionType.AUDIO_SENTENCE_SELECTION"
      ref="audioSentenceSelectionRef"
      :current-word="currentWord"
      @on-answer-status-change="handleAnswerStatusChange"
      @on-audio-status-change="handleAudioStatusChange"
      @on-can-go-next-change="handleCanGoNextChange"
    />

    <!-- 底部按钮 -->
    <view class="bottom-area">
      <view
        class="next-btn commonBtn"
        :class="{
          active: canGoToNext,
          'audio-playing': isAudioPlaying,
          disabled: !canGoToNext,
        }"
        @tap="handleNextWord"
      >
        <text>{{ isAudioPlaying ? "请听完发音..." : "下一题" }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {
  disposeSounds,
  preloadSounds,
} from "@/sub-exercise/utils/sound-effects";
import type { WordInfo } from "@/types/homework-exercise";
import { onMounted, onUnmounted, ref, watch } from "vue";
import AudioPictureSelection from "./AudioPictureSelection.vue";
import AudioSentenceSelection from "./AudioSentenceSelection.vue";
import PictureWordSelection from "./PictureWordSelection.vue";
import WordPictureSelection from "./WordPictureSelection.vue";

// 定义题目类型枚举
enum QuestionType {
  PICTURE_WORD_SELECTION = 2, // 看图选词
  AUDIO_PICTURE_SELECTION = 3, // 听词选图
  WORD_PICTURE_SELECTION = 4, // 看词选图
  AUDIO_SENTENCE_SELECTION = 5, // 听句选图
  WORD_PHONICS = 6, // 自然拼读挖空题
}

const props = defineProps<{
  currentWord: WordInfo;
  questionType: number;
}>();

const emit = defineEmits<{
  (e: "onWordComplete", answer: string, isCorrect: boolean): void;
}>();

// 组件引用
const pictureWordSelectionRef = ref<InstanceType<
  typeof PictureWordSelection
> | null>(null);
const audioPictureSelectionRef = ref<InstanceType<
  typeof AudioPictureSelection
> | null>(null);
const wordPictureSelectionRef = ref<InstanceType<
  typeof WordPictureSelection
> | null>(null);
const audioSentenceSelectionRef = ref<InstanceType<
  typeof AudioSentenceSelection
> | null>(null);

// 全局状态管理
const isAudioPlaying = ref(false);
const isAnswerCorrect = ref(false);
const canGoToNext = ref(false);

// 子组件状态变化处理
const handleAudioStatusChange = (isPlaying: boolean) => {
  isAudioPlaying.value = isPlaying;
};

const handleAnswerStatusChange = (correct: boolean, finished: boolean) => {
  isAnswerCorrect.value = correct;
};

// 处理子组件可进入下一题状态变化
const handleCanGoNextChange = (value: boolean) => {
  console.log("子组件通知可以进入下一题状态变化:", value);
  canGoToNext.value = value;
};

// 在组件数据变化或重置时重置状态
watch(
  () => props.currentWord,
  () => {
    // 新的单词，重置状态
    canGoToNext.value = false;
  }
);

// 确保在子组件重置时，父组件状态也重置
const resetComponents = () => {
  // ... 现有重置代码 ...
  canGoToNext.value = false;
};

// 获取当前活动的子组件
const getActiveComponent = () => {
  switch (props.questionType) {
    case QuestionType.PICTURE_WORD_SELECTION:
      return pictureWordSelectionRef.value;
    case QuestionType.AUDIO_PICTURE_SELECTION:
      return audioPictureSelectionRef.value;
    case QuestionType.WORD_PICTURE_SELECTION:
      return wordPictureSelectionRef.value;
    case QuestionType.AUDIO_SENTENCE_SELECTION:
      return audioSentenceSelectionRef.value;
    default:
      return null;
  }
};

// 处理下一个单词
const handleNextWord = () => {
  // 如果不能前往下一题，直接返回
  if (!canGoToNext.value) {
    uni.showToast({
      title: "请先完成当前题目",
      icon: "none",
    });
    return;
  }

  // 设置状态为不可前往下一题，避免重复点击
  canGoToNext.value = false;

  try {
    // 如果音频正在播放或没有正确答案，不允许进入下一题
    if (isAudioPlaying.value || !isAnswerCorrect.value) {
      if (isAudioPlaying.value) {
        uni.showToast({
          title: "请先听完发音",
          icon: "none",
        });
      } else if (!isAnswerCorrect.value) {
        uni.showToast({
          title: "请选择正确的答案",
          icon: "none",
        });
      }
      return;
    }

    // 获取当前活动的组件
    const activeComponent = getActiveComponent();
    if (!activeComponent) {
      console.error("无法获取当前活动组件");
      return;
    }

    // 获取当前状态
    const status = activeComponent.getStatus();
    if (!status) {
      console.error("无法获取当前状态");
      return;
    }

    // 记录当前答案信息
    const answer = status.firstAnswer || "";
    const isCorrect = !!status.firstAnswerCorrect;

    // 重置所有状态
    activeComponent.resetSelectionState();

    // 释放音效资源
    disposeSounds();

    // 重置全局状态
    isAnswerCorrect.value = false;
    isAudioPlaying.value = false;

    // 传递答案信息给父组件
    emit("onWordComplete", answer, isCorrect);
  } catch (error) {
    console.error("处理下一个单词时发生错误:", error);
    uni.showToast({
      title: "操作异常，请重试",
      icon: "none",
    });
  }
};

// 添加 onMounted 生命周期钩子，确保组件首次加载时自动播放
onMounted(() => {
  console.log("开始预加载音效");
  preloadSounds();

  // 延迟一下确保子组件已经挂载
  setTimeout(() => {
    if (props.currentWord) {
      try {
        // 根据题型获取对应的组件引用
        let componentRef: any = null;

        switch (props.questionType) {
          case QuestionType.PICTURE_WORD_SELECTION:
            componentRef = pictureWordSelectionRef.value;
            break;
          case QuestionType.AUDIO_PICTURE_SELECTION:
            componentRef = audioPictureSelectionRef.value;
            break;
          case QuestionType.WORD_PICTURE_SELECTION:
            componentRef = wordPictureSelectionRef.value;
            break;
          case QuestionType.AUDIO_SENTENCE_SELECTION:
            componentRef = audioSentenceSelectionRef.value;
            break;
        }

        // 如果组件存在，尝试播放音频
        if (componentRef) {
          if (props.questionType === QuestionType.AUDIO_SENTENCE_SELECTION) {
            // 听句选图组件使用 playSentenceAudio 方法
            if (typeof componentRef.playSentenceAudio === "function") {
              componentRef.playSentenceAudio();
            }
          } else {
            // 其他组件使用 playWordAudio 方法
            if (typeof componentRef.playWordAudio === "function") {
              componentRef.playWordAudio();
            }
          }
        }
      } catch (error) {
        console.error("自动播放音频失败:", error);
      }
    }
  }, 300);
});

// 组件卸载时清理资源
onUnmounted(() => {
  console.log("组件卸载：清理音频资源");
  disposeSounds();
});
</script>

<style lang="scss">
.word-choice {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  width: 100%;
  overflow-y: auto;
  position: relative;

  /* iPad和平板设备的全局调整 */
  @media screen and (min-width: 768px) {
    padding: 20px;
    justify-content: space-between;
  }

  .bottom-area {
    margin-top: 10px;
    margin-bottom: 10px;
    padding: 10px 0;
    display: flex;
    justify-content: center;
    flex-shrink: 0;

    .next-btn {
      background-color: #ffd84e;
      width: 170px;
      height: 40px;
      border-radius: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      @media screen and (min-width: 768px) {
        width: 340px;
        height: 80px;
        border-radius: 50px;
      }

      text {
        font-size: 16px;
        color: rgba(78, 41, 13, 1);
        transition: all 0.3s ease;
        @media screen and (min-width: 768px) {
          font-size: 32px;
        }
      }

      &.active {
        background-color: #ffd84e;
        box-shadow: 0 4px 8px rgba(255, 211, 82, 0.3);

        &:active {
          transform: scale(0.97);
          box-shadow: 0 2px 4px rgba(255, 211, 82, 0.2);
        }
      }

      &.audio-playing,
      &:not(.active) {
        background-color: rgba(255, 216, 78, 0.5);
        pointer-events: none;
        opacity: 0.8;

        text {
          color: rgba(78, 41, 13, 0.3);
        }
      }
    }
  }
}
</style>
