<template>
  <view class="picture-word-selection">
    <!-- 题目区域 - 看图选词 -->
    <view class="question-pic">
      <view class="question-image-wrapper">
        <image
          class="question-image"
          :src="optimizedImageUrl"
          mode="aspectFit"
        />
      </view>
      <view class="sound-btn" @tap="playWordAudio">
        <text
          class="iconfont icon-sound"
          :class="{ playing: isAudioPlaying }"
        ></text>
      </view>
    </view>

    <!-- 文字选项 -->
    <view class="text-options">
      <view
        v-for="(option, index) in currentWord.textOptions"
        :key="index"
        class="text-option"
        :class="{
          selected: selectedOption === option,
          'option-correct':
            selectedOption === option && isCorrectOption(option),
          'option-error': selectedOption === option && !isCorrectOption(option),
        }"
        @tap="handleSelect(option, index)"
      >
        <view
          class="option-indicator"
          v-if="selectedOption === option && isCorrectOption(option)"
        >
          <image src="/static/good.png"></image>
        </view>
        <text>{{ option }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { AudioPlayer } from "@/sub-exercise/utils/audio-player";
import { optimizeImageUrl } from "@/sub-exercise/utils/image-optimizer";
import { playSound } from "@/sub-exercise/utils/sound-effects";
import type { WordInfo } from "@/types/homework-exercise";
import { computed, onUnmounted, ref, watch } from "vue";

const props = defineProps<{
  currentWord: WordInfo;
}>();

const emit = defineEmits<{
  (e: "onWordComplete", answer: string, isCorrect: boolean): void;
  (e: "onAudioStatusChange", isPlaying: boolean): void;
  (e: "onAnswerStatusChange", isCorrect: boolean, isFinished: boolean): void;
  (e: "onCanGoNextChange", canGoNext: boolean): void;
}>();

// 音频播放状态
const isAudioPlaying = ref(false);

// 构建音频播放器实例
const wordAudioPlayer = new AudioPlayer();

// UI状态变量
const selectedOption = ref<string | null>(null);
const isAnswerCorrect = ref(false);
const isAudioFinished = ref(false);
// 添加新的状态变量控制是否可以进入下一题
const canGoToNext = ref(false);

// 记录第一次选择的答案和正确性（用于统计）
const firstAnswer = ref<string | null>(null);
const firstAnswerCorrect = ref<boolean | null>(null);

// 重置所有选择和音频状态
const resetSelectionState = () => {
  // 停止所有音频播放
  wordAudioPlayer.stop();

  // 重置选择状态
  selectedOption.value = null;
  isAnswerCorrect.value = false;
  firstAnswer.value = null;
  firstAnswerCorrect.value = null;

  // 重置音频状态
  isAudioFinished.value = false;
  isAudioPlaying.value = false;

  // 重置下一题状态
  canGoToNext.value = false;
  // 通知父组件
  emit("onCanGoNextChange", false);
};

// 播放单词音频的函数
const playWordAudio = () => {
  const audioUrl = props.currentWord?.audioUrl;
  if (!audioUrl) {
    uni.showToast({
      title: "单词音频不存在",
      icon: "none",
    });
    // 如果没有音频，设置为已完成状态
    isAudioFinished.value = true;
    // 没有音频可以直接进入下一题
    if (isAnswerCorrect.value) {
      canGoToNext.value = true;
      emit("onCanGoNextChange", true);
    }
    return;
  }

  // 如果已经在播放中，不重复播放
  if (isAudioPlaying.value) return;

  // 设置播放状态
  isAudioPlaying.value = true;
  emit("onAudioStatusChange", true);

  // 使用 AudioPlayer 播放音频
  wordAudioPlayer.play(audioUrl, {
    onStart: () => {
      console.log("单词音频开始播放");
    },
    onEnd: () => {
      isAudioPlaying.value = false;
      emit("onAudioStatusChange", false);
      // 播放结束后设置为已完成状态
      isAudioFinished.value = true;

      // 如果答案正确且音频播放完成，此时可以进入下一题
      if (isAnswerCorrect.value) {
        canGoToNext.value = true;
        emit("onCanGoNextChange", true);
      }
    },
    onError: (error) => {
      console.error("单词音频播放失败:", error);
      isAudioPlaying.value = false;
      emit("onAudioStatusChange", false);
      // 播放出错也设置为已完成状态
      isAudioFinished.value = true;
      uni.showToast({
        title: "单词音频播放失败",
        icon: "none",
      });

      // 即使播放失败，也允许进入下一题
      if (isAnswerCorrect.value) {
        canGoToNext.value = true;
        emit("onCanGoNextChange", true);
      }
    },
  });
};

// 监听 currentWord 的变化
watch(
  () => props.currentWord,
  (newWord, oldWord) => {
    // 当单词变化时，重置状态
    resetSelectionState();
  }
);

// 处理选项选择
const handleSelect = (option: string, index: number) => {
  // 如果已经有选中的选项，则不再响应
  if (selectedOption.value) return;

  // 判断选项是否正确
  const isCorrect = isCorrectOption(option);

  // 记录第一次选择
  if (firstAnswer.value === null) {
    firstAnswer.value = option;
    firstAnswerCorrect.value = isCorrect;
  }

  // 先设置选中状态，避免重复触发
  selectedOption.value = option;
  isAnswerCorrect.value = isCorrect;

  // 通知父组件答案状态
  emit("onAnswerStatusChange", isCorrect, isCorrect);

  if (isCorrect) {
    // 选择正确，播放成功音效
    console.log("准备播放成功音效");
    try {
      playSound("success");
    } catch (error) {
      console.error("播放成功音效失败:", error);
    }

    // 看图选词模式下需要先播放完音频后才能点击下一题
    if (props.currentWord.audioUrl) {
      // 默认不能点击下一题
      isAudioFinished.value = false;

      setTimeout(() => {
        try {
          playWordAudio();
        } catch (error) {
          console.error("播放单词音频失败:", error);
          // 如果播放失败，也允许点击下一题
          isAudioFinished.value = true;
          canGoToNext.value = true;
          emit("onCanGoNextChange", true);
        }
      }, 500);
    } else {
      // 没有音频可以直接点击下一题
      isAudioFinished.value = true;
      canGoToNext.value = true;
      emit("onCanGoNextChange", true);
    }
  } else {
    // 选择错误，播放错误音效
    console.log("准备播放错误音效");
    try {
      playSound("error");
    } catch (error) {
      console.error("播放错误音效失败:", error);
    }

    // 错误选择后，延迟重置选择状态以便用户可以再次选择
    setTimeout(() => {
      selectedOption.value = null;
      isAnswerCorrect.value = false;
      // 通知父组件答案状态变化
      emit("onAnswerStatusChange", false, false);
    }, 500);
  }
};

// 判断选项是否正确
const isCorrectOption = (option: string) => {
  return option === props.currentWord.word;
};

// 公开方法：获取当前答题状态
const getStatus = () => {
  return {
    selectedOption: selectedOption.value,
    isAnswerCorrect: isAnswerCorrect.value,
    isAudioFinished: isAudioFinished.value,
    firstAnswer: firstAnswer.value,
    firstAnswerCorrect: firstAnswerCorrect.value,
    canGoToNext: canGoToNext.value,
  };
};

// 暴露方法给父组件
defineExpose({
  resetSelectionState,
  playWordAudio,
  getStatus,
});

// 优化后的图片URL
const optimizedImageUrl = computed(() => {
  if (!props.currentWord?.imageUrl) return "";
  return optimizeImageUrl(props.currentWord.imageUrl);
});

// 增加组件卸载时释放音频资源
onUnmounted(() => {
  // 停止播放并释放资源
  if (wordAudioPlayer) {
    wordAudioPlayer.stop();
    wordAudioPlayer.dispose();
  }
});
</script>

<style lang="scss">
.picture-word-selection {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  .question-pic {
    margin-top: 38px;
    width: 260px;
    height: 260px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    align-self: center;

    .sound-btn {
      margin-top: 16px;
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 211, 82, 0.1);
      border-radius: 50%;
      box-shadow: 0 4rpx 12rpx rgba(208, 118, 28, 0.15);
      transition: all 0.3s ease;
      border: 1px solid rgba(208, 118, 28, 0.2);

      &:active {
        transform: scale(0.95);
        background-color: rgba(255, 211, 82, 0.3);
      }

      .iconfont {
        font-size: 40rpx;
        color: #d0761c;
        opacity: 0.99;
        transition: all 0.3s ease;
      }

      .playing {
        animation: soundWave 1s ease-in-out infinite;
      }
    }

    .question-image-wrapper {
      width: 230px;
      aspect-ratio: 1;
      position: relative;
      margin-bottom: 5px;
      border-radius: 8px;
      overflow: hidden;
      background: #fff;
      margin: 5px 0;

      .question-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 16px;
      }
    }
  }

  .text-options {
    margin-top: 40px;
    padding: 0;
    width: 100%;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    .text-option {
      position: relative;
      margin-bottom: 10px;
      background: #f5f6fa;
      width: 90%;
      height: 45px;
      border-radius: 8px;
      text-align: center;
      font-size: 24px;
      color: #333;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid transparent;

      .option-indicator {
        position: absolute;
        right: 0;
        top: -9px;
        width: 33px;
        height: 33px;
        display: block;

        image {
          width: 100%;
          height: 100%;
          display: block;
        }
      }

      &.selected {
        background: rgba(255, 211, 82, 0.239);
        transform: scale(0.98);
        border-color: rgba(196, 146, 98, 1);
      }

      &.option-correct {
        background: rgba(76, 175, 80, 0.1);
        border-color: rgba(76, 175, 80, 0.8);
      }

      &.option-error {
        background: rgba(244, 67, 54, 0.1);
        border-color: rgba(244, 67, 54, 0.8);
      }
    }
  }
}

/* iPad 和其他平板设备 */
@media screen and (min-width: 768px) {
  .picture-word-selection {
    .question-pic {
      width: 400px;
      height: 400px;
    }

    .text-options {
      .text-option {
        margin-bottom: 20px;
      }
    }
  }
}

@media screen and (max-width: 320px) {
  .text-options {
    .text-option {
      height: 35px !important;
    }
  }
}

@keyframes soundWave {
  0% {
    opacity: 0.99;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.99;
    transform: scale(1);
  }
}
</style>
