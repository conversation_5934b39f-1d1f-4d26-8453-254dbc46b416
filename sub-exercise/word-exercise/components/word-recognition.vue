<template>
  <view class="word-recognition">
    <!-- 单词展示区域 -->
    <view class="word-card" v-if="currentWord">
      <!-- 添加左右箭头导航 -->
      <view class="word-navigation">
        <view class="nav-arrow left" @tap="handlePrevWord">
          <text class="iconfont icon-left-f"></text>
        </view>
        <view class="word-content">
          <text class="word">{{ currentWord.word }}</text>
          <view class="word-info">
            <text class="part-of-speech">{{ currentWord.partOfSpeech }}</text>
            <text class="phonetic" v-if="currentWord.phonetic?.trim()"
              >/{{ currentWord.phonetic }}/</text
            >
            <view class="sound-btn" @tap="playAudio">
              <text
                class="iconfont icon-sound"
                :class="{ playing: isAudioPlaying }"
              ></text>
            </view>
          </view>
        </view>
        <view class="nav-arrow right" @tap="handleNavNextWord">
          <text class="iconfont icon-right-f"></text>
        </view>
      </view>
    </view>
    <!-- 示例句子和图片 -->
    <view
      class="example-content"
      v-if="localMode !== 'recognition' && currentWord?.exampleSentences?.[0]"
    >
      <view class="example-image-wrapper">
        <image
          class="example-image"
          :src="currentWord.exampleSentences[0].imageUrl"
        ></image>
      </view>
      <view class="example-sentence">
        <mp-html :content="highlightedSentence" />
        <view class="audio-btn small" @tap="playExampleAudio">
          <text
            class="iconfont icon-sound"
            :class="{ playing: isExampleAudioPlaying }"
          ></text>
        </view>
      </view>
      <!-- 中文释义区域 -->
      <view
        v-if="localMode !== 'reading'"
        class="chinese-meaning"
        @tap="toggleChineseMeaning"
      >
        <text v-if="!showChineseMeaning" class="hint-text"
          >点击空白处显示中文释义</text
        >
        <text v-else class="meaning-text">{{
          currentWord.chineseMeaning
        }}</text>
      </view>
    </view>

    <!-- 底部区域 -->
    <view class="bottom-area">
      <!-- 认词状态 -->
      <view v-if="localMode === 'recognition'" class="bottom-buttons">
        <view class="btn forget" @tap="handleForget">
          <text class="emoji">😢</text>
          <text>忘记</text>
        </view>
        <view class="btn fuzzy" @tap="handleFuzzy">
          <text class="emoji">🤔</text>
          <text>模糊</text>
        </view>
        <view class="btn remember" @tap="handleRemember">
          <text class="emoji">😎</text>
          <text>记得</text>
        </view>
      </view>

      <!-- 学词状态 -->
      <view v-else-if="localMode === 'learning'" class="bottom-buttons">
        <view
          class="go-reading commonBtn"
          @tap="handleFollowRead"
          :class="{ disabled: !canFollowRead }"
        >
          <text>跟读单词</text>
        </view>
      </view>

      <view class="reading-section" v-else>
        <!-- 评分星星 -->
        <view class="score-stars">
          <template v-if="showScore">
            <view
              v-for="i in 3"
              :key="i"
              class="star"
              :class="{ active: i <= starScore }"
            >
              <image
                :src="
                  i <= starScore ? '/static/star-a.png' : '/static/star.png'
                "
              ></image>
            </view>
          </template>
        </view>

        <!-- 固定高度的提示信息容器 -->
        <view class="prompt-message-container">
          <!-- 上传状态提示 -->
          <view class="prompt-message" v-show="promptState === 'uploading'">
            <text class="upload-hint">正在上传录音，请稍候...</text>
          </view>

          <!-- 评估状态提示 -->
          <view class="prompt-message" v-show="promptState === 'evaluating'">
            <text class="evaluate-hint">正在评估发音，请稍候...</text>
          </view>

          <!-- 下一步提示 -->
          <view class="prompt-message" v-show="promptState === 'complete'">
            <text class="next-hint">完成跟读，请点击"下一个"按钮</text>
          </view>

          <!-- 准备录音提示 -->
          <view class="prompt-message" v-show="promptState === 'ready'">
            <text>请点击下方按钮开始跟读</text>
          </view>

          <!-- 默认占位消息 -->
          <view class="prompt-message empty" v-show="promptState === 'empty'">
            <text class="empty-hint"></text>
          </view>
        </view>

        <view class="reading-controls">
          <!-- 我的按钮 -->
          <view
            class="control-btn my-record"
            :class="{ disabled: readingState !== 'recorded' }"
          >
            <view
              class="image myplaybtn commonBtn"
              @tap="playMyRecord"
              :class="{ disabled: readingState !== 'recorded' }"
            >
              <image
                :class="playingRecord ? 'playImage' : ''"
                src="/static/play.png"
                mode="aspectFit"
              ></image>
            </view>
            <text>我的</text>
          </view>

          <!-- 跟读按钮 -->
          <view
            class="control-btn"
            :class="readingState"
            @tap="handleReadingClick"
          >
            <view
              class="reading-btn commonBtn"
              :class="{
                disabled: readingState !== 'recorded',
                blinking: readingState === 'ready_to_record',
              }"
            >
              <view
                class="reading-btn-reading"
                v-if="readingState === 'recording'"
              >
                <view class="bar left"></view>
                <view class="bar middle"></view>
                <view class="bar right"></view>
              </view>
              <image v-else :src="'/static/speak.png'" mode="aspectFit"></image>
            </view>
            <text>{{
              readingState === "ready"
                ? ""
                : readingState === "recording"
                ? "跟读中..."
                : readingState === "ready_to_record"
                ? "点击开始跟读"
                : readingState === "evaluating"
                ? "评估中..."
                : "录音完成"
            }}</text>
          </view>

          <!-- 下一个按钮 -->
          <view
            class="control-btn next-btn next"
            :class="{
              disabled:
                readingState !== 'recorded' ||
                starScore <= 0 ||
                !hasPlayedRecord,
              active:
                readingState === 'recorded' && starScore > 0 && hasPlayedRecord,
            }"
            @tap="handleNextWord"
          >
            <view
              class="image commonBtn"
              :class="{
                disabled:
                  readingState !== 'recorded' ||
                  starScore <= 0 ||
                  !hasPlayedRecord,
                active:
                  readingState === 'recorded' &&
                  starScore > 0 &&
                  hasPlayedRecord,
              }"
            >
              <image src="/static/rij.png" mode="aspectFit"></image>
            </view>
            <text>下一个</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { uploadFile } from "@/api/file";
import { evaluateWordPronunciation } from "@/api/word-api";
import type { WordInfo } from "@/types/homework-exercise";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";
import {
  scoreToStars,
  TRY_AGAIN_SCORE_THRESHOLD,
  TRY_AGAIN_SOUND,
} from "../score";
// 导入音频播放工具
import { uploadObjectLog } from "@/utils/log/uploader";
import { AudioPlayer } from "../../utils/audio-player";

const props = defineProps<{
  currentWord: WordInfo;
  showExample?: boolean;
  mode?: "recognition" | "learning" | "reading";
}>();

// 修改readingState的类型定义
type ReadingStateType =
  | "ready"
  | "recording"
  | "evaluating"
  | "recorded"
  | "ready_to_record";
const readingState = ref<ReadingStateType>("ready");

const emit = defineEmits<{
  (
    e: "onWordComplete",
    status: "forget" | "fuzzy" | "remember",
    score: number,
    readingAudioUrl: string
  ): void;
  (e: "onPrevWord"): void;
  (e: "onNextWord"): void;
}>();

// 在 setup 中添加评分状态
const totalScore = ref(0); // 评分,范围0-100
const readingAudioUrl = ref(""); // 跟读音频地址
const starScore = ref(0); // 评分,范围0-5
const showScore = ref(false); // 是否显示评分
const hasPlayedRecord = ref(false); // 添加状态：是否已播放过录音

const isRecording = ref(false);
const playingRecord = ref(false);
// 添加录音相关的状态
const recordPath = ref(""); // 录音文件路径
const recordDuration = ref(0); // 添加录音时长
const recorderManager = uni.getRecorderManager();

// 添加音量相关的状态
const silenceTimer = ref<any>(null);
const hasSound = ref(false);

// 本地状态控制
const localMode = ref(props.mode || "recognition");
// 本地记录用户选择的状态
const localRecognitionStatus = ref<"forget" | "fuzzy" | "remember" | null>(
  null
);

// 创建音频播放器实例
const wordPlayer = ref(new AudioPlayer());
const examplePlayer = ref(new AudioPlayer());
const recordPlayer = ref(new AudioPlayer());
const feedbackPlayer = ref(new AudioPlayer());

// 添加控制去跟读按钮状态
const canFollowRead = ref(false);

// 添加上传状态
const isUploading = ref(false);

// 修改音频播放状态
const isAudioPlaying = ref(false);

// 修改示例音频播放状态
const isExampleAudioPlaying = ref(false);

// 中文释义显示控制
const showChineseMeaning = ref(false);

// 修改 handleReadingClick 函数 - 增加对try-again音效播放状态的判断
const isPlayingTryAgain = ref(false); // 添加一个状态来跟踪try-again音效是否正在播放

// 在setup中添加超时定时器引用
const audioPlayTimeout = ref<any>(null);

const adjustFontSize = (text: string, initialFontSize = 14) => {
  // 获取设备屏幕宽度
  const screenWidth = uni.getSystemInfoSync().windowWidth;

  // 使用 canvas 来测量文本的宽度
  const canvas = uni.createCanvasContext("canvas-id"); // 生成一个canvas对象
  canvas.setFontSize(initialFontSize); // 设置初始字体大小
  canvas.fillText(text, 0, 0); // 绘制文本
  canvas.draw(true);

  // 使用 canvas.measureText() 获取文本的宽度
  const textWidth = canvas.measureText(text).width;
  console.log("adjustFontSize", screenWidth, textWidth);
  // 如果文本宽度超出屏幕宽度，按比例缩小字体
  if (textWidth > screenWidth) {
    const ratio = screenWidth / textWidth; // 计算缩放比例
    const newFontSize = initialFontSize * ratio; // 按比例缩小字体大小

    return newFontSize;
  }

  // 如果不超出屏幕宽度，返回原字体大小
  return initialFontSize;
};
// 计算高亮后的句子
const highlightedSentence = computed(() => {
  if (!props.currentWord?.exampleSentences?.[0]?.sentence) return "";

  const sentence = props.currentWord.exampleSentences[0].sentence;
  const word = props.currentWord.word;

  // 使用不区分大小写的正则表达式
  const regex = new RegExp(`(${word})`, "gi");

  // 获取设备信息
  const systemInfo = uni.getSystemInfoSync();
  const isIPad = systemInfo.windowWidth >= 768;

  // 根据设备类型设置基础字体大小
  let baseFontSize = isIPad ? 32 : 16;

  let fontSize = adjustFontSize(sentence);
  console.log("计算出来的字体大小", fontSize);
  fontSize = Math.max(fontSize, baseFontSize); // 使用基础字体大小作为最小值

  // 将句子转换为HTML字符串，给目标单词添加颜色样式
  const html = `<div style="font-size: ${fontSize}px; color: #333; line-height: 1.8; text-align: center; padding: 20rpx;">
    ${sentence.replace(
      regex,
      `<span style="color: #bc9469; display: inline; font-size: ${
        fontSize * 1.1
      }px;">$1</span>`
    )}
  </div>`;

  console.log(html);
  return html;
});

// 切换中文释义显示状态
const toggleChineseMeaning = () => {
  showChineseMeaning.value = !showChineseMeaning.value;
};

// 创建一个函数用于停止所有音频播放并重置状态
const stopAllAudio = () => {
  // 停止所有正在播放的音频
  wordPlayer.value.stop();
  examplePlayer.value.stop();
  recordPlayer.value.stop();
  feedbackPlayer.value.stop();

  // 重置音频播放状态
  isAudioPlaying.value = false;
  isExampleAudioPlaying.value = false;
  playingRecord.value = false;
  isPlayingTryAgain.value = false;

  // 清除可能存在的超时定时器
  if (audioPlayTimeout.value) {
    clearTimeout(audioPlayTimeout.value);
    audioPlayTimeout.value = null;
  }
};

// 切换到学词模式
const switchToLearningMode = () => {
  // 只有在 recognition 状态且已经选择了认识状态时才能进入 learning 状态
  if (localMode.value === "recognition" && localRecognitionStatus.value) {
    localMode.value = "learning";
  }
};

// 切换到跟读模式
const switchToReadingMode = () => {
  // 只有在 learning 状态时才能进入 reading 状态
  if (localMode.value === "learning") {
    localMode.value = "reading";
  }
};

// 修改 initAudioContext 函数
const initAudioContext = () => {
  try {
    // 销毁之前的音频实例
    wordPlayer.value.dispose();
    examplePlayer.value.dispose();
    recordPlayer.value.dispose();
  } catch (error: any) {
    console.error("初始化音频上下文失败:", error);
    uploadObjectLog("初始化音频上下文失败", {
      ...error,
    });
  }
};

// 修改 playAudio 函数
const playAudio = () => {
  if (!props.currentWord?.audioUrl) {
    uni.showToast({
      title: "音频地址不存在",
      icon: "none",
    });
    return;
  }

  try {
    // 停止所有正在播放的音频
    wordPlayer.value.stop();
    examplePlayer.value.stop();
    recordPlayer.value.stop();

    // 设置播放状态
    isAudioPlaying.value = true;

    // 使用AudioPlayer播放音频
    wordPlayer.value.play(props.currentWord.audioUrl, {
      onStart: () => {
        console.log("单词音频开始播放");
      },
      onEnd: () => {
        console.log("单词音频播放结束");
        isAudioPlaying.value = false;

        // 如果当前是learning模式，确保播放完单词后也能启用跟读按钮
        if (localMode.value === "learning") {
          canFollowRead.value = true;
        }
      },
      onError: (error: any) => {
        console.error("单词音频播放错误:", error);
        // 使用新方法上报错误对象
        uploadObjectLog("单词音频播放错误", {
          ...error,
          word: props.currentWord?.word || "未知",
          wordId: props.currentWord?.id || 0,
          audioUrl: props.currentWord?.audioUrl,
          mode: localMode.value,
        });
        isAudioPlaying.value = false;

        // 如果当前是learning模式，确保出错时也能启用跟读按钮
        if (localMode.value === "learning") {
          canFollowRead.value = true;
        }
      },
    });
  } catch (error: any) {
    uploadObjectLog("播放音频时出错", {
      ...error,
    });
    console.error("播放音频时出错:", error);
    isAudioPlaying.value = false;

    // 如果当前是learning模式，确保出错时也能启用跟读按钮
    if (localMode.value === "learning") {
      canFollowRead.value = true;
    }

    uni.showToast({
      title: "音频播放失败",
      icon: "none",
    });
  }
};

// 监听 localMode 的变化
watch(
  () => localMode.value,
  (newMode) => {
    console.log("localMode", newMode);

    // 在模式切换时，先停止所有正在播放的音频，避免多个声音冲突播放
    stopAllAudio();

    // 清除可能存在的超时定时器
    if (audioPlayTimeout.value) {
      clearTimeout(audioPlayTimeout.value);
      audioPlayTimeout.value = null;
    }

    if (newMode === "recognition") {
      // 延迟300ms播放音频
      setTimeout(() => {
        playAudio();
      }, 300);
    } else if (newMode === "learning") {
      // 进入learning状态时，禁用去跟读按钮
      canFollowRead.value = false;
      // 直接播放句子音频，不再播放单词音频
      setTimeout(() => {
        // 直接调用 playExampleAudio 方法，复用已有逻辑
        playExampleAudio();
      }, 300);
    } else if (newMode === "reading") {
      // 延迟200ms播放音频，不再自动开始录音，等待用户手动点击
      setTimeout(() => {
        // 设置播放状态
        isAudioPlaying.value = true;

        // 使用AudioPlayer播放单词音频
        wordPlayer.value.play(props.currentWord.audioUrl, {
          onStart: () => {
            console.log("单词音频开始播放");
          },
          onEnd: () => {
            console.log("单词音频播放完毕，等待用户点击开始录音");
            isAudioPlaying.value = false;
            // 设置状态为准备录音
            readingState.value = "ready_to_record";
          },
          onError: (error: any) => {
            console.error("单词音频播放错误:", error);
            // 使用新方法上报错误对象
            uploadObjectLog("单词音频播放错误", {
              ...error,
              word: props.currentWord?.word || "未知",
              wordId: props.currentWord?.id || 0,
              audioUrl: props.currentWord?.audioUrl,
              mode: localMode.value,
            });
            isAudioPlaying.value = false;
            // 出错也设置状态为准备录音
            readingState.value = "ready_to_record";
          },
        });
      }, 200);
    }
  },
  { immediate: true }
);

// 修改 playExampleAudio 函数也添加超时保护
const playExampleAudio = () => {
  if (!props.currentWord?.exampleSentences?.[0]?.audio_url) {
    uni.showToast({
      title: "示例音频不存在",
      icon: "none",
    });

    // 如果当前是learning模式，即使没有示例音频也启用跟读按钮
    if (localMode.value === "learning") {
      canFollowRead.value = true;
    }
    return;
  }

  try {
    // 停止所有正在播放的音频
    wordPlayer.value.stop();
    examplePlayer.value.stop();
    recordPlayer.value.stop();

    // 清除可能存在的超时定时器
    if (audioPlayTimeout.value) {
      clearTimeout(audioPlayTimeout.value);
      audioPlayTimeout.value = null;
    }

    // 设置播放状态
    isExampleAudioPlaying.value = true;

    // 使用AudioPlayer播放音频
    examplePlayer.value.play(props.currentWord.exampleSentences[0].audio_url, {
      onStart: () => {
        console.log("示例音频开始播放");

        // 设置一个超时定时器，确保在一定时间后完成播放状态
        audioPlayTimeout.value = setTimeout(() => {
          console.log("示例音频播放超时，自动结束播放状态");
          isExampleAudioPlaying.value = false;
          // 如果当前是learning模式，确保播放完示例后启用跟读按钮
          if (localMode.value === "learning") {
            canFollowRead.value = true;
          }
        }, 5000); // 10秒后超时
      },
      onEnd: () => {
        console.log("示例音频播放结束");
        isExampleAudioPlaying.value = false;

        // 如果当前是learning模式，确保播放完示例后启用跟读按钮
        if (localMode.value === "learning") {
          canFollowRead.value = true;
        }

        // 清除超时定时器
        if (audioPlayTimeout.value) {
          clearTimeout(audioPlayTimeout.value);
          audioPlayTimeout.value = null;
        }
      },
      onError: (error: any) => {
        console.error("示例音频播放错误:", error);
        // 使用新方法上报错误对象
        uploadObjectLog("单词示例音频播放错误", {
          ...error,
          word: props.currentWord?.word || "未知",
          wordId: props.currentWord?.id || 0,
          exampleAudioUrl: props.currentWord?.exampleSentences?.[0]?.audio_url,
          mode: localMode.value,
        });
        isExampleAudioPlaying.value = false;
        // 出错也需要清除超时定时器
        if (audioPlayTimeout.value) {
          clearTimeout(audioPlayTimeout.value);
          audioPlayTimeout.value = null;
        }
        // 如果当前是learning模式，确保出错时也能启用跟读按钮
        if (localMode.value === "learning") {
          canFollowRead.value = true;
        }
      },
    });
  } catch (error: any) {
    uploadObjectLog("播放示例音频时出错", {
      ...error,
    });
    console.error("播放示例音频时出错:", error);
    isExampleAudioPlaying.value = false;

    // 如果当前是learning模式，确保出错时也能启用跟读按钮
    if (localMode.value === "learning") {
      canFollowRead.value = true;
    }

    uni.showToast({
      title: "示例音频播放失败",
      icon: "none",
    });
  }
};

// 处理底部按钮点击
const handleForget = () => {
  // 只在 recognition 状态下设置状态
  if (localMode.value === "recognition") {
    localRecognitionStatus.value = "forget";
    switchToLearningMode();
  }
};

const handleFuzzy = () => {
  // 只在 recognition 状态下设置状态
  if (localMode.value === "recognition") {
    localRecognitionStatus.value = "fuzzy";
    switchToLearningMode();
  }
};

const handleRemember = () => {
  // 只在 recognition 状态下设置状态
  if (localMode.value === "recognition") {
    localRecognitionStatus.value = "remember";
    switchToLearningMode();
  }
};

const handleNextWord = () => {
  // 检查状态条件：必须是已录制状态，星星数量大于0，且已播放过录音
  if (readingState.value !== "recorded" || !hasPlayedRecord.value) {
    return;
  }

  // 检查评分是否大于0
  if (starScore.value <= 0) {
    uni.showToast({
      title: "继续练习，提高评分才能进入下一个单词",
      icon: "none",
      duration: 2000,
    });
    return;
  }

  // 停止所有正在播放的音频
  stopAllAudio();

  // recordPath.value = ''; // 清空录音路径
  // 重置状态
  readingState.value = "ready";
  hasPlayedRecord.value = false; // 重置录音播放状态

  // 在跟读模式下，使用之前记录的状态
  if (localMode.value === "reading") {
    if (!localRecognitionStatus.value) {
      uni.showToast({
        title: "状态异常，请重新开始",
        icon: "none",
      });
      return;
    }
    emit(
      "onWordComplete",
      localRecognitionStatus.value,
      totalScore.value,
      readingAudioUrl.value
    );
    // 重置本地状态
    localRecognitionStatus.value = null;
    totalScore.value = 0;
    showScore.value = false;
    starScore.value = 0;
    // // 重置模式到初始状态
    // localMode.value = "recognition";
  }
};

// 去跟读按钮点击处理
const handleFollowRead = () => {
  // 只有在 learning 状态且允许跟读时才能去跟读
  if (localMode.value === "learning" && canFollowRead.value) {
    // 停止所有正在播放的音频
    stopAllAudio();

    switchToReadingMode();
  }
};

// 监听 mode prop 的变化
watch(
  () => props.mode,
  (newMode) => {
    if (newMode) {
      // 在模式切换前，先停止所有正在播放的音频，避免多个声音冲突播放
      stopAllAudio();

      // 如果是从外部强制切换到 recognition，需要重置状态
      if (newMode === "recognition") {
        localRecognitionStatus.value = null;
      }
      localMode.value = newMode;
    }
  }
);

// 上传录音文件
const uploadRecordFile = async (tempFilePath: string, duration: number) => {
  // 设置上传状态为true
  isUploading.value = true;

  try {
    const fileOptions = {
      tempFilePath: tempFilePath,
      name: `audio_${Date.now()}.${tempFilePath.split(".").pop()}`, // 根据实际格式设置扩展名
      duration: recordDuration.value,
    };

    const uploadResult = await uploadFile(fileOptions);

    if (!uploadResult.isSuccess()) {
      throw new Error(uploadResult.msg || "上传失败");
    }

    const fileInfo = uploadResult.getData();
    readingAudioUrl.value = fileInfo.url;
    return fileInfo.url;
  } finally {
    // 设置上传状态为false
    isUploading.value = false;
  }
};

// 播放用户录音
const playUserRecord = () => {
  return new Promise<void>((resolve) => {
    if (!recordPath.value) {
      console.error("录音文件路径为空");
      resolve();
      return;
    }

    // 先播放录音
    console.log("准备播放录音");

    // 停止所有正在播放的音频
    wordPlayer.value.stop();
    examplePlayer.value.stop();
    recordPlayer.value.stop();

    // 设置播放状态
    playingRecord.value = true;

    // 使用AudioPlayer播放录音
    recordPlayer.value.play(recordPath.value, {
      onStart: () => {
        console.log("录音开始播放");
      },
      onEnd: () => {
        console.log("录音播放完成");
        playingRecord.value = false;
        // 设置已播放录音状态为true，允许进入下一单词
        hasPlayedRecord.value = true;
        resolve(); // 完成Promise
      },
      onError: (error: any) => {
        console.error("播放录音错误:", error);
        // 使用新方法上报错误对象
        uploadObjectLog("单词跟读播放录音错误", {
          ...error,
          word: props.currentWord?.word || "未知",
          wordId: props.currentWord?.id || 0,
          recordPath: recordPath.value,
          recordDuration: recordDuration.value,
          readingState: readingState.value,
        });
        playingRecord.value = false;
        resolve(); // 出错也要完成Promise
      },
    });
  });
};

// 播放评分反馈音效
const playScoreFeedback = (score: number) => {
  // 停止所有正在播放的音频
  wordPlayer.value.stop();
  examplePlayer.value.stop();
  recordPlayer.value.stop();
  feedbackPlayer.value.stop();

  let audioUrl = "";

  if (score < TRY_AGAIN_SCORE_THRESHOLD) {
    // 设置状态为播放try-again音效
    isPlayingTryAgain.value = true;
    audioUrl = TRY_AGAIN_SOUND;
  } else if (scoreToStars(score) === 3) {
    // 3星播放excellent.mp3
    audioUrl = "/static/sounds/excellent.mp3";
  } else {
    // 1-2星播放good-job.mp3
    audioUrl = "/static/sounds/good-job.mp3";
  }

  // 使用AudioPlayer播放音效
  feedbackPlayer.value.play(audioUrl, {
    onStart: () => {
      console.log("评分音效开始播放");
    },
    onEnd: () => {
      console.log("评分音效播放结束");
      isPlayingTryAgain.value = false;
    },
    onError: (error: any) => {
      console.error("评分音效播放错误:", error);
      isPlayingTryAgain.value = false;
    },
  });
};

// 处理评分结果
const handleScoreResult = (score: number) => {
  totalScore.value = score;

  if (score < TRY_AGAIN_SCORE_THRESHOLD) {
    // 显示 0 颗星
    starScore.value = 0;
    showScore.value = true;
    playScoreFeedback(score);
    return false;
  }

  // 设置星星数量
  starScore.value = scoreToStars(score);
  showScore.value = true;
  playScoreFeedback(score);
  return true;
};

// 修改录音结束事件处理
const handleRecordEnd = async (tempFilePath: string, duration: number) => {
  try {
    readingState.value = "evaluating"; // 设置为评估状态
    recordPath.value = tempFilePath;
    recordDuration.value = duration / 1000; // 将毫秒转换为秒

    // 录音时间过短的处理
    if (duration < 500) {
      // 小于0.5秒
      uni.showToast({
        title: "录音时间太短，请重新录音",
        icon: "none",
      });
      readingState.value = "ready";
      return;
    }

    // 上传录音文件
    await uploadRecordFile(tempFilePath, duration);

    // 注意：这里不立即设置为recorded状态，等待评估完成后再设置

    try {
      // 创建一个Promise来表示评估结果
      const evaluationPromise = evaluateWordPronunciation({
        text: props.currentWord.word,
        voiceUrl: readingAudioUrl.value,
      });

      // 创建一个Promise来表示录音播放完成
      const playPromise = playUserRecord();

      // 等待两个Promise都完成
      const [evaluateResult] = await Promise.all([
        evaluationPromise,
        playPromise,
      ]);

      // 确保无论评分结果如何，状态都会更新为recorded
      readingState.value = "recorded";

      // 处理评分结果
      const scoreSuccess = handleScoreResult(evaluateResult.data.totalScore);

      // 如果评分不通过，直接返回
      if (!scoreSuccess) {
        return;
      }
    } catch (innerError: any) {
      uploadObjectLog("评估过程中出错", {
        ...innerError,
      });
      // 内部评估过程中出错，也要确保状态更新
      console.error("评估过程中出错:", innerError);
      readingState.value = "recorded";
      uni.showToast({
        title:
          (innerError?.message as string) || "评估过程中出错，但已保存录音",
        icon: "none",
      });
    }
  } catch (error: any) {
    uploadObjectLog("评估失败", {
      ...error,
    });
    console.error("评估失败:", error);
    uni.showToast({
      title: (error?.message as string) || "评估失败，请重试",
      icon: "none",
    });
    readingState.value = "ready";
    playingRecord.value = false;
  }
};

// 修改录音管理器的事件监听
const listenerRecorderManager = () => {
  // 录音开始事件
  recorderManager.onStart(() => {
    console.log("录音开始");
    isRecording.value = true;

    // 清除之前可能存在的录音文件数据
    recordPath.value = "";
  });

  // 录音结束事件
  recorderManager.onStop((res) => {
    console.log("录音结束", res);
    isRecording.value = false;
    // 清除定时器
    if (silenceTimer.value) {
      clearTimeout(silenceTimer.value);
      silenceTimer.value = null;
    }
    const { tempFilePath, duration } = res;
    if (tempFilePath && duration > 300) {
      // 确保录音至少300毫秒
      handleRecordEnd(tempFilePath, duration);
    } else {
      uni.showToast({
        title: "录音时间太短，请重试",
        icon: "none",
      });
      readingState.value = "ready";
    }
  });

  // 录音错误事件
  recorderManager.onError((res) => {
    console.error(
      "录音错误:",
      res,
      "错误代码:",
      res.errCode,
      "错误信息:",
      res.errMsg,
      "单词:",
      props.currentWord?.word || "未知"
    );
    // 使用新方法上报错误对象
    uploadObjectLog("单词跟读录音错误", {
      ...res,
      word: props.currentWord?.word || "未知",
      wordId: props.currentWord?.id || 0,
      readingState: readingState.value,
      isRecording: isRecording.value,
      hasSound: hasSound.value,
    });

    // 检查是否是权限问题
    if (
      res.errMsg &&
      (res.errMsg.includes("authorize") ||
        res.errMsg.includes("fail auth deny") ||
        res.errMsg.includes("permission") ||
        res.errCode === 10003 ||
        res.errCode === -2 ||
        res.errMsg.includes("access"))
    ) {
      // 权限问题，弹出授权提示
      uni.showModal({
        title: "需要录音权限",
        content: "完成单词跟读需要录音权限，请允许小程序使用录音功能",
        confirmText: "去授权",
        cancelText: "取消",
        success: (result) => {
          if (result.confirm) {
            // 打开设置页面，引导用户授权
            uni.openSetting({
              success: (settingRes) => {
                if (settingRes.authSetting["scope.record"]) {
                  uni.showToast({
                    title: "授权成功，请重新录音",
                    icon: "success",
                  });
                }
              },
            });
          }
        },
      });
    } else {
      // 其他错误，显示通用提示
      uni.showToast({
        title: "录音失败：" + (res.errMsg || "未知错误"),
        icon: "none",
      });
    }

    readingState.value = "ready";
    isRecording.value = false;
  });
};

// 确保调用录音事件监听器
listenerRecorderManager();

// 修改 playMyRecord 函数
const playMyRecord = () => {
  if (readingState.value !== "recorded" || playingRecord.value) {
    return;
  }
  if (!recordPath.value) {
    uni.showToast({
      title: "没有录音文件",
      icon: "none",
    });
    return;
  }

  try {
    console.log("开始播放录音", recordPath.value);

    // 停止所有正在播放的音频
    wordPlayer.value.stop();
    examplePlayer.value.stop();
    recordPlayer.value.stop();

    // 设置播放状态
    playingRecord.value = true;

    // 使用AudioPlayer播放录音
    recordPlayer.value.play(recordPath.value, {
      onStart: () => {
        console.log("录音开始播放");
      },
      onEnd: () => {
        console.log("录音播放完成");
        playingRecord.value = false;
        // 设置已播放录音状态为true，允许进入下一单词
        hasPlayedRecord.value = true;
      },
      onError: (error: any) => {
        console.error("播放录音错误:", error);
        // 使用新方法上报错误对象
        uploadObjectLog("单词跟读播放录音错误", {
          ...error,
          word: props.currentWord?.word || "未知",
          wordId: props.currentWord?.id || 0,
          recordPath: recordPath.value,
          recordDuration: recordDuration.value,
          readingState: readingState.value,
        });
        playingRecord.value = false;
      },
    });
  } catch (error: any) {
    uploadObjectLog("播放录音时出错", {
      ...error,
    });
    console.error("播放录音时出错:", error);
    playingRecord.value = false;
    uni.showToast({
      title: "播放录音失败",
      icon: "none",
    });
  }
};

// 修改组件卸载时的清理
onUnmounted(() => {
  // 清理音频实例
  wordPlayer.value.dispose();
  examplePlayer.value.dispose();
  recordPlayer.value.dispose();
  feedbackPlayer.value.dispose();

  // 清理可能存在的定时器
  if (audioPlayTimeout.value) {
    clearTimeout(audioPlayTimeout.value);
    audioPlayTimeout.value = null;
  }
});

// 在组件挂载时初始化音频上下文
onMounted(() => {
  initAudioContext();
});

// 修改 handleReadingClick 函数
const handleReadingClick = () => {
  if (isAudioPlaying.value) {
    uni.showToast({
      title: "请等待音频播放结束",
      icon: "none",
    });
    return;
  }

  console.log("当前跟读状态:", readingState.value);

  if (
    readingState.value === "ready" ||
    readingState.value === "recorded" ||
    readingState.value === "ready_to_record"
  ) {
    console.log("开始录音，状态从", readingState.value, "变为recording");

    // 在开始录音前停止所有音频播放
    stopAllAudio();

    readingState.value = "recording";
    // 开始录音时隐藏星星
    showScore.value = false;
    // 开始录音逻辑
    toggleRecording();
  } else if (readingState.value === "recording") {
    console.log("结束录音，状态从recording变为evaluating");
    // 结束录音逻辑
    toggleRecording();
  }
};

// 修改 toggleRecording 函数，避免重复设置状态
const toggleRecording = () => {
  if (!isRecording.value) {
    // 立即设置为true，防止多次点击
    isRecording.value = true;
    hasSound.value = false; // 重置声音状态

    // 短暂延迟以避免初始噪声
    // 检查当前是否仍然处于录音预期状态
    if (readingState.value !== "recording") {
      isRecording.value = false;
      return;
    }

    try {
      // 开始录音，优化参数配置
      recorderManager.start({
        duration: 600000,
        sampleRate: 16000, // 提高采样率
        numberOfChannels: 1,
        encodeBitRate: 96000, // 提高比特率
        format: "mp3", // 改回MP3格式
        frameSize: 4, // 增加帧大小以获取更好的音质
        // audioSource 属性在某些平台可能不支持，移除以避免类型错误
      });
    } catch (error) {
      console.error("开始录音时出错:", error);
      isRecording.value = false;
      readingState.value = "ready";
      uni.showToast({
        title: "录音启动失败，请重试",
        icon: "none",
      });
    }
  } else {
    // 停止录音
    try {
      recorderManager.stop();
    } catch (error) {
      console.error("停止录音时出错:", error);
      uploadObjectLog("停止录音时出错", {
        error: error instanceof Error ? error.message : String(error),
        word: props.currentWord?.word || "未知",
        wordId: props.currentWord?.id || 0,
      });
    } finally {
      isRecording.value = false;
    }

    // 清除定时器
    if (silenceTimer.value) {
      clearTimeout(silenceTimer.value);
      silenceTimer.value = null;
    }
  }
};

// 中文释义显示控制
const promptState = computed(() => {
  // 按优先级顺序判断
  if (isUploading.value) {
    return "uploading";
  } else if (readingState.value === "evaluating") {
    return "evaluating";
  } else if (
    readingState.value === "recorded" &&
    starScore.value > 0 &&
    hasPlayedRecord.value
  ) {
    return "complete";
  } else if (readingState.value === "ready_to_record") {
    return "ready";
  } else {
    return "empty";
  }
});

// 处理导航到前一个单词
const handlePrevWord = () => {
  // 停止所有正在播放的音频
  stopAllAudio();

  // 发送前一个单词事件
  emit("onPrevWord");
};

// 处理导航到下一个单词
const handleNavNextWord = () => {
  // 停止所有正在播放的音频
  stopAllAudio();

  // 发送下一个单词事件
  emit("onNextWord");
};
</script>

<style lang="scss">
.word-recognition {
  // flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto; // 允许垂直滚动
  padding: 0 10px; // 添加左右内边距

  .word-card {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 5px 0;
    margin-top: 20px;
    min-height: 70px;

    .word-navigation {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .nav-arrow {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .iconfont {
          font-size: 24px;
          color: #d0761c;
          opacity: 0.8;
          transition: all 0.3s ease;

          &:hover {
            opacity: 1;
          }
        }

        @media screen and (min-width: 768px) {
          width: 50px;
          height: 50px;

          .iconfont {
            font-size: 40px;
          }
        }
      }

      .word-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0 10px;

        .word {
          font-size: 40px;
          color: #333;
          font-weight: 500;
          margin-bottom: 5px;
          line-height: 1.2;
        }

        .word-info {
          display: flex;
          align-items: center;
          gap: 4px;

          .part-of-speech {
            font-size: 22px;
            color: #bc9469;

            @media screen and (min-width: 768px) {
              font-size: 44px;
            }
          }

          .phonetic {
            font-size: 16px;
            color: #666;
            @media screen and (min-width: 768px) {
              font-size: 32px;
            }
          }

          .sound-btn {
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 2px;
            margin-left: 2px;

            .iconfont {
              font-size: 20px;
              color: #d0761c;
              opacity: 0.99;
              transition: all 0.3s ease;
            }

            .playing {
              animation: soundWave 1s ease-in-out infinite;
            }

            @media screen and (min-width: 768px) {
              width: 50px;
              height: 50px;

              .iconfont {
                font-size: 40px;
              }
            }
          }
        }
      }
    }
  }

  .example-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 5px 0;
  }

  .example-image-wrapper {
    width: 260px;
    height: auto; /* 移除固定高度比例，使高度自适应内容 */
    position: relative;
    margin: 10rpx 0; /* 使用rpx单位，减小上下外边距 */
    overflow: hidden;
    background: #fff;

    .example-image {
      width: 100%;
      aspect-ratio: 1; /* 设置合适的宽高比，可以根据实际需要调整 */
      object-fit: contain; /* 改为contain，确保图片完全显示 */
      border-radius: 6px;
    }
  }

  .example-sentence {
    position: relative;
    padding: 0px 40px 0px 10px;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    text-align: center;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;

    .audio-btn.small {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      .iconfont {
        font-size: 20px;
        color: #d0761c;
        opacity: 0.99;
        transition: all 0.3s ease;
      }

      .playing {
        animation: exampleSoundWave 1s ease-in-out infinite;
      }
    }
  }

  .chinese-meaning {
    padding: 10px;
    width: 80%;
    margin: 0 auto;
    text-align: center;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: #fff;
    border-radius: 8px;

    @media screen and (min-width: 768px) {
      padding: 20px;
      min-height: 60px;
      border-radius: 16px;
    }

    .hint-text {
      color: #999;
      font-size: 14px;

      @media screen and (min-width: 768px) {
        font-size: 28px;
      }
    }

    .meaning-text {
      color: #333;
      font-size: 14px;
      line-height: 1.5;

      @media screen and (min-width: 768px) {
        font-size: 28px;
        line-height: 1.6;
      }
    }
  }

  .bottom-area {
    margin-top: auto;
    margin-bottom: 80rpx;
    padding: 10px 0;

    .bottom-buttons {
      display: flex;
      gap: 12px;

      .go-reading {
        background-color: #ffd84e;
        width: 170px;
        height: 40px;
        margin: 0 auto;
        text-align: center;
        border-radius: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        @media screen and (min-width: 768px) {
          width: 340px;
          height: 80px;
          border-radius: 50px;
        }

        &.disabled {
          background-color: rgba(255, 216, 78, 0.5);
          pointer-events: none;
          opacity: 0.8;

          text {
            color: rgba(78, 41, 13, 0.3);
          }
        }

        text {
          font-size: 16px;
          color: rgba(78, 41, 13, 1);
          transition: all 0.3s ease;
          @media screen and (min-width: 768px) {
            font-size: 32px;
          }
        }
      }

      .btn {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 12px;
        border-radius: 12px;
        gap: 6px;

        .emoji {
          font-size: 16px;
          @media screen and (min-width: 768px) {
            font-size: 32px;
          }
        }

        text {
          font-size: 12px;
          color: #333;
          @media screen and (min-width: 768px) {
            font-size: 24px;
          }
        }
      }

      .forget {
        background-color: #fff3f3;
      }

      .fuzzy {
        background-color: #fff8e7;
      }

      .remember {
        background-color: #f0f9ff;
      }

      &.reading {
        .btn {
          height: 44px;
          flex-direction: row;
          background-color: #ffd352;
          @media screen and (min-width: 768px) {
            height: 88px;
          }

          text {
            color: #333;
            font-weight: 500;
            font-size: 16px;
            @media screen and (min-width: 768px) {
              font-size: 32px;
            }
          }
        }

        .my-reading {
          background-color: #f0f9ff;
        }

        .click-read {
          background-color: #ffd352;
        }

        .next-word {
          background-color: #fff8e7;
        }
      }
    }
  }

  // 平板设备适配
  @media screen and (min-width: 768px) {
    padding: 0px 20px 20px 20px;

    .word-card {
      min-height: 12vh;

      .word {
        font-size: 10vh;
      }

      // 添加平板端导航箭头样式
      .word-navigation {
        .nav-arrow {
          width: 60px;
          height: 60px;

          .iconfont {
            font-size: 46px;
          }
        }

        .word-content {
          padding: 0 30px;
        }
      }
    }

    .example-image-wrapper {
      max-width: 80%;
      padding-top: 0; /* 移除大量的上内边距 */
      margin-left: auto;
      margin-right: auto;
      margin-top: 20rpx;
      margin-bottom: 20rpx;
    }

    .example-sentence,
    .chinese-meaning {
      max-width: 80%;
      margin-left: auto;
      margin-right: auto;
    }

    .chinese-meaning {
      padding: 0;
      min-height: 24px;
    }

    .bottom-area {
      margin-bottom: 0vh;

      .bottom-buttons {
        max-width: 80%;
        margin: 0 auto;
      }
    }
  }
}

@media screen and (max-width: 320px) {
  .bottom-area {
    padding: 0 !important;
  }
  .reading-section {
    padding: 10px !important;
    padding-bottom: 0 !important;
  }

  .example-image-wrapper {
    width: 250px !important;
  }
}

.reading-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 20px;
}
.score-stars {
  text-align: center;
  // margin-bottom: 20px;
  position: relative;
  height: 40px; /* 为弧形布局预留空间 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.star {
  display: inline-block;
  margin: 0 8px;
  position: relative;
  transform-origin: center; /* 设置旋转中心点 */
  width: 24px;
  height: 24px;
  opacity: 0; /* 初始时星星不可见 */
  // animation: fadeInOut 0.5s forwards; /* 使星星在加载时逐个显示 */

  /* 每颗星星出现时的缩放效果 */
  animation: scaleIn 0.8s ease-out forwards;
}

.star.active {
  animation: scaleIn 0.8s ease-out forwards;
}

.star image {
  width: 100%;
  height: 100%;
  display: block;
}

/* 星星的弧形布局 */
.star:nth-child(1) {
  transform: rotate(-7.5deg);
  // &.active{
  animation: scaleIn1 0.8s ease-out forwards;
  animation-delay: 0.2s; /* 每颗星星的延时 */
  // }
}
.star:nth-child(2) {
  transform: translateY(-20px) rotate(0deg);
  // &.active{
  animation: scaleIn2 0.8s ease-out forwards;
  animation-delay: 0.4s; /* 每颗星星的延时 */
  // }
}
.star:nth-child(3) {
  transform: rotate(7.5deg);
  // &.active{
  animation: scaleIn3 0.8s ease-out forwards;
  animation-delay: 0.6s; /* 每颗星星的延时 */
  // }
}

/* 缩放动画 */
@keyframes scaleIn1 {
  0% {
    transform: scale(8.5); /* 初始时星星较大 */
    opacity: 0; /* 初始时透明 */
  }
  100% {
    transform: rotate(-7.5deg) scale(1); /* 缩放到正常大小 */
    opacity: 1; /* 最终星星可见 */
  }
}
@keyframes scaleIn2 {
  0% {
    transform: scale(8.5); /* 初始时星星较大 */
    opacity: 0; /* 初始时透明 */
  }
  100% {
    transform: translateY(-30px) rotate(0deg) scale(1); /* 缩放到正常大小 */
    opacity: 1; /* 最终星星可见 */
  }
}
@keyframes scaleIn3 {
  0% {
    transform: scale(8.5); /* 初始时星星较大 */
    opacity: 0; /* 初始时透明 */
  }
  100% {
    transform: rotate(7.5deg) scale(1); /* 缩放到正常大小 */
    opacity: 1; /* 最终星星可见 */
  }
}

.reading-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  // margin-bottom: 20px;

  .control-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    opacity: 0.3;

    &.disabled {
      pointer-events: none;
    }

    &:not(.disabled) {
      opacity: 1;
    }

    // .sound-btn {
    //   width: 64px;
    //   height: 64px;
    //   background-color: #FFD84E;
    //   border-radius: 50%;
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;

    //   &.disabled {
    //     background-color: #E0E0E0;

    //     color:rgba(212, 212, 212, 1);
    //   }
    // }

    text {
      font-size: 12px;
      color: #333;
    }
  }
}
.reading-controls {
  .control-btn {
    &.my-record {
      .sound-btn {
        width: 32px;
        height: 32px;
        // background-color: #4caf50;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 4px;
        background-color: rgba(250, 199, 73, 1);

        &.disabled {
          background-color: rgba(196, 196, 196, 1);
          pointer-events: none;
        }

        @media screen and (min-width: 768px) {
          width: 64px;
          height: 64px;
        }
      }
    }

    .reading-btn {
      width: 60px;
      height: 60px;
      border-radius: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ffd84e;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

      // text {
      //   font-size: 32px;
      //   color: #fff;
      //   font-weight: 500;
      // }

      image {
        width: 37px;
        height: 37px;
      }

      &.recording {
        // background-color: #FF4444;

        text {
          color: #fff;
        }
      }

      &.recorded {
        // background-color: #4CAF50;

        text {
          color: #fff;
        }
      }

      &-reading {
        position: relative;
        width: 37px;
        height: 37px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px; // 控制柱子之间的间距

        .bar {
          width: 4px;
          height: 16px;
          background: #ffffff;
          border-radius: 4px;
          animation: recording 1s ease-in-out infinite;

          &.left {
            animation-delay: -0.4s;
          }

          &.middle {
            animation-delay: -0.2s;
          }

          &.right {
            animation-delay: 0s;
          }
        }
      }
    }

    .image {
      height: 32px;
      width: 32px;
      border-radius: 50%;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(250, 199, 73, 1);

      &.disabled {
        background-color: rgba(196, 196, 196, 1);

        color: rgba(212, 212, 212, 1);
      }

      image {
        width: 16px;
        height: 16px;
        display: flex;
      }
    }

    .playImage {
      animation: soundWave 1s ease-in-out infinite;
    }
  }
}

@keyframes soundWave {
  0% {
    opacity: 0.99;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.99;
    transform: scale(1);
  }
}

@keyframes exampleSoundWave {
  0% {
    opacity: 0.99;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.99;
    transform: scale(1);
  }
}

@keyframes recording {
  0%,
  100% {
    height: 16px;
  }
  50% {
    height: 24px;
  }
}

@keyframes blinking {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(255, 216, 78, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(255, 216, 78, 0.8);
  }
}

.reading-btn.blinking {
  animation: blinking 1.5s ease-in-out infinite;
  background-color: #ffd84e;
}

.reading-controls {
  .control-btn {
    &.next-btn {
      &.active {
        .image {
          background-color: #ffd84e;
          transform: scale(1.05);
          transition: all 0.3s ease;
        }
      }
    }
  }
}

.prompt-message-container {
  width: 100%;
  text-align: center;
  margin: 20rpx 0;
  height: 70rpx; /* 增加固定高度 */
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.prompt-message {
  width: 100%;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.prompt-message text {
  font-size: 28rpx;
  color: #bc9469;
  font-weight: 500;
  background-color: rgba(188, 148, 105, 0.1);
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  animation: fadeInOut 2s ease-in-out infinite;
}

.prompt-message text.next-hint {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.prompt-message text.upload-hint {
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  position: relative;
  padding-left: 50rpx;
}

.prompt-message text.upload-hint::before {
  content: "";
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #2196f3;
  border-top-color: transparent;
  border-radius: 50%;
  animation: rotate 1s linear infinite;
}

.prompt-message text.evaluate-hint {
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  position: relative;
  padding-left: 50rpx;
}

.prompt-message text.evaluate-hint::before {
  content: "";
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #2196f3;
  border-top-color: transparent;
  border-radius: 50%;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }
  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.control-btn.ready_to_record {
  .reading-btn.commonBtn {
    background-color: #ffd84e;
  }

  text {
    color: #bc9469;
    font-weight: bold;
  }
}

.reading-btn.blinking {
  animation: blinking 1.5s ease-in-out infinite;
  background-color: #ffd84e;
}

.prompt-message text.empty-hint {
  background-color: transparent;
  animation: none;
  padding: 0;
  visibility: hidden;
}
</style>
