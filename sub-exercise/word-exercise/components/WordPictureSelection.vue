<template>
  <view class="word-picture-selection">
    <!-- 题目区域 - 看词选图 -->
    <view class="word-question">
      <view class="word">{{ currentWord.word }}</view>
      <view class="word-info">
        <text class="part-of-speech">{{ currentWord.partOfSpeech }}</text>
        <text class="phonetic" v-if="currentWord.phonetic?.trim()"
          >/{{ currentWord.phonetic }}/</text
        >
        <view class="sound-btn" @tap="playWordAudio">
          <text
            class="iconfont icon-sound"
            :class="{ playing: isAudioPlaying }"
          ></text>
        </view>
      </view>
    </view>

    <!-- 图片选项 -->
    <view class="image-options">
      <view
        v-for="(option, index) in optimizedWordOptions"
        :key="index"
        class="image-option"
        :class="renderImageClass(option.imageUrl)"
        @tap="handleSelectImage(option.imageUrl, index)"
      >
        <image :src="option.imageUrl" mode="aspectFill" />
        <view class="chinese-meaning">{{ option.chineseMeaning }}</view>
        <view class="image-mask" v-if="showImageMask">
          <view class="image-mask-icon image-mask-true">
            <image src="/static/correct.png"></image>
          </view>
          <view class="image-mask-icon image-mask-error">
            <image src="/static/wrong.png"></image>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { AudioPlayer } from "@/sub-exercise/utils/audio-player";
import { optimizeImageUrl } from "@/sub-exercise/utils/image-optimizer";
import { playSound } from "@/sub-exercise/utils/sound-effects";
import type { WordInfo } from "@/types/homework-exercise";
import { computed, onUnmounted, ref, watch } from "vue";

const props = defineProps<{
  currentWord: WordInfo;
}>();

const emit = defineEmits<{
  (e: "onWordComplete", answer: string, isCorrect: boolean): void;
  (e: "onAudioStatusChange", isPlaying: boolean): void;
  (e: "onAnswerStatusChange", isCorrect: boolean, isFinished: boolean): void;
  (e: "onCanGoNextChange", canGoNext: boolean): void;
}>();

// 音频播放状态
const isAudioPlaying = ref(false);

// 构建音频播放器实例
const wordAudioPlayer = new AudioPlayer();

// UI状态变量
const showImageMask = ref(false);
const imageIsTrue = ref("");
const selectedOption = ref<string | null>(null);
const isAnswerCorrect = ref(false);
// 添加可以进入下一题的状态
const canGoToNext = ref(false);

// 记录第一次选择的答案和正确性（用于统计）
const firstAnswer = ref<string | null>(null);
const firstAnswerCorrect = ref<boolean | null>(null);

// 优化后的图片选项
const optimizedWordOptions = computed(() => {
  if (!props.currentWord?.wordOptions || !props.currentWord?.imageOptions)
    return [];

  return props.currentWord.wordOptions.map((option, index) => ({
    ...option,
    imageUrl: optimizeImageUrl(props.currentWord.imageOptions[index] || ""),
  }));
});

// 封装统一的下一题按钮状态控制方法
const updateNextButtonState = (isCorrect: boolean, audioPlaying: boolean) => {
  // 只有答案正确且音频不在播放中，才能进入下一题
  const canGoNext = isCorrect && !audioPlaying;
  if (canGoNext !== canGoToNext.value) {
    canGoToNext.value = canGoNext;
    emit("onCanGoNextChange", canGoNext);
  }
  return canGoNext;
};

// 重置所有选择和音频状态
const resetSelectionState = () => {
  // 停止所有音频播放
  wordAudioPlayer.stop();

  // 重置选择状态
  selectedOption.value = null;
  showImageMask.value = false;
  imageIsTrue.value = "";
  isAnswerCorrect.value = false;
  firstAnswer.value = null;
  firstAnswerCorrect.value = null;

  // 重置音频状态
  isAudioPlaying.value = false;

  // 重置下一题状态
  updateNextButtonState(false, false);
};

// 播放单词音频的函数
const playWordAudio = () => {
  const audioUrl = props.currentWord?.audioUrl;
  if (!audioUrl) {
    uni.showToast({
      title: "单词音频不存在",
      icon: "none",
    });

    // 如果没有音频，根据答案状态决定是否可进入下一题
    updateNextButtonState(isAnswerCorrect.value, false);
    return;
  }

  // 如果已经在播放中，不重复播放
  if (isAudioPlaying.value) return;

  // 设置播放状态
  isAudioPlaying.value = true;
  emit("onAudioStatusChange", true);

  // 更新下一题按钮状态（开始播放时不能进入下一题）
  updateNextButtonState(isAnswerCorrect.value, true);

  // 使用 AudioPlayer 播放音频
  wordAudioPlayer.play(audioUrl, {
    onStart: () => {
      console.log("单词音频开始播放");
    },
    onEnd: () => {
      isAudioPlaying.value = false;
      emit("onAudioStatusChange", false);

      // 播放结束后，更新下一题按钮状态
      updateNextButtonState(isAnswerCorrect.value, false);
    },
    onError: (error) => {
      console.error("单词音频播放失败:", error);
      isAudioPlaying.value = false;
      emit("onAudioStatusChange", false);
      uni.showToast({
        title: "单词音频播放失败",
        icon: "none",
      });

      // 即使播放失败，也更新下一题按钮状态
      updateNextButtonState(isAnswerCorrect.value, false);
    },
  });
};

// 监听 currentWord 的变化
watch(
  () => props.currentWord,
  (newWord, oldWord) => {
    // 当单词变化时，重置状态
    resetSelectionState();

    // 自动播放单词音频
    if (newWord) {
      playWordAudio();
    }
  }
);

const renderImageClass = (option: string) => {
  if (selectedOption.value !== option) {
    return "";
  }

  // 判断是否正确
  const isCorrect = isCorrectOption(option);

  // 如果是选中状态，根据答案是否正确返回对应的class
  return `selected ${isCorrect ? "image-true" : "image-error"}`;
};

// 判断选项是否正确
const isCorrectOption = (option: string) => {
  // 获取优化前的原始URL
  const originalImageUrl = props.currentWord.imageUrl || "";
  // 获取优化后的URL
  const optimizedImageUrl = optimizeImageUrl(originalImageUrl);

  // 首先判断是否与当前单词的imageUrl相等（比较优化后的URL）
  if (option === optimizedImageUrl) {
    return true;
  }

  // 如果不相等，检查是否与例句中的图片相等
  if (
    props.currentWord.exampleSentences &&
    props.currentWord.exampleSentences.length > 0
  ) {
    return props.currentWord.exampleSentences.some(
      (sentence) => optimizeImageUrl(sentence.imageUrl || "") === option
    );
  }
  return false;
};

const handleSelectImage = (option: string, index: number) => {
  // 如果已经选择了答案，不再处理
  if (selectedOption.value) return;

  // 判断选中的图片是否正确
  const isCorrect = isCorrectOption(option);

  // 记录第一次选择
  if (firstAnswer.value === null) {
    firstAnswer.value = option;
    firstAnswerCorrect.value = isCorrect;
  }

  console.log("选择结果:", isCorrect ? "正确" : "错误");

  // 先设置选中状态，避免重复触发
  selectedOption.value = option;
  isAnswerCorrect.value = isCorrect;

  // 通知父组件答案状态变化
  emit("onAnswerStatusChange", isCorrect, isCorrect);

  if (!isCorrect) {
    // 选择错误，播放错误音效
    console.log("准备播放错误音效");
    try {
      playSound("error");
    } catch (error) {
      console.error("播放错误音效失败:", error);
    }

    showImageMask.value = true;
    imageIsTrue.value = "image-error";

    setTimeout(() => {
      showImageMask.value = false;
      imageIsTrue.value = "";
      // 错误选择后重置状态
      selectedOption.value = null;
      isAnswerCorrect.value = false;
      // 通知父组件答案状态变化
      emit("onAnswerStatusChange", false, false);

      // 更新下一题按钮状态
      updateNextButtonState(false, isAudioPlaying.value);
    }, 300);
  } else {
    // 选择正确，播放成功音效
    console.log("准备播放成功音效");
    try {
      playSound("success");
    } catch (error) {
      console.error("播放成功音效失败:", error);
    }

    showImageMask.value = true;
    imageIsTrue.value = "image-true";

    // 更新下一题按钮状态
    updateNextButtonState(true, isAudioPlaying.value);

    // 如果没有音频，可以直接进入下一题
    // 如果有音频，则自动播放
    if (props.currentWord.audioUrl) {
      // 自动播放音频
      setTimeout(() => {
        playWordAudio();
      }, 500);
    }
  }
};

// 公开方法：获取当前答题状态
const getStatus = () => {
  return {
    selectedOption: selectedOption.value,
    isAnswerCorrect: isAnswerCorrect.value,
    firstAnswer: firstAnswer.value,
    firstAnswerCorrect: firstAnswerCorrect.value,
    canGoToNext: canGoToNext.value,
  };
};

// 暴露方法给父组件
defineExpose({
  resetSelectionState,
  playWordAudio,
  getStatus,
});

// 增加组件卸载时释放音频资源
onUnmounted(() => {
  // 停止播放并释放资源
  if (wordAudioPlayer) {
    wordAudioPlayer.stop();
    wordAudioPlayer.dispose();
  }
});
</script>

<style lang="scss">
.word-picture-selection {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  .word-question {
    margin-top: 40rpx;
    text-align: center;

    .word {
      font-size: 60rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
      line-height: 1.2;
    }

    .word-info {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      .part-of-speech {
        font-size: 22px;
        color: #bc9469;
        line-height: 1.2;

        @media screen and (min-width: 768px) {
          font-size: 44px;
        }
      }

      .phonetic {
        font-size: 16px;
        color: #666;
        line-height: 1.2;

        @media screen and (min-width: 768px) {
          font-size: 32px;
        }
      }

      .sound-btn {
        width: 25px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 2px;
        margin-left: 2px;
        background-color: rgba(255, 211, 82, 0.1);
        border-radius: 50%;
        box-shadow: 0 4rpx 12rpx rgba(208, 118, 28, 0.15);
        transition: all 0.3s ease;
        border: 1px solid rgba(208, 118, 28, 0.2);

        &:active {
          transform: scale(0.95);
          background-color: rgba(255, 211, 82, 0.3);
        }

        .iconfont {
          font-size: 20px;
          color: #d0761c;
          opacity: 0.99;
          transition: all 0.3s ease;
        }

        .playing {
          animation: soundWave 1s ease-in-out infinite;
        }

        @media screen and (min-width: 768px) {
          width: 50px;
          height: 50px;

          .iconfont {
            font-size: 40px;
          }
        }
      }
    }
  }

  .image-options {
    margin-top: 40px;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    gap: 20rpx;
    justify-content: center;
    padding: 0;
    box-sizing: border-box;
    flex: 1;

    @media screen and (min-width: 768px) {
      margin-top: 20px;
      gap: 30rpx;
    }

    .image-option {
      position: relative;
      width: 40%;
      aspect-ratio: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      transition: all 0.3s ease;
      padding: 0;
      border-radius: 16rpx;
      border: 2px solid transparent;

      &:active {
        transform: scale(0.98);
      }

      image {
        width: 100%;
        height: 100%;
        display: block;
      }

      .chinese-meaning {
        width: 100%;
        height: 16%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.5);
        color: #333;
        font-size: 22rpx;
        padding: 8rpx;
        text-align: center;
        position: absolute;
        bottom: 0;
        left: 0;
      }

      .image-mask {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 16rpx;

        &-icon {
          position: absolute;
          right: 20rpx;
          top: 20rpx;
          width: 40rpx;
          height: 40rpx;
          display: none;

          image {
            width: 100%;
            height: 100%;
            display: block;
            border: none;
          }
        }
      }
      &.selected {
        &.image-true {
          border-color: rgb(97, 146, 53);
        }
        &.image-error {
          border-color: rgb(220, 38, 38);
        }
        transform: scale(0.98);
      }

      &.image-true {
        &.selected {
          .image-mask {
            background-color: rgba(255, 255, 255, 0.1);
          }
          .image-mask-true {
            display: block;
          }
        }
      }
      &.image-error {
        .image-mask {
          background-color: rgba(255, 255, 255, 0.1);
        }
        &.selected {
          .image-mask-error {
            display: block;
          }
        }
      }
    }
  }
}

/* iPad 和其他平板设备 */
@media screen and (min-width: 768px) {
  .word-picture-selection {
    .image-options {
      padding: 0 10rpx;
      box-sizing: border-box;
      .image-option {
        width: 30% !important;
        aspect-ratio: 1;
      }
    }
  }
}

@keyframes soundWave {
  0% {
    opacity: 0.99;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.99;
    transform: scale(1);
  }
}
</style>
