<template>
  <view class="ready-page">
    <image
      class="ready-image"
      src="/static/icons/dictation-write.svg"
      mode="aspectFit"
    />
    <text class="ready-title">准备好开始复习了吗？</text>
    <text class="ready-subtitle">让我们一起巩固单词吧</text>

    <!-- 加载中的进度显示 -->
    <view v-if="isLoading" class="loading-container">
      <!-- 自定义进度条 -->
      <view class="custom-progress">
        <view
          class="progress-inner"
          :style="{ width: `${loadingProgress}%` }"
        ></view>
      </view>
      <text class="loading-text">加载中 {{ loadingProgress.toFixed(0) }}%</text>
    </view>

    <button v-else class="ready-btn" @click="handleReady">开始复习</button>
  </view>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps } from "vue";

const props = defineProps({
  isLoading: {
    type: <PERSON>olean,
    default: false,
  },
  loadingProgress: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(["ready"]);

const handleReady = () => {
  emit("ready");
};
</script>

<style lang="scss" scoped>
// 添加按钮重置样式
button {
  margin: 0;
  padding: 0;
  border: 0 none;
  background: none;
  outline: none;
  position: relative;
  overflow: visible;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: none;
    transform: none;
    border-radius: inherit;
    pointer-events: none;
  }
}

.ready-page {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  .ready-image {
    width: 400rpx;
    height: 400rpx;
    margin-bottom: 48rpx;
  }

  .ready-title {
    font-size: 36rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
  }

  .ready-subtitle {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 64rpx;
  }

  .loading-container {
    width: 320rpx;
    margin-bottom: 24rpx;

    // 自定义进度条样式
    .custom-progress {
      width: 100%;
      height: 16rpx;
      background-color: #f0f0f0;
      border-radius: 8rpx;
      overflow: hidden;
      margin-bottom: 20rpx;
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);

      .progress-inner {
        height: 100%;
        background-color: #ffd600;
        border-radius: 8rpx;
        transition: width 0.3s ease;
      }
    }

    .loading-text {
      font-size: 28rpx;
      color: #666;
      text-align: center;
    }
  }

  .ready-btn {
    width: 320rpx;
    height: 88rpx;
    background: #ffd600;
    border-radius: 44rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      opacity: 0.9;
    }
  }
}
</style>
