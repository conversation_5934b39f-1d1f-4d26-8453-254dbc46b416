<template>
  <view class="card-wrapper">
    <!-- <template > -->
    <view
      v-for="(item, index) in list"
      :key="index"
      class="cardItem"
      :class="{
        active: index === currentIndex,
        prev: index === currentIndex - 1,
      }"
    >
      <view class="itemSlot" v-if="shouldShowCard(index)">
        <slot name="default" :question="item"></slot>
      </view>
    </view>
    <!-- </template> -->
  </view>
</template>

<script lang="ts" setup>
const props = defineProps<{
  list: Array<any>;
  currentIndex: number;
  direction?: "left" | "right"; // 滑动方向
}>();

// 只显示当前、前一个和后一个卡片
const shouldShowCard = (index: number) => {
  return index === props.currentIndex;
};
</script>

<style lang="scss">
.card-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  // flex: 1 1 auto;

  .cardItem {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // border-radius: 32rpx;
    // box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06),
    //   0 12rpx 32rpx rgba(0, 0, 0, 0.04), 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(100%) scale(0.95);
    opacity: 0;
    transform: translateX(100%) scale(0.95);

    .itemSlot {
      width: 100%;
      height: 100%;
    }

    &.active {
      opacity: 1;
      transform: translateX(0) scale(1);
      z-index: 2;
    }
    &.prev {
      opacity: 0;
      transform: translateX(-100%) scale(0.95);
    }
  }
}
</style>
