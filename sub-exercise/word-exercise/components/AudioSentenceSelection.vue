<template>
  <view class="audio-sentence-selection">
    <!-- 题目区域 - 听句选图 -->
    <view class="sentence-question">
      <view class="sound-btn-container">
        <view class="sound-btn" @tap="playSentenceAudio">
          <text
            class="iconfont icon-sound"
            :class="{ playing: isPlaying }"
          ></text>
        </view>
      </view>

      <!-- 将句子提示和显示区域放在一个固定大小的父容器中 -->
      <view class="sentence-container">
        <!-- 添加点击提示文本 -->
        <view
          class="sentence-hint"
          @tap="toggleSentenceVisibility"
          v-if="!showSentence"
        >
          <text class="hint-text">{{
            showSentence ? "" : "需要提示可点击查看"
          }}</text>
          <text class="iconfont icon-eye hint-icon"></text>
        </view>
        <!-- 显示句子，仅在用户点击后显示 -->
        <view
          v-if="showSentence"
          class="sentence-display"
          :class="{ 'with-scroll': isSentenceLong }"
          @tap="toggleSentenceVisibility"
        >
          <mp-html
            :content="highlightedSentence"
            style="
              width: 100%;
              max-width: 100%;
              word-break: break-word;
              word-wrap: break-word;
              overflow-x: hidden;
              white-space: normal;
              box-sizing: border-box;
            "
          ></mp-html>
        </view>
      </view>
    </view>

    <!-- 图片选项 -->
    <view class="image-options">
      <view
        v-for="(option, index) in optimizedWordOptions"
        :key="index"
        class="image-option"
        :class="renderImageClass(option.imageUrl)"
        @tap="handleSelectImage(option.imageUrl, index)"
      >
        <image :src="option.imageUrl" mode="aspectFill" />
        <view class="chinese-meaning">{{ option.chineseMeaning }}</view>
        <view class="image-mask" v-if="showImageMask">
          <view class="image-mask-icon image-mask-true">
            <image src="/static/correct.png"></image>
          </view>
          <view class="image-mask-icon image-mask-error">
            <image src="/static/wrong.png"></image>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { AudioPlayer } from "@/sub-exercise/utils/audio-player";
import { optimizeImageUrl } from "@/sub-exercise/utils/image-optimizer";
import { playSound } from "@/sub-exercise/utils/sound-effects";
import type { WordInfo } from "@/types/homework-exercise";
import { uploadObjectLog } from "@/utils/log/uploader";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";

const props = defineProps<{
  currentWord: WordInfo;
}>();

const emit = defineEmits<{
  (e: "onWordComplete", answer: string, isCorrect: boolean): void;
  (e: "onAudioStatusChange", isPlaying: boolean): void;
  (e: "onAnswerStatusChange", isCorrect: boolean, isFinished: boolean): void;
  (e: "onCanGoNextChange", canGoNext: boolean): void;
}>();

// 音频播放状态
const isPlaying = ref(false); // 句子音频播放状态

// 构建音频播放器实例
const sentenceAudioPlayer = new AudioPlayer(); // 用于播放句子音频

// UI状态变量
const showImageMask = ref(false);
const imageIsTrue = ref("");
const selectedOption = ref<string | null>(null);
const showSentence = ref(false);
const isAnswerCorrect = ref(false);
const canGoToNext = ref(false);

// 记录第一次选择的答案和正确性（用于统计）
const firstAnswer = ref<string | null>(null);
const firstAnswerCorrect = ref<boolean | null>(null);

// 封装统一的下一题按钮状态控制方法
const updateNextButtonState = (isCorrect: boolean, audioPlaying: boolean) => {
  // 只有答案正确且音频不在播放中，才能进入下一题
  const canGoNext = isCorrect && !audioPlaying;
  if (canGoNext !== canGoToNext.value) {
    canGoToNext.value = canGoNext;
    emit("onCanGoNextChange", canGoNext);
  }
  return canGoNext;
};

// 计算高亮后的句子
const highlightedSentence = computed(() => {
  if (!props.currentWord?.exampleSentences?.[0]?.sentence) return "";

  const sentence = props.currentWord.exampleSentences[0].sentence;
  const word = props.currentWord.word;

  // 使用不区分大小写的正则表达式
  const regex = new RegExp(`(${word})`, "gi");

  let fontSize = 30;

  // 将句子转换为HTML字符串，给目标单词添加颜色样式
  const html = `<div style="font-size: ${fontSize}rpx; color: #333; line-height: 1.2; text-align: left; padding: 1px 3px; width: 100%; display: block; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; white-space: normal; box-sizing: border-box;">
    ${sentence.replace(
      regex,
      '<span style="color: #bc9469; font-weight: bold; display: inline; background-color: rgba(255, 211, 82, 0.2); padding: 0 3px; border-radius: 3px;">$1</span>'
    )}
  </div>`;

  return html;
});

// 判断句子是否过长需要滚动条
const isSentenceLong = computed(() => {
  if (!props.currentWord?.exampleSentences?.[0]?.sentence) return false;

  const sentence = props.currentWord.exampleSentences[0].sentence;
  // 如果句子超过40个字符，认为需要滚动
  return sentence.length > 40;
});

// 添加切换句子显示状态的函数
const toggleSentenceVisibility = () => {
  showSentence.value = !showSentence.value;
};

// 重置所有选择和音频状态
const resetSelectionState = () => {
  // 停止所有音频播放
  sentenceAudioPlayer.stop();

  // 重置选择状态
  selectedOption.value = null;
  showSentence.value = false;
  showImageMask.value = false;
  imageIsTrue.value = "";
  isAnswerCorrect.value = false;
  firstAnswer.value = null;
  firstAnswerCorrect.value = null;

  // 重置播放状态
  isPlaying.value = false;

  // 重置下一题状态
  updateNextButtonState(false, false);
};

// 播放句子音频
const playSentenceAudio = () => {
  if (!props.currentWord?.exampleSentences?.[0]?.audio_url) {
    uni.showToast({
      title: "例句音频不存在",
      icon: "none",
    });

    // 更新下一题按钮状态
    updateNextButtonState(isAnswerCorrect.value, false);
    return;
  }

  // 如果已经在播放中，不重复播放
  if (isPlaying.value) return;

  // 设置播放状态
  isPlaying.value = true;
  emit("onAudioStatusChange", true);

  // 更新下一题按钮状态（播放中不能进入下一题）
  updateNextButtonState(isAnswerCorrect.value, true);

  // 使用句子音频播放器播放音频
  const audioUrl = props.currentWord.exampleSentences[0].audio_url;
  sentenceAudioPlayer.play(audioUrl, {
    onStart: () => {
      console.log("例句音频开始播放");
    },
    onEnd: () => {
      isPlaying.value = false;
      emit("onAudioStatusChange", false);

      // 更新下一题按钮状态
      updateNextButtonState(isAnswerCorrect.value, false);
    },
    onError: (error: any) => {
      uploadObjectLog("例句音频播放失败", {
        ...error,
      });
      console.error("例句音频播放失败:", error);
      isPlaying.value = false;
      emit("onAudioStatusChange", false);
      uni.showToast({
        title: "例句音频播放失败",
        icon: "none",
      });

      // 更新下一题按钮状态
      updateNextButtonState(isAnswerCorrect.value, false);
    },
  });
};

// 监听 currentWord 的变化
watch(
  () => props.currentWord,
  (newWord, oldWord) => {
    // 当单词变化时，重置状态
    resetSelectionState();

    // 自动播放句子音频
    if (newWord) {
      playSentenceAudio();
    }
  }
);

// 添加 onMounted 生命周期钩子，确保组件首次加载时自动播放
onMounted(() => {
  if (props.currentWord) {
    // 短暂延迟确保组件完全渲染
    setTimeout(() => {
      playSentenceAudio();
    }, 200);
  }
});

// 优化后的图片选项
const optimizedWordOptions = computed(() => {
  if (!props.currentWord?.wordOptions || !props.currentWord?.imageOptions)
    return [];

  return props.currentWord.wordOptions.map((option, index) => ({
    ...option,
    imageUrl: optimizeImageUrl(props.currentWord.imageOptions[index] || ""),
  }));
});

const renderImageClass = (option: string) => {
  if (selectedOption.value !== option) {
    return "";
  }

  // 判断是否正确
  const isCorrect = isCorrectOption(option);

  // 如果是选中状态，根据答案是否正确返回对应的class
  return `selected ${isCorrect ? "image-true" : "image-error"}`;
};

// 判断选项是否正确
const isCorrectOption = (option: string) => {
  // 获取优化前的原始URL
  const originalImageUrl = props.currentWord.imageUrl || "";
  // 获取优化后的URL
  const optimizedImageUrl = optimizeImageUrl(originalImageUrl);

  // 首先判断是否与当前单词的imageUrl相等（比较优化后的URL）
  if (option === optimizedImageUrl) {
    return true;
  }

  // 如果不相等，检查是否与例句中的图片相等
  if (
    props.currentWord.exampleSentences &&
    props.currentWord.exampleSentences.length > 0
  ) {
    return props.currentWord.exampleSentences.some(
      (sentence) => optimizeImageUrl(sentence.imageUrl || "") === option
    );
  }
  return false;
};

const handleSelectImage = (option: string, index: number) => {
  // 如果已经选择了答案，不再处理
  if (selectedOption.value) return;

  // 判断选中的图片是否正确
  const isCorrect = isCorrectOption(option);

  // 记录第一次选择
  if (firstAnswer.value === null) {
    firstAnswer.value = option;
    firstAnswerCorrect.value = isCorrect;
  }

  console.log("选择结果:", isCorrect ? "正确" : "错误");

  // 先设置选中状态，避免重复触发
  selectedOption.value = option;
  isAnswerCorrect.value = isCorrect;

  // 通知父组件答案状态
  emit("onAnswerStatusChange", isCorrect, isCorrect);

  if (!isCorrect) {
    // 选择错误，播放错误音效
    console.log("准备播放错误音效");
    try {
      playSound("error");
    } catch (error) {
      console.error("播放错误音效失败:", error);
    }

    // 设置显示错误图标
    showImageMask.value = true;
    imageIsTrue.value = "image-error";

    // 错误选择后延迟重置状态
    setTimeout(() => {
      showImageMask.value = false;
      imageIsTrue.value = "";
      selectedOption.value = null;
      isAnswerCorrect.value = false;
      // 通知父组件状态
      emit("onAnswerStatusChange", false, false);

      // 更新下一题按钮状态
      updateNextButtonState(false, isPlaying.value);
    }, 500);
  } else {
    // 选择正确，播放成功音效
    console.log("准备播放成功音效");
    try {
      playSound("success");
    } catch (error) {
      console.error("播放成功音效失败:", error);
    }

    // 设置显示正确图标
    showImageMask.value = true;
    imageIsTrue.value = "image-true";

    // 更新下一题按钮状态
    updateNextButtonState(true, isPlaying.value);
  }
};

// 公开方法：获取当前答题状态
const getStatus = () => {
  return {
    selectedOption: selectedOption.value,
    isAnswerCorrect: isAnswerCorrect.value,
    firstAnswer: firstAnswer.value,
    firstAnswerCorrect: firstAnswerCorrect.value,
    canGoToNext: canGoToNext.value,
  };
};

// 暴露方法给父组件
defineExpose({
  resetSelectionState,
  playSentenceAudio,
  getStatus,
});

// 增加组件卸载时释放音频资源
onUnmounted(() => {
  // 停止播放并释放资源
  if (sentenceAudioPlayer) {
    sentenceAudioPlayer.stop();
    sentenceAudioPlayer.dispose();
  }
});
</script>

<style lang="scss">
.audio-sentence-selection {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  .sentence-question {
    margin-top: 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 0 20rpx;
    box-sizing: border-box;

    /* iPad适配 */
    @media screen and (min-width: 768px) {
      margin-top: 40rpx;
      padding: 0 30rpx;
    }

    .sound-btn-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20rpx;

      /* iPad适配 */
      @media screen and (min-width: 768px) {
        margin-bottom: 30rpx;
      }

      .sound-btn-hint {
        font-size: 24rpx;
        color: #999;
        margin-top: 18rpx;
        transition: all 0.3s;
      }
    }

    .sound-btn {
      width: 120rpx;
      height: 120rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 211, 82, 0.1);
      border-radius: 50%;
      box-shadow: 0 4rpx 12rpx rgba(208, 118, 28, 0.15);
      transition: all 0.3s ease;
      border: 1px solid rgba(208, 118, 28, 0.2);

      &:active {
        transform: scale(0.95);
        background-color: rgba(255, 211, 82, 0.3);
      }

      .iconfont {
        font-size: 50rpx;
        color: #d0761c;
        opacity: 0.99;
        transition: all 0.3s ease;
      }

      .playing {
        animation: soundWave 1.2s ease-in-out infinite;
      }
    }

    /* 句子容器样式 */
    .sentence-container {
      width: 90%;
      height: 140rpx;
      border-radius: 16rpx;
      background-color: rgba(255, 250, 230, 0.3);
      display: flex;
      flex-direction: column;
      align-items: stretch;
      justify-content: flex-start;
      overflow: hidden;
      position: relative;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
      transition: all 0.3s ease;
      margin-bottom: 30rpx;
      box-sizing: border-box;
      padding: 0;

      @media screen and (min-width: 768px) {
        width: 80%;
        height: 120rpx;
        max-height: 120rpx;
        border-radius: 12rpx;
        margin-bottom: 10rpx;
      }
    }

    /* 句子提示样式 */
    .sentence-hint {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      padding: 16rpx;
      cursor: pointer;
      background-image: linear-gradient(
        to bottom,
        rgba(255, 250, 230, 0.2),
        rgba(255, 211, 82, 0.05)
      );
      transition: all 0.3s ease;

      &:active {
        background-color: rgba(196, 146, 98, 0.05);
      }

      .hint-text {
        font-size: 24rpx;
        color: #bc9469;
        opacity: 0.7;
        margin-bottom: 8rpx;
        font-weight: normal;

        @media screen and (min-width: 768px) {
          font-size: 28rpx;
        }
      }

      .hint-icon {
        font-size: 28rpx;
        color: #d0761c;
        opacity: 0.5;

        @media screen and (min-width: 768px) {
          font-size: 36rpx;
        }
      }
    }

    /* 句子显示区域样式 */
    .sentence-display {
      width: 100%;
      height: 100%;
      padding: 20rpx 24rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      overflow-y: auto;
      overflow-x: hidden !important;
      text-align: left;
      animation: fadeIn 0.3s ease;
      box-sizing: border-box;
      background-color: rgba(255, 250, 230, 0.5);

      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 4rpx;
        background-color: rgba(255, 250, 230, 0.1);
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(196, 146, 98, 0.3);
        border-radius: 4rpx;
      }

      &.with-scroll {
        padding-right: 24rpx;

        /* 添加滚动提示 */
        &::after {
          content: "";
          position: absolute;
          bottom: 8rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 3rpx;
          background-color: rgba(196, 146, 98, 0.2);
          border-radius: 4rpx;
        }
      }
    }
  }

  .image-options {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    gap: 20rpx;
    justify-content: center;
    padding: 0;
    box-sizing: border-box;
    flex: 1;

    @media screen and (min-width: 768px) {
      margin-top: 20px;
      gap: 30rpx;
    }

    .image-option {
      position: relative;
      width: 40%;
      aspect-ratio: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      transition: all 0.3s ease;
      padding: 0;
      border-radius: 16rpx;
      border: 2px solid transparent;

      &:active {
        transform: scale(0.98);
      }

      image {
        width: 100%;
        height: 100%;
        display: block;
        object-fit: cover;
      }

      .chinese-meaning {
        width: 100%;
        height: 16%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.5);
        color: #333;
        font-size: 22rpx;
        padding: 8rpx;
        text-align: center;
        position: absolute;
        bottom: 0;
        left: 0;
      }

      .image-mask {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 16rpx;

        &-icon {
          position: absolute;
          right: 20rpx;
          top: 20rpx;
          width: 40rpx;
          height: 40rpx;
          display: none;

          image {
            width: 100%;
            height: 100%;
            display: block;
            border: none;
          }
        }
      }
      &.selected {
        &.image-true {
          border-color: rgb(97, 146, 53);
        }
        &.image-error {
          border-color: rgb(220, 38, 38);
        }
        transform: scale(0.98);
      }

      &.image-true {
        &.selected {
          .image-mask {
            background-color: rgba(255, 255, 255, 0.1);
          }
          .image-mask-true {
            display: block;
          }
        }
      }
      &.image-error {
        .image-mask {
          background-color: rgba(255, 255, 255, 0.1);
        }
        &.selected {
          .image-mask-error {
            display: block;
          }
        }
      }
    }
  }
}

/* iPad 和其他平板设备 */
@media screen and (min-width: 768px) {
  .audio-sentence-selection {
    .sentence-question {
      .sound-btn {
        width: 100rpx;
        height: 100rpx;

        .iconfont {
          font-size: 40rpx;
        }
      }
    }

    .image-options {
      padding: 0 10rpx;
      box-sizing: border-box;
      .image-option {
        width: 30% !important;
        aspect-ratio: 1;
      }
    }
  }
}

@keyframes soundWave {
  0% {
    opacity: 0.99;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.99;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
