<template>
  <CommonBox
    class="word-review"
    @back="handleBack"
    :use-default-back="false"
    :loading="loading"
    title="单词复习"
  >
    <template #left-txt>
      <view class="left-txt" v-if="!loading && isReady"
        >{{ currentIndex + 1 }}/{{ questionList.length }}</view
      >
    </template>

    <template v-if="!loading">
      <view class="question-container">
        <!-- 准备状态 -->
        <ready-page v-if="!isReady" :loading="loading" @ready="handleReady" />

        <!-- 复习状态 -->
        <view v-else class="card-wrapper">
          <view
            v-for="(item, index) in questionList"
            :key="index"
            class="cardItem"
            :class="{
              active: index === currentIndex,
              prev: index === currentIndex - 1,
            }"
          >
            <word-recognition
              v-if="shouldShowCard(index)"
              class="word-recognition"
              :current-word="item.words"
              :show-example="showExample"
              @onWordComplete="handleWordComplete"
              @onPrevWord="handlePrevWord"
              @onNextWord="handleNextWord"
            />
          </view>
        </view>
      </view>
    </template>

    <!-- 继续学习弹窗 -->
    <continue-popup
      ref="continuePopup"
      title="继续复习"
      :content="
        '上次复习到第' + (savedProgress.currentIndex + 1) + '个单词，是否继续？'
      "
      continue-text="继续复习"
      @restart="handleRestart"
      @continue="handleContinue"
    />
  </CommonBox>
</template>

<script lang="ts" setup>
import CommonBox from "@/components/common-box/commonBox.vue";
import { computed, onUnmounted, ref } from "vue";
import { submitOneWordExercise } from "../../api/homework-submission";
import type { Question, WordInfo } from "../../types/homework-exercise";

import { getHomeworkExerciseDetail } from "@/api/homework-exercise";

import ContinuePopup from "@/components/continue-popup/continue-popup.vue";
// @ts-ignore
import { onLoad } from "@dcloudio/uni-app";
import ReadyPage from "./components/ready-page.vue";
import WordRecognition from "./components/word-recognition.vue";
// 定义结果类型
interface WordResult {
  exerciseId: number;
  questionType: number;
  wordId: number;
  word: string;
  recognitionStatus: "forget" | "fuzzy" | "remember";
  index: number;
  spendTime: number;
  readingScore: number;
  readingAudioUrl: string;
}

// 页面参数
const exerciseId = ref<number>(0);
const homeworkId = ref<number>(0);

// 状态管理
const currentIndex = ref(0);
const totalWords = ref(0);
const showExample = ref(true);
const loading = ref(true);
const isReady = ref(false);
const spendTime = ref(0); // 花费的时间（秒）
const timer = ref<ReturnType<typeof setInterval> | null>(null); // 计时器

// 问题列表
const questionList = ref<Question[]>([]);
// 当前问题 - 作为计算属性，自动跟随currentIndex变化
const currentQuestion = computed<Question | null>(() => {
  if (
    questionList.value.length > 0 &&
    currentIndex.value < questionList.value.length
  ) {
    return questionList.value[currentIndex.value];
  }
  return null;
});
// 当前单词 - 作为计算属性，自动跟随currentQuestion变化
const currentWord = computed<WordInfo | null>(() => {
  return currentQuestion.value?.words || null;
});

// 记录用户选择的状态
const currentRecognitionStatus = ref<"forget" | "fuzzy" | "remember" | null>(
  null
);

// 保存的进度
const savedProgress = ref({
  currentIndex: 0,
  spendTime: 0,
});

// 继续学习弹窗
const continuePopup = ref();

// 保存进度
const saveProgress = () => {
  const progress = {
    exerciseId: exerciseId.value,
    currentIndex: currentIndex.value,
    spendTime: spendTime.value,
  };
  uni.setStorageSync(
    `wordReviewProgress_${exerciseId.value}`,
    JSON.stringify(progress)
  );
};

// 加载进度
const loadProgress = () => {
  try {
    const savedData = uni.getStorageSync(
      `wordReviewProgress_${exerciseId.value}`
    );
    if (savedData) {
      const progress = JSON.parse(savedData);
      return progress;
    }
  } catch (error) {
    console.error("加载进度失败:", error);
  }
  return null;
};

// 清除进度
const clearProgress = () => {
  uni.removeStorageSync(`wordReviewProgress_${exerciseId.value}`);
};

// 开始计时
const startTimer = () => {
  if (timer.value) return;
  timer.value = setInterval(() => {
    spendTime.value++;
  }, 1000);
};

// 停止计时
const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};

// 添加准备完成处理方法
const handleReady = () => {
  const progress = loadProgress();
  if (progress) {
    savedProgress.value = progress;
    continuePopup.value.open();
  } else {
    isReady.value = true;
    startTimer();
  }
};

// 处理重新开始
const handleRestart = () => {
  clearProgress();
  currentIndex.value = 0;
  spendTime.value = 0;

  isReady.value = true;
  startTimer();
};

// 处理继续学习
const handleContinue = () => {
  currentIndex.value = savedProgress.value.currentIndex;
  spendTime.value = savedProgress.value.spendTime || 0;

  isReady.value = true;
  startTimer();
};

// 只显示当前、前一个和后一个卡片
const shouldShowCard = (index: number) => {
  return index === currentIndex.value;
};
// 获取练习详情
const fetchExerciseDetail = async () => {
  try {
    loading.value = true;
    const res = await getHomeworkExerciseDetail(exerciseId.value);
    const data = res.data;
    // 处理问题数据
    if (data.extraWordData?.questions) {
      questionList.value = data.extraWordData.questions;
      totalWords.value = questionList.value.length;

      // 设置初始索引
      if (totalWords.value > 0) {
        currentIndex.value = 0;
        // 不需要手动设置currentQuestion和currentWord，它们会自动计算
      } else {
        uni.showToast({
          title: "没有可练习的单词",
          icon: "none",
        });
      }
    } else {
      uni.showToast({
        title: "练习数据格式错误",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("获取练习详情失败:", error);
    uni.showToast({
      title: typeof error === "string" ? error : "获取数据失败",
      icon: "none",
    });
  } finally {
    loading.value = false;
  }
};

// 提交最后一个单词并完成练习
const submitLastWordAndComplete = async (result: WordResult) => {
  try {
    uni.showLoading({ title: "提交中..." });
    // 提交当前单词的结果
    await submitOneWordExercise({
      ...result,
      answer: undefined,
      correct: undefined,
      isFinish: true,
    });

    // 打开结果页面
    uni.redirectTo({
      url: `/sub-exercise/word-exercise/word-recognition-review-result?id=${exerciseId.value}&homeworkId=${homeworkId.value}&fromExercise=true`,
    });
    // 所有单词都完成了，停止计时并清除进度
    stopTimer();
    clearProgress();
    // 通知刷新
    uni.$emit("wordExerciseComplete");
  } catch (error) {
    console.error("提交失败:", error);
    uni.showToast({
      title: "提交失败，请检查网络",
      icon: "none",
    });
  } finally {
    uni.hideLoading();
  }
};

// 处理单词完成
const handleWordComplete = async (
  status: "forget" | "fuzzy" | "remember",
  score: number,
  readingAudioUrl: string
) => {
  try {
    // 确保currentQuestion和currentWord存在
    if (!currentQuestion.value || !currentWord.value) {
      console.error("当前问题或单词不存在");
      return;
    }

    // 记录状态
    currentRecognitionStatus.value = status;

    // 创建当前单词的结果
    const result: WordResult = {
      exerciseId: exerciseId.value,
      questionType: currentQuestion.value.questionType,
      wordId: currentWord.value.id,
      word: currentWord.value.word,
      recognitionStatus: status,
      readingScore: score,
      readingAudioUrl: readingAudioUrl,
      index: currentIndex.value,
      spendTime: spendTime.value,
    };

    // 判断是否是最后一个单词
    const isLastWord = currentIndex.value === totalWords.value - 1;

    if (isLastWord) {
      await submitLastWordAndComplete(result);
    } else {
      // 非最后一个单词，异步提交不等待结果
      submitOneWordExercise({
        ...result,
        answer: undefined,
        correct: undefined,
        isFinish: false,
      }).catch((error) => {
        console.error("提交失败:", error);
      });

      // 直接更新到下一个单词
      currentIndex.value++;
      // 不需要手动设置currentQuestion和currentWord，它们会自动计算
      currentRecognitionStatus.value = null; // 重置状态

      // 保存进度
      saveProgress();
    }
  } catch (error) {
    console.error("操作失败:", error);
    uni.showToast({
      title: "操作失败",
      icon: "none",
    });
  }
};

// 处理前一个单词
const handlePrevWord = () => {
  // 判断是否已经是第一个单词
  if (currentIndex.value > 0) {
    // 更新到前一个单词
    currentIndex.value--;
    // 重置状态
    currentRecognitionStatus.value = null;
    // 保存进度
    saveProgress();
  } else {
    uni.showToast({
      title: "已经是第一个单词",
      icon: "none",
    });
  }
};

// 处理下一个单词
const handleNextWord = () => {
  // 判断是否已经是最后一个单词
  if (currentIndex.value < totalWords.value - 1) {
    // 更新到下一个单词
    currentIndex.value++;
    // 重置状态
    currentRecognitionStatus.value = null;
    // 保存进度
    saveProgress();
  } else {
    uni.showToast({
      title: "已经是最后一个单词",
      icon: "none",
    });
  }
};

// 获取页面参数
onLoad((options: any) => {
  console.log("页面参数:", options);
  if (!options.id) {
    uni.showToast({
      title: "练习ID不能为空",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
    return;
  }

  if (!options.homeworkId) {
    uni.showToast({
      title: "作业ID不能为空",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
    return;
  }

  exerciseId.value = Number(options.id);
  homeworkId.value = Number(options.homeworkId);

  // 获取练习详情
  fetchExerciseDetail();
});

// 返回上一页
const handleBack = () => {
  stopTimer(); // 停止计时
  uni.navigateBack();
};

// 页面卸载时清理
onUnmounted(() => {
  stopTimer();
});
</script>

<style lang="scss">
.word-review {
  // position: relative;
  // width: 100%;
  // height: 100%;
}
.question-container {
  width: 100%;
  height: 100%;
  // flex: 1;
  // min-height: 0;
  position: relative;
  // left: 0;
  // top: 0;
  // display: flex;
  // overflow: hidden;
  // padding: 20rpx;

  .word-recognitionBox {
    width: 100%;
    height: 100%;
  }
}
.card-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  // flex: 1 1 auto;

  .cardItem {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // border-radius: 32rpx;
    // box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06),
    //   0 12rpx 32rpx rgba(0, 0, 0, 0.04), 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(100%) scale(0.95);
    opacity: 0;
    transform: translateX(100%) scale(0.95);

    .itemSlot {
      width: 100%;
      height: 100%;
    }

    &.active {
      opacity: 1;
      transform: translateX(0) scale(1);
      z-index: 2;
    }
    &.prev {
      opacity: 0;
      transform: translateX(-100%) scale(0.95);
    }
  }
}

.left-txt {
  font-size: 16px;
  color: #333;
  padding: 3px 15px;
  border: 1px solid #333;
  border-radius: 25px;
}
</style>
