<template>
  <commonBox
    :loading="loading"
    title="练习结果"
    :use-default-back="false"
    @back="handleComplete"
  >
    <template v-if="!loading">
      <view class="result-container">
        <!-- 表情图标展示 -->
        <view class="emotion-section">
          <image
            :src="emotionImageSrc"
            class="emotion-image"
            @tap="playResultAudio"
          ></image>
        </view>

        <!-- 统计数据 -->
        <view class="stats-section">
          <view class="stats-header">
            <text class="header-item">练习单词</text>
            <text class="header-item">错误数量</text>
            <text class="header-item">练习时长</text>
          </view>
          <view class="stats-content">
            <view class="stats-item">
              <text class="stats-value">{{ totalWords }}个</text>
            </view>
            <view class="stats-item">
              <text class="stats-value">{{ incorrectWords }}个</text>
            </view>
            <view class="stats-item">
              <text class="stats-value">{{ participatedTime }}</text>
            </view>
          </view>
        </view>

        <!-- 易错词回顾标题 -->
        <view class="review-title">本次练习回顾</view>

        <!-- 单词列表 -->
        <view class="word-list">
          <view
            class="word-item"
            v-for="(word, index) in sortedWords"
            :key="index"
          >
            <view class="word-info">
              <text class="word-text">{{ word.text }}</text>
              <text class="word-type" v-if="word.type">{{ word.type }}</text>
              <text class="word-meaning" v-if="word.meaning">{{
                word.meaning
              }}</text>
              <view class="sound-btn" @tap="playAudio(word, index)">
                <text
                  class="iconfont icon-sound"
                  :class="{
                    playing: isAudioPlaying && playingWordId === index,
                  }"
                ></text>
              </view>
            </view>
            <view class="word-status">
              <view class="status-icons">
                <text
                  class="iconfont"
                  :class="{
                    'icon-icon-check-true-right-circle-filled': word.correct,
                    'icon-icon-check-true-right-circle': !word.correct,
                  }"
                ></text>
                <text
                  class="iconfont"
                  :class="{
                    'icon-icon-close-false-circle': word.correct,
                    'icon-icon-close-false-circle-filled': !word.correct,
                  }"
                ></text>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="bottom-actions">
          <button class="retake-btn" @tap="handleRetake">重新开始</button>
          <button class="check-image-btn" @tap="handleComplete">完成</button>
        </view>
      </view>
    </template>
  </commonBox>
</template>

<script lang="ts" setup>
import { getHomeworkExerciseDetail } from "@/api/homework-exercise";
import commonBox from "@/components/common-box/commonBox.vue";
import type { HomeworkExercise } from "@/types/homework-exercise";
import type { HomeworkExerciseSubmissionDetail } from "@/types/homework-submission";
import { computed, onUnmounted, ref } from "vue";

// 导入音频工具
import { AudioPlayer } from "@/sub-exercise/utils/audio-player";

// @ts-ignore
import { onLoad } from "@dcloudio/uni-app";

// 页面参数
const exerciseId = ref<number>(0);
const homeworkId = ref<string>("");
const loading = ref(true);
const fromExercise = ref<boolean>(false);

interface Question {
  questionType: number;
  words: {
    id: number;
    word: string;
    phonetic: string | null;
    partOfSpeech: string;
    syllable: string | null;
    definition: string | null;
    chineseMeaning: string;
    audioUrl: string;
  };
}

// 练习结果数据
const exerciseDetail = ref<HomeworkExerciseSubmissionDetail | null>(null);
// 练习详情数据
const exerciseData = ref<HomeworkExercise | null>(null);

// 计算属性
const score = computed(() => {
  if (!exerciseDetail.value?.wordEvaluationResult?.results) return 0;
  const results = exerciseDetail.value.wordEvaluationResult.results;
  const totalScore = results.reduce((acc, curr) => {
    return acc + (curr.correct ? 100 : 0);
  }, 0);
  // 计算百分比得分
  return Math.round(totalScore / results.length);
});

const totalWords = computed(
  () => exerciseDetail.value?.wordEvaluationResult?.results?.length || 0
);

const correctWords = computed(
  () =>
    exerciseDetail.value?.wordEvaluationResult?.results?.filter(
      (w) => w.correct
    ).length || 0
);

const incorrectWords = computed(() => totalWords.value - correctWords.value);

// 将时间戳转换为分钟
const participatedTime = computed(() => {
  const spentTime = exerciseDetail.value?.wordEvaluationResult?.spentTime;
  if (!spentTime) return "0分0秒";
  return `${Math.floor(spentTime / 60)}分${spentTime % 60}秒`;
});

// 排序后的单词列表
const sortedWords = computed(() => {
  if (!exerciseDetail.value?.wordEvaluationResult?.results) return [];

  const wordMap = new Map();
  // 先从 extraWordData 获取单词详细信息
  if (exerciseData.value?.extraWordData?.questions) {
    exerciseData.value.extraWordData.questions.forEach((q: Question) => {
      wordMap.set(q.words.id, q.words);
    });
  }

  return exerciseDetail.value.wordEvaluationResult.results.map((result) => {
    const wordInfo = wordMap.get(result.wordId);
    return {
      text: result.word,
      type: wordInfo?.partOfSpeech || "",
      meaning: wordInfo?.chineseMeaning || "",
      correct: result.correct,
      audioUrl: wordInfo?.audioUrl || "",
    };
  });
});

// 音频播放相关
const isAudioPlaying = ref(false);
const playingWordId = ref<number | null>(null);
// 创建音频播放器实例
const audioPlayer = new AudioPlayer();

// 计算表情图片路径和对应的音频
const emotionData = computed(() => {
  // 防止除以零
  if (totalWords.value === 0) {
    return {
      imageSrc: "/static/icons/nice.png",
      audioSrc: "/static/sounds/nice.mp3",
    };
  }

  const correctRate = correctWords.value / totalWords.value;
  if (correctRate === 1) {
    // 100% 正确率
    return {
      imageSrc: "/static/icons/excellent.png",
      audioSrc: "/static/sounds/excellent2.mp3",
    };
  } else if (correctRate >= 0.5) {
    // 50-90% 正确率
    return {
      imageSrc: "/static/icons/nice.png",
      audioSrc: "/static/sounds/nice.mp3",
    };
  } else {
    // 低于50% 正确率
    return {
      imageSrc: "/static/icons/keep-it-up.png",
      audioSrc: "/static/sounds/keep-it-up.mp3",
    };
  }
});

// 简化访问
const emotionImageSrc = computed(() => emotionData.value.imageSrc);
const emotionAudioSrc = computed(() => emotionData.value.audioSrc);

// 结果音频播放器
const resultAudioPlayer = new AudioPlayer();

// 播放结果音频
const playResultAudio = () => {
  if (emotionAudioSrc.value) {
    resultAudioPlayer.play(emotionAudioSrc.value, {
      onStart: () => {
        console.log("结果音频开始播放");
      },
      onEnd: () => {
        console.log("结果音频播放结束");
      },
      onError: () => {
        console.error("结果音频播放失败");
      },
    });
  }
};

// 获取练习详情
const fetchExerciseDetail = async () => {
  try {
    loading.value = true;
    const res = await getHomeworkExerciseDetail(exerciseId.value);
    if (res.data) {
      exerciseData.value = res.data;
      if (res.data.extSubmissionDetail) {
        exerciseDetail.value = res.data.extSubmissionDetail;
        // 数据加载完成后播放音频
        setTimeout(() => {
          playResultAudio();
        }, 500); // 延迟500ms播放，确保页面渲染完成
      }
    } else {
      uni.showToast({
        title: "未找到练习结果",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("获取练习详情失败:", error);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
    });
  } finally {
    loading.value = false;
  }
};

// 修改音频播放函数
const playAudio = (word: any, index: number) => {
  if (!word.audioUrl) {
    uni.showToast({
      title: "音频不存在",
      icon: "none",
    });
    return;
  }

  // 如果当前有音频在播放，且点击的是同一个音频，则停止播放
  if (playingWordId.value === index && isAudioPlaying.value) {
    audioPlayer.stop();
    isAudioPlaying.value = false;
    playingWordId.value = null;
    return;
  }

  // 播放音频
  isAudioPlaying.value = true;
  playingWordId.value = index;

  audioPlayer.play(word.audioUrl, {
    onStart: () => {
      console.log("音频开始播放");
    },
    onEnd: () => {
      isAudioPlaying.value = false;
      playingWordId.value = null;
    },
    onError: () => {
      isAudioPlaying.value = false;
      playingWordId.value = null;
      uni.showToast({
        title: "音频播放失败",
        icon: "none",
      });
    },
  });
};

// 完成按钮点击处理
const handleComplete = () => {
  // 如果是从练习页完成后跳转的，则显示庆祝弹窗
  if (fromExercise.value) {
    uni.$emit("exerciseCompleted", {
      score: score.value,
      totalWords: totalWords.value,
      correctWords: correctWords.value,
      incorrectWords: incorrectWords.value,
      exerciseType: "word-play",
    });
  } else {
    // 否则，使用不同的事件名称，避免显示庆祝弹窗
    uni.$emit("resultPageComplete", {
      exerciseType: "word-play",
    });
  }
  uni.navigateBack();
};

// 重新开始按钮点击处理
const handleRetake = () => {
  // 直接跳转到练习页面，不使用事件通信
  uni.redirectTo({
    url: `/sub-exercise/word-exercise/word-play?id=${exerciseId.value}&homeworkId=${homeworkId.value}`,
    fail: (err) => {
      console.error("跳转失败:", err);
      uni.showToast({
        title: "跳转失败，请重试",
        icon: "none",
      });
    },
  });
};

// 获取页面参数并加载数据
onLoad((options: any) => {
  if (!options.id) {
    uni.showToast({
      title: "练习ID不能为空",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
    return;
  }

  exerciseId.value = Number(options.id);
  homeworkId.value = options.homeworkId || "";
  fromExercise.value = options.fromExercise === "true";
  fetchExerciseDetail();
});

// 组件卸载时清理音频
onUnmounted(() => {
  audioPlayer.dispose();
  resultAudioPlayer.dispose(); // 清理结果音频播放器
});
</script>

<style lang="scss" scoped>
.result-container {
  padding: 32rpx;
  padding-bottom: 120rpx;

  .emotion-section {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40rpx 0;

    .emotion-image {
      width: 300rpx;
      height: 300rpx;
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .stats-section {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 1);
    overflow: hidden;
    box-shadow: 0.1px 0.1px 3px 1.5px rgba(0, 0, 0, 0.1);
    opacity: 1;
    border-radius: 10px;

    .stats-header {
      display: flex;
      background-color: #f0f0f0;
      width: 100%;

      .header-item {
        flex: 1;
        text-align: center;
        padding: 10px 0;
        font-size: 28rpx;
        color: rgba(105, 105, 105, 1);
      }
    }

    .stats-content {
      display: flex;
      width: 100%;

      .stats-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 24rpx 0;
        position: relative;

        .stats-value {
          font-size: 36rpx;
          color: rgba(0, 0, 0, 1);
        }
      }
    }
  }

  .review-title {
    font-size: 36rpx;
    color: #333333;
    font-weight: 600;
    margin: 48rpx 0 24rpx;
    text-align: center;
    width: 100%;
  }

  .word-list {
    background: rgba(245, 245, 245, 1);
    border-radius: 24rpx;
    padding: 0 32rpx;
    box-shadow: 0.1px 0.1px 3px 1.5px rgba(0, 0, 0, 0.1);
    margin-bottom: 88rpx;

    .word-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 2rpx solid rgba(229, 229, 229, 1);

      &:last-child {
        border-bottom: none;
      }

      .word-info {
        display: flex;
        align-items: center;
        gap: 24rpx;
        flex: 1;
        overflow: hidden;

        .word-text {
          font-size: 32rpx;
          color: #333333;
          font-weight: 500;
          white-space: nowrap;
        }

        .word-type {
          font-size: 28rpx;
          color: #999999;
          white-space: nowrap;
        }

        .word-meaning {
          font-size: 32rpx;
          color: #666666;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .sound-btn {
          width: 25px;
          height: 25px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 2px;
          margin-left: 2px;
          cursor: pointer;

          .iconfont {
            font-size: 40rpx;
            color: #d0761c;
            opacity: 0.99;
            transition: all 0.3s ease;
          }

          .playing {
            animation: soundWave 1s ease-in-out infinite;
          }
        }
      }

      .word-status {
        .status-icons {
          display: flex;
          align-items: center;
          gap: 10rpx;

          .iconfont {
            font-size: 40rpx;
            transition: all 0.2s ease;
          }

          .icon-icon-check-true-right-circle-filled {
            color: #4caf50;
          }

          .icon-icon-check-true-right-circle {
            color: #b6b6b6;
          }

          .icon-icon-close-false-circle-filled {
            color: #f44336;
          }

          .icon-icon-close-false-circle {
            color: #b6b6b6;
          }
        }
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 40rpx 32rpx;
  gap: 20rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #eceef2;

  button {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .check-image-btn {
    background-color: #ffe251;
    color: #333333;
    font-weight: 500;
  }

  .retake-btn {
    background-color: #f5f5f5;
    color: #666666;
  }
}

@keyframes soundWave {
  0% {
    opacity: 0.99;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.99;
    transform: scale(1);
  }
}
</style>
