是的，无敌大帅哥

# 单词练习任务后台数据表设计方案（扩展版）

本文档描述了单词练习任务的后台数据表设计方案。为使单词功能更加通用，支持多个业务关联，本文在原有设计的基础上进行了扩展：

1. 将单词部分的公共内容抽取到一个公共表中。
2. 修改原有单词练习项表，通过外键关联公共单词表，实现学生作业练习与单词公共库关联，同时便于后续其他业务扩展。

---

## 1. 公共单词表：word_common

该表存储所有单词的公共信息，供多个业务模块共享。

字段说明：

- **id**: 主键，自增。
- **word**: 单词文本。
- **meaning**: 单词释义/解释（可选）。
- **image_url**: 单词相关图片（用于看图识字）。
- **audio_url**: 单词标准发音（用于跟读比对）。
- **created_at**: 记录创建时间。
- **updated_at**: 记录更新时间。

```sql
CREATE TABLE word_common (
    id INT AUTO_INCREMENT PRIMARY KEY,
    word VARCHAR(100) NOT NULL,
    meaning VARCHAR(255),
    image_url VARCHAR(255),
    audio_url VARCHAR(255),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 2. 任务关联表：word_practice_task

该表用于记录每个单词练习任务的基本信息，通常与作业任务表（如 homework 或 homework_exercise）关联。

字段说明：

- **id**: 主键，自增。
- **homework_id**: 关联作业的 ID。
- **day_number**: 任务所属天数或周期编号（如 Day1, Day2）。
- **task_title**: 任务标题或描述。
- **created_at**: 记录创建时间。
- **updated_at**: 记录更新时间。

```sql
CREATE TABLE word_practice_task (
    id INT AUTO_INCREMENT PRIMARY KEY,
    homework_id INT NOT NULL,
    day_number INT NOT NULL,
    task_title VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (homework_id) REFERENCES homework(id)
);
```

## 3. 单词练习项表：word_practice_item

为实现单词功能的通用性，该表不再直接存储单词的基本信息，而是通过外键关联至公共单词表，同时记录当前业务（如每日作业练习）中特有的信息，如练习类型和答案设置。

字段说明：

- **id**: 主键，自增。
- **task_id**: 外键，关联到单词练习任务表 (word_practice_task)。
- **word_common_id**: 外键，关联公共单词表 (word_common) 中的单词。
- **options**: 多选题选项（JSON 格式存储，例如 '["选项 A", "选项 B", "选项 C", "选项 D"]'），仅针对选择题。
- **correct_option**: 正确选项（针对选择题）。
- **correct_answer**: 正确答案（用于默写题判断）。
- **practice_type**: 练习类型，枚举值，例如：
  - 'recognition' —— 看图并标记认识/不认识
  - 'reading' —— 跟读
  - 'multiple_choice' —— 选择题
  - 'dictation' —— 默写
- **created_at**: 创建时间。
- **updated_at**: 更新时间。

```sql
CREATE TABLE word_practice_item (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    word_common_id INT NOT NULL,
    options JSON,
    correct_option VARCHAR(50),
    correct_answer VARCHAR(100),
    practice_type ENUM('recognition', 'reading', 'multiple_choice', 'dictation') NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES word_practice_task(id),
    FOREIGN KEY (word_common_id) REFERENCES word_common(id)
);
```

## 4. 学生练习记录表：word_practice_response

该表用于记录学生针对每个单词练习项（关联公共单词）的答题情况和练习状态。

字段说明：

- **id**: 主键，自增。
- **student_id**: 学生的 ID。
- **practice_item_id**: 外键，关联到单词练习项表 (word_practice_item)。
- **answer**: 学生的回答内容（针对跟读、选择题或默写）。
- **is_correct**: 是否回答正确（布尔值，0 或 1）。
- **response_time**: 提交答案的时间。
- **feedback**: 老师或系统的反馈信息。

```sql
CREATE TABLE word_practice_response (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    practice_item_id INT NOT NULL,
    answer VARCHAR(255),
    is_correct TINYINT(1) DEFAULT 0,
    response_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    feedback VARCHAR(255),
    FOREIGN KEY (practice_item_id) REFERENCES word_practice_item(id)
);
```

## 5. 备注说明

- 通过将单词公共信息抽取到 `word_common` 表中，可实现单词数据的共享和复用，降低冗余，并支持各业务模块关联使用。
- `word_practice_item` 表记录的是针对具体业务（如每日作业练习）中单词的练习配置信息，与公共单词表关联后，可灵活扩展到其它使用场景。
- 各表之间使用外键约束确保数据完整性，其中 `word_practice_task` 表中的 `homework_id` 需关联现有作业表，具体外键可根据项目实际情况调整。
- 建议在业务层定义详细的枚举类型，以确保 `practice_type` 数据一致性和正确处理。

以上设计方案既满足单词练习功能的通用需求，又能与学生每日作业练习业务关联，便于后续扩展到其它业务场景。
